stages:
  - deploy

deploy:dev:
  stage: deploy
  only:
    refs:
      - development
  script:
    - >
      ansible-playbook -l marc-do-dev-sw-sw deploy/deploy.dev.yml
      -e COMMIT_SHA=$CI_COMMIT_SHA
      -e APP_ENV=development
      -e INSTANCES=$INSTANCES
      -e PORT=$PORT
      -e DATABASE_URL=$DEV_DATABASE_URL
      -e REDIS_HOST=$DEV_REDIS_HOST
      -e REDIS_PORT=$DEV_REDIS_PORT
      -e SENTRY_DSN=$SENTRY_DSN
      -e SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN
      -e USE_SEARCH_SERVICES=$DEV_USE_SEARCH_SERVICES
      -e SESSION_COOKIE_KEY=$SESSION_COOKIE_KEY
      -e SESSION_PREFIX=$DEV_SESSION_PREFIX
      -e SESSION_SECRET=$DEV_SESSION_SECRET
      -e DEBUG_MODE=$DEBUG_MODE
    - >
      ansible-playbook -l marc-aws-sw-dev deploy/deploy.dev.yml
      -e COMMIT_SHA=$CI_COMMIT_SHA
      -e APP_ENV=development
      -e INSTANCES=$INSTANCES
      -e PORT=$PORT
      -e DATABASE_URL=$DEV_DATABASE_URL
      -e REDIS_HOST=$DEV_REDIS_HOST
      -e REDIS_PORT=$DEV_REDIS_PORT
      -e SENTRY_DSN=$SENTRY_DSN
      -e SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN
      -e USE_SEARCH_SERVICES=$DEV_USE_SEARCH_SERVICES
      -e SESSION_COOKIE_KEY=$SESSION_COOKIE_KEY
      -e SESSION_PREFIX=$DEV_SESSION_PREFIX
      -e SESSION_SECRET=$DEV_SESSION_SECRET
      -e DEBUG_MODE=$DEBUG_MODE

deploy:prod:
  stage: deploy
  only:
    refs:
      - master
  script:
    - >
      ansible-playbook -l marc-aws-sw deploy/deploy.prod.yml
      -e COMMIT_SHA=$CI_COMMIT_SHA
      -e APP_ENV=production
      -e INSTANCES=$INSTANCES
      -e PORT=$PROD_PORT
      -e DATABASE_URL=$PROD_DATABASE_URL
      -e REDIS_HOST=$PROD_REDIS_HOST
      -e REDIS_PORT=$PROD_REDIS_PORT
      -e SENTRY_DSN=$SENTRY_DSN
      -e SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN
      -e USE_SEARCH_SERVICES=$PROD_USE_SEARCH_SERVICES
      -e SESSION_COOKIE_KEY=$SESSION_COOKIE_KEY
      -e SESSION_PREFIX=$PROD_SESSION_PREFIX
      -e SESSION_SECRET=$PROD_SESSION_SECRET
      -e DEBUG_MODE=false
