generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model admission_refund_request {
  id                Int                    @default(autoincrement())
  created           DateTime               @default(now()) @db.Timestamp(6)
  modified          DateTime               @default(now()) @db.Timestamp(6)
  initiator_user_id Int
  purchase_id       Int                    @unique(map: "unique_refunded_purchase_id")
  reason            String?
  notes             String?
  request_status    refund_request_status? @default(pending)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model app_logs {
  id       String   @unique(map: "logs_id_key") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  moment   DateTime @default(now()) @db.Timestamp(6)
  app_name String   @db.VarChar(255)
  event_id Int
  message  String   @db.VarChar

  @@index([app_name])
  @@index([event_id])
  @@index([moment])
}

model balance_transaction_account {
  id                    String    @unique
  amount                BigInt
  available_on          BigInt?
  created               BigInt
  currency              String    @db.VarChar(3)
  description           String?
  fee                   BigInt?
  net                   BigInt?
  status                String    @db.VarChar(100)
  type                  String    @db.VarChar(100)
  source_id             String?
  automatic_transfer_id String?
  created_at            DateTime? @default(now()) @db.Timestamp(6)
  modified_at           DateTime? @db.Timestamp(6)
  account_id            String    @db.VarChar(100)
  source_data           Json?     @default("{}")

  @@index([automatic_transfer_id], map: "balance_transaction_account_automatic_transfer_id_index")
  @@index([source_id], map: "balance_transaction_account_source_id_index")
}

model banned_email {
  banned_email_id Int      @unique(map: "unique_banned_email_id") @default(autoincrement())
  created         DateTime @default(now()) @db.Timestamp(6)
  modified        DateTime @default(now()) @db.Timestamp(6)
  reason          String?
  email           String   @unique(map: "unique_email")
  history         Json     @default("[]") @db.Json
  unlocked        Boolean? @default(false)
  event_id        Int?
  purchase_id     Int?

  @@index([purchase_id, event_id], map: "index_banned_email")
}

model banned_fingerprint {
  banned_fingerprint_id Int      @unique(map: "unique_banned_fingerprint_id") @default(autoincrement())
  created               DateTime @default(now()) @db.Timestamp(6)
  modified              DateTime @default(now()) @db.Timestamp(6)
  reason                String?
  fingerprint           String   @unique(map: "unique_fingerprint")
  history               Json     @default("[]") @db.Json
  unlocked              Boolean? @default(false)
  event_id              Int?
  purchase_id           Int?

  @@index([purchase_id, event_id], map: "index_banned_fingerprint")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model bracketmatch {
  bracketmatch_id     Int      @unique(map: "unique_bracketmatch_id") @default(autoincrement())
  created             DateTime @default(now()) @db.Timestamp(6)
  display_name        String?  @db.VarChar(50)
  division_id         Int?
  division_short_name String?  @db.VarChar(50)
  modified            DateTime @default(now()) @db.Timestamp(6)
  name                String?  @db.VarChar(50)
  uuid                String?  @db.Uuid
  short_name          String?  @db.VarChar(50)
  match_number        Int?
  team1_roster_id     Int?
  team2_roster_id     Int?
  event_id            Int?
  winning_roster_id   Int?
  score               String?  @db.VarChar(100)
}

model bracketteams {
  uuid                String  @id @unique(map: "unique_uuid") @db.Uuid
  m1t1                String? @db.VarChar(100)
  m1t2                String? @db.VarChar(100)
  m2t1                String? @db.VarChar(100)
  m2t2                String? @db.VarChar(100)
  m2ref               String? @db.VarChar(100)
  m3t1                String? @db.VarChar(100)
  m3t2                String? @db.VarChar(100)
  m4t1                String? @db.VarChar(100)
  m4t2                String? @db.VarChar(100)
  m4ref               String? @db.VarChar(100)
  m5t1                String? @db.VarChar(100)
  m5t2                String? @db.VarChar(100)
  m5ref               String? @db.VarChar(100)
  m1ref               String? @db.VarChar(100)
  m3ref               String? @db.VarChar(100)
  division_short_name String? @db.VarChar(100)
  name                String? @db.VarChar(100)
  short_name          String? @db.VarChar(100)
  display_name        String? @db.VarChar(100)
  division_id         Int?
  m6t1                String? @db.VarChar(100)
  m6t2                String? @db.VarChar(100)
  m6ref               String? @db.VarChar(100)
  m7t1                String? @db.VarChar(100)
  m7t2                String? @db.VarChar(100)
  m7ref               String? @db.VarChar(100)
  m8t1                String? @db.VarChar(100)
  m8t2                String? @db.VarChar(100)
  m8ref               String? @db.VarChar(100)
  m9t1                String? @db.VarChar(100)
  m9t2                String? @db.VarChar(100)
  m9ref               String? @db.VarChar(100)
  m10t1               String? @db.VarChar(100)
  m10t2               String? @db.VarChar(100)
  m10ref              String? @db.VarChar(100)
  win                 String? @default("")
  rd                  String? @default("") @map("3rd")
  m11t1               String? @db.VarChar(100)
  m11t2               String? @db.VarChar(100)
  m11ref              String? @db.VarChar(100)
  m12t1               String? @db.VarChar(100)
  m12t2               String? @db.VarChar(100)
  m12ref              String? @db.VarChar(100)
  m13t1               String? @db.VarChar(100)
  m13t2               String? @db.VarChar(100)
  m13ref              String? @db.VarChar(100)
  m14t1               String? @db.VarChar(100)
  m14t2               String? @db.VarChar(100)
  m14ref              String? @db.VarChar(100)
  m15t1               String? @db.VarChar(100)
  m15t2               String? @db.VarChar(100)
  m15ref              String? @db.VarChar(100)
  m16t1               String? @db.VarChar(100)
  m16t2               String? @db.VarChar(100)
  m16ref              String? @db.VarChar(100)
  m17t1               String? @db.VarChar(100)
  m17t2               String? @db.VarChar(100)
  m17ref              String? @db.VarChar(100)
  m18t1               String? @db.VarChar(100)
  m18t2               String? @db.VarChar(100)
  m18ref              String? @db.VarChar(100)
  m19t1               String? @db.VarChar(100)
  m19t2               String? @db.VarChar(100)
  m19ref              String? @db.VarChar(100)
  m20t1               String? @db.VarChar(100)
  m20t2               String? @db.VarChar(100)
  m20ref              String? @db.VarChar(100)
  m21t1               String? @db.VarChar(100)
  m21t2               String? @db.VarChar(100)
  m21ref              String? @db.VarChar(100)
  m22t1               String? @db.VarChar(100)
  m22t2               String? @db.VarChar(100)
  m22ref              String? @db.VarChar(100)
  event_id            Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model club_owner {
  club_owner_id  Int      @id @default(autoincrement())
  created        DateTime @default(now()) @db.Timestamp(6)
  modified       DateTime @default(now()) @db.Timestamp(6)
  user_id        Int      @unique(map: "club_owner_user_id_uindex")
  active         Boolean  @default(true)
  master_club_id Int?

  @@index([club_owner_id], map: "index_club_owner_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model common_item {
  common_item_id String
  title          String
  item_type      String
  details        Json?     @default("{}")
  created        DateTime? @default(now()) @db.Timestamp(6)
  modified       DateTime? @default(now()) @db.Timestamp(6)

  @@id([item_type, common_item_id])
  @@index([item_type], map: "common_item_type_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model country {
  created       DateTime @default(now()) @db.Timestamp(6)
  modified      DateTime @default(now()) @db.Timestamp(6)
  code          String   @id(map: "unique_country_id") @db.VarChar(2)
  name          String   @db.VarChar(100)
  has_states    Boolean  @default(false)
  has_provinces Boolean  @default(false)
  calling_code  Int?

  @@index([code], map: "index_code")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model courts {
  uuid          String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  sort_priority Int?      @default(0)
  name          String    @db.VarChar
  numeric       Int?      @default(0)
  champdesk     String?   @db.VarChar
  modified      DateTime? @db.Timestamp(6)
  is_dirty      Int?      @default(0)
  event_id      Int?
  short_name    String?
  swb_settings  Json?

  @@index([event_id], map: "courts_event_id_index")
  @@index([sort_priority])
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model custom_payment {
  custom_payment_id        Int                          @default(autoincrement())
  created                  DateTime                     @default(now()) @db.Timestamptz(6)
  modified                 DateTime?                    @db.Timestamptz(6)
  event_id                 Int
  status                   custom_payment_status
  payment_for              custom_payment_for
  payment_for_type         custom_payment_for_type
  amount                   Decimal                      @db.Decimal(8, 2)
  net_profit               Decimal                      @db.Decimal(8, 2)
  merchant_fee             Decimal                      @db.Decimal(8, 2)
  stripe_payment_intent_id Int
  description              String?
  session_mode             custom_payment_session_mode? @default(default)

  @@index([event_id], map: "custom_payment_event_id_index")
  @@index([stripe_payment_intent_id], map: "custom_payment_stripe_custom_payment_id_index")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model custom_payment_item {
  custom_payment_item_id Int       @id @default(autoincrement())
  created                DateTime  @default(now()) @db.Timestamp(6)
  modified               DateTime? @db.Timestamp(6)
  title                  String
  description            String?
  price                  Decimal?  @db.Decimal(10, 2)
  visible                Boolean   @default(true)
  payment_page           String?   @default("coalition")
  sort_order             Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model custom_payment_uncollected_fee_balance_info {
  custom_payment_uncollected_fee_balance_info_id Int       @id @default(autoincrement())
  custom_payment_id                              Int
  created                                        DateTime  @default(now()) @db.Timestamptz(6)
  modified                                       DateTime? @db.Timestamptz(6)
  sw_fee                                         Decimal   @db.Decimal(8, 2)
  balance_details                                Json
  current_balance_sum                            Decimal   @db.Decimal(8, 2)
  new_balance_sum                                Decimal   @db.Decimal(8, 2)

  @@index([custom_payment_id], map: "custom_payment_fee_balance_info_custom_payment_id_index")
}

model custom_recipient {
  custom_recipient_id       Int       @id(map: "custom_recipient_pk") @default(autoincrement())
  custom_recipients_list_id Int
  first                     String?
  last                      String?
  email                     String
  document_row              Json
  created                   DateTime? @default(now()) @db.Timestamp(6)
  modified                  DateTime? @db.Timestamp(6)

  @@unique([email, custom_recipients_list_id])
}

model custom_recipients_list {
  custom_recipients_list_id Int       @id(map: "custom_recipients_list_pk") @default(autoincrement())
  title                     String
  document_path             String
  event_id                  Int?
  event_owner_id            Int?
  user_id                   Int
  created                   DateTime? @default(now()) @db.Timestamp(6)
  modified                  DateTime? @db.Timestamp(6)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model dispute_evidence {
  id                Int       @id @default(autoincrement())
  created_at        DateTime? @default(now()) @db.Timestamptz(6)
  modified_at       DateTime? @default(now()) @db.Timestamptz(6)
  stripe_dispute_id String    @unique(map: "dispute_evidence_stripe_dispute_id_unique") @db.VarChar(255)
  due_by            BigInt
  past_due          Boolean
  has_evidence      Boolean
  submission_count  Int
  data              Json
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model division {
  event_id                    Int
  name                        String      @db.VarChar(120)
  short_name                  String?     @db.VarChar(25)
  created                     DateTime    @default(now()) @db.Timestamp(6)
  modified                    DateTime    @default(now()) @db.Timestamp(6)
  max_teams                   Int         @default(40)
  division_id                 Int         @id(map: "event_pkey") @unique @default(autoincrement())
  max_auto_enter              Int?        @default(0)
  max_waiting                 Int?        @default(0)
  locked                      Boolean     @default(false)
  gender                      team_gender
  reg_fee                     Decimal?    @db.Decimal
  late_reg_penalty            Float?      @db.Real
  max_age                     Int?
  level                       String?     @db.VarChar(100)
  published                   Boolean     @default(true)
  closed                      DateTime?   @db.Timestamp(6)
  division_seeds              String?     @db.VarChar
  division_finishes           String?     @db.VarChar
  sort_priority               Int         @default(0)
  is_dirty                    Int         @default(0)
  color                       String?
  roster_deadline             DateTime?   @db.Timestamp(6)
  officials_required_override Int         @default(0)
  date_reg_close              DateTime?   @db.Timestamp(6)
  tie_breaker_type            Int         @default(0)
  athletes_maxcount           Int?
  level_sort_order            Int?
  sort_order                  Int?
  swb_settings                Json?
  has_flow_chart              Boolean?    @default(false)
  is_qualifying               Boolean     @default(false)
  seasonality                 String?     @default("full")
  credit_surcharge            Float?      @db.Real
  athletes_maxcount_checkin   Int?

  @@index([name])
  @@index([short_name])
  @@index([event_id], map: "division_tournament_id_idx")
  @@index([division_id], map: "index_division_id2")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model division_standing {
  uuid         String @id @unique @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  event_id     Int    @default(0)
  division_id  Int    @default(0)
  team_id      Int    @default(0)
  matches_won  Int    @default(0)
  matches_lost Int    @default(0)
  sets_won     Int    @default(0)
  sets_lost    Int    @default(0)
  points_won   Int    @default(0)
  points_lost  Int    @default(0)
  matches_pct  Float  @default(0) @db.Real
  sets_pct     Float  @default(0) @db.Real
  points_ratio Float  @default(0) @db.Real
  rank         Int    @default(0)
  info         Json?
  swb_settings Json?

  @@index([division_id, team_id, event_id], map: "division_standing_division_id_team_id_event_id_index")
}

model email_sending_history {
  email_sending_history_id Int       @id @default(autoincrement())
  email_address            String
  email_sending_id         String
  created                  DateTime? @default(now()) @db.Timestamp(6)
  modified                 DateTime? @default(now()) @db.Timestamp(6)

  @@index([email_address], map: "email_sending_history_email_address_index")
  @@index([email_sending_id], map: "email_sending_history_email_sending_id_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model email_template {
  created              DateTime  @default(now()) @db.Timestamp(6)
  email_html           String?
  email_subject        String?
  email_template_id    Int       @id @default(autoincrement())
  email_text           String?
  event_owner_id       Int?
  modified             DateTime  @default(now()) @db.Timestamp(6)
  recipient_type       String?   @db.VarChar(20)
  sender_type          String?   @db.VarChar(20)
  title                String
  event_id             Int?
  bee_json             Json?
  img_name             String?
  email_template_type  String?
  is_valid             Boolean?
  email_template_group String?
  published            Boolean?  @default(false)
  deleted              DateTime? @db.Timestamptz(6)
  grapes_json          Json?

  @@index([email_template_type], map: "index_email_template_type")
  @@index([event_id], map: "index_event_id9")
  @@index([event_owner_id], map: "index_event_owner_id1")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model email_template_group {
  created            DateTime? @default(now()) @db.Timestamp(6)
  modified           DateTime? @default(now()) @db.Timestamp(6)
  group              String    @unique(map: "unique_group")
  title              String
  description        String
  variables          Json?     @default("[]")
  usage_restrictions Json?     @default("{ \"roles\": [\"any\"] }")
  position           Int?      @default(999)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model email_template_type {
  type                      String
  email_template_group      String
  title                     String
  description               String
  long_title                String
  is_trigger                Boolean? @default(false)
  default_email_template_id Int?

  @@unique([type, email_template_group], map: "unique_type")
  @@index([email_template_group], map: "index_group")
  @@index([title], map: "index_title")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model email_unsubscribe_list {
  email_unsubscribe_list_id Int       @unique @default(autoincrement())
  created                   DateTime? @default(now()) @db.Timestamp(6)
  modified                  DateTime? @db.Timestamp(6)
  email                     String    @db.VarChar(100)
  event_id                  Int
  unsubscribe_token_id      Int?
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event {
  event_id                              Int                           @id(map: "tournament_pkey") @default(autoincrement())
  created                               DateTime                      @default(now()) @db.Timestamp(6)
  modified                              DateTime                      @default(now()) @db.Timestamp(6)
  name                                  String                        @db.VarChar(100)
  long_name                             String
  date_start                            DateTime?                     @db.Timestamp(6)
  date_end                              DateTime?                     @db.Timestamp(6)
  event_owner_id                        Int?
  sport_id                              Int
  has_status_housing                    Boolean?
  has_status_roster                     Boolean?
  status                                String?                       @db.VarChar(20)
  sport_sanctioning_id                  Int?
  country                               String?                       @db.VarChar(2)
  city                                  String?                       @db.VarChar(100)
  zip                                   String?                       @db.VarChar(10)
  state                                 String?                       @db.VarChar(2)
  address                               String?
  date_reg_open                         DateTime?                     @db.Timestamp(6)
  late_reg_date                         DateTime?                     @db.Timestamp(6)
  date_reg_close                        DateTime?                     @db.Timestamp(6)
  enter_req_roster                      Boolean?
  accept_req_roster                     Boolean?
  roster_deadline                       DateTime?                     @db.Timestamp(6)
  sport_variation_id                    Int?
  host                                  String?
  location                              String?
  website                               String?                       @db.VarChar(500)
  email                                 String?
  has_male_teams                        Boolean
  has_female_teams                      Boolean
  has_coed_teams                        Boolean
  region                                String?                       @db.VarChar(2)
  late_reg_penalty                      Float?                        @db.Real
  reg_fee                               Float?                        @db.Real
  stripe_statement                      String?                       @db.VarChar(22)
  payment_name                          String?
  payment_address                       String?
  payment_city                          String?
  payment_state                         String?                       @db.VarChar(2)
  payment_zip                           String?                       @db.VarChar(10)
  payment_country                       String?                       @db.VarChar(2)
  mincount_enter                        Int?
  mincount_accept                       Int?
  has_late_reg                          Boolean?                      @default(false)
  housing_company_id                    Int?                          @default(0)
  custom_housing_company                String?
  hosting_org_name                      String?
  hosting_org_address                   String?
  hosting_org_city                      String?
  hosting_org_state                     String?                       @db.VarChar(2)
  hosting_org_zip                       String?                       @db.VarChar(10)
  hosting_org_phone                     String?                       @db.VarChar(100)
  event_notes                           String?
  published                             Boolean                       @default(false)
  credit_surcharge                      Float?                        @db.Real
  housing_nights_required               Int?
  housing_local_teams_distance          Int?
  housing_nights_threshold              Int?
  sales_manager_id                      Int?
  show_teams_entered                    event_teams_visibility?       @default(hide)
  notify_frequency                      event_notify_frequency?       @default(never)
  notify_emails                         String?                       @db.VarChar(500)
  rules_website                         String?                       @db.VarChar(500)
  has_officials                         Boolean?                      @default(false)
  officials_meeting_datetime            String?                       @db.VarChar(100)
  officials_meeting_venue               String?                       @db.VarChar(100)
  official_applied_email_template_id    Int?
  official_accepted_email_template_id   Int?
  official_waitlisted_email_template_id Int?
  official_declined_email_template_id   Int?
  timezone                              String?                       @default(dbgenerated("0")) @db.VarChar
  has_rosters                           Boolean?                      @default(true)
  remote_sync_allowed                   Boolean?                      @default(false)
  schedule_published                    Boolean?
  score_entry_allowed                   Boolean?
  admin_security_pin                    String?
  registration_method                   reg_method?                   @default(club)
  usav_required                         Boolean?                      @default(false)
  has_match_barcodes                    Boolean?                      @default(true)
  tickets_published                     Boolean?                      @default(false)
  tickets_options                       Json?                         @db.Json
  tickets_sw_fee                        Decimal?                      @default(1.1) @db.Decimal
  teams_entry_sw_fee                    Decimal?                      @db.Decimal
  stripe_teams_private_key              String?
  stripe_tickets_private_key            String?
  tickets_purchase_date_start           DateTime?                     @db.Timestamp(6)
  tickets_purchase_date_end             DateTime?                     @db.Timestamp(6)
  tickets_receipt_descr                 String?
  tickets_purchase_passcode             String?                       @db.VarChar(10)
  tickets_refund_passcode               String?                       @db.VarChar(10)
  tickets_visible                       Boolean?                      @default(false)
  event_tickets_code                    String?                       @db.VarChar(20)
  tickets_purchase_additional_fields    Json?
  allow_ticket_sales                    Boolean?                      @default(false)
  live_to_public                        Boolean                       @default(false)
  tickets_description                   String?
  tickets_locations                     Json?                         @db.Json
  date_official_reg_open                DateTime?                     @db.Timestamp(6)
  date_official_reg_close               DateTime?                     @db.Timestamp(6)
  social_links                          Json?                         @db.Json
  officials_required                    Int                           @default(0)
  housing_teams_access_level            ths_teams_access_level?
  allow_teams_registration              Boolean?                      @default(false)
  allow_card_payments                   Boolean?
  public_teams_visibility               event_teams_visibility_level? @default(hidden)
  clubs_teams_visibility                event_teams_visibility_level? @default(hidden)
  ticket_camps_registration             Boolean?                      @default(false)
  tickets_waiver_agreement              String?
  tickets_use_connect                   Boolean?                      @default(false)
  stripe_tickets_percent                Decimal?                      @default(2.9) @db.Decimal
  stripe_tickets_fixed                  Decimal?                      @default(0.3) @db.Decimal
  stripe_tickets_fee_payer              payer_option?                 @default(seller)
  tickets_sw_fee_payer                  payer_option?                 @default(seller)
  tie_breaker_type                      Int                           @default(0)
  tickets_purchase_by_card              Boolean?                      @default(true)
  tickets_purchase_by_check             Boolean?                      @default(false)
  tickets_purchase_by_ach               Boolean?                      @default(false)
  tickets_check_payment_details         String?
  esw_id                                String?
  email_on_ticket_purchase              Boolean?                      @default(false)
  tickets_has_barcodes                  Boolean?                      @default(true)
  is_test                               Boolean                       @default(false)
  online_team_checkin_available         Boolean?                      @default(false)
  online_team_checkin_end               DateTime?                     @db.Timestamp(6)
  online_team_checkin_disclaimer        String?
  maxcount_accept                       Int?
  maxcount_staff_accept                 Int?
  tickets_stripe_statement              String?                       @db.VarChar(22)
  team_members_validation               Json?
  tickets_sw_balance                    Decimal?                      @default(0) @db.Decimal
  tickets_sw_extra_fee                  Decimal?                      @default(0) @db.Decimal
  tickets_sw_target_balance             Decimal?                      @default(0) @db.Decimal
  allow_check_payments                  Boolean?                      @default(true)
  allow_ach_payments                    Boolean?                      @default(false)
  teams_use_connect                     Boolean?                      @default(false)
  plaid_teams_public_key                String?
  plaid_teams_private_key               String?
  ach_surcharge                         Decimal?                      @default(0) @db.Decimal
  stripe_teams_percent                  Decimal?                      @default(2.9) @db.Decimal
  stripe_teams_fixed                    Decimal?                      @default(0.3) @db.Decimal
  ach_teams_percent                     Decimal?                      @default(1.5) @db.Decimal
  ach_teams_max_fee                     Decimal?                      @default(5) @db.Decimal
  plaid_teams_account_id                String?
  teams_sw_extra_fee                    Decimal?                      @db.Decimal
  teams_sw_balance                      Decimal?                      @default(0) @db.Decimal
  teams_sw_target_balance               Decimal?                      @db.Decimal
  require_match_end_time                Boolean                       @default(true)
  official_payment_method               Json?                         @default("{}")
  score_entry_live                      Boolean?                      @default(false)
  enable_officials_reg                  Boolean?                      @default(false)
  deleted                               DateTime?                     @db.Timestamp(6)
  enable_hotel_for_officials            Boolean?                      @default(false)
  officials_hotel_comp                  Json?
  officials_hotel_date_end              DateTime?                     @db.Date
  officials_hotel_date_start            DateTime?                     @db.Date
  season                                Int?
  swb_settings                          Json?
  hide_seeds                            Boolean?
  teams_escrow_collected                Decimal?                      @default(0) @db.Decimal
  tickets_discount                      Json?
  tickets_settings                      Json?
  event_type                            event_type?
  online_team_checkin_mode              online_team_checkin_mode?     @default(default)
  teams_settings                        Json?                         @default("{}")
  teams_use_clubs_module                Boolean?                      @default(false)
  has_staff                             Boolean?                      @default(false)
  enable_staff_reg                      Boolean?                      @default(false)
  date_staff_reg_open                   DateTime?                     @db.Timestamp(6)
  date_staff_reg_close                  DateTime?                     @db.Timestamp(6)
  staff_payment_method                  Json?                         @default("{}")
  enable_hotel_for_staff                Boolean?                      @default(false)
  staff_hotel_date_start                DateTime?                     @db.Date
  staff_hotel_date_end                  DateTime?                     @db.Date
  staff_hotel_comp                      Json?
  coordinates                           Json?                         @default("{}") @db.Json
  club_private_reg_code                 String?                       @unique(map: "event_club_private_reg_code_uindex")
  club_private_reg_active               Boolean?
  entry_region_restriction              String[]
  tickets_lite_pin                      String?
  tickets_buy_pin                       String?
  tickets_admin_pin                     String?
  tickets_sw_fee_internal               Decimal?                      @default(1.0) @db.Decimal
  stripe_sw_percent                     Decimal?                      @default(2.7) @db.Decimal
  show_on_home_page                     Boolean                       @default(true)
  housing_rooming_list_due_date         DateTime?                     @db.Date
  event_kiosk_description               String?
  has_exhibitors                        Boolean                       @default(false)
  enable_exhibitors_reg                 Boolean                       @default(false)
  date_exhibitors_reg_open              DateTime?                     @db.Timestamp(6)
  date_exhibitors_reg_close             DateTime?                     @db.Timestamp(6)
  stripe_exhibitors_private_key         String?                       @db.VarChar(2044)
  exhibitors_stripe_statement           String?                       @db.VarChar(22)
  stripe_exhibitors_percent             Decimal?                      @default(2.9) @db.Decimal
  exhibitors_sw_fee                     Decimal?                      @db.Decimal
  show_number_of_teams_for_public       Boolean?                      @default(true)
  show_number_of_teams_for_cd           Boolean?                      @default(true)
  online_team_checkin_start             DateTime?                     @db.Timestamp(6)
  show_team_entry_status                Boolean                       @default(false)
  extra_fee_collection_mode             extra_fee_collection_mode?    @default(auto)
  showcase_registration                 Boolean                       @default(false)
  official_qr_code_enable               Boolean?                      @default(true)
  use_clinic                            Boolean?                      @default(false)
  official_additional_role_enable       Boolean                       @default(false)

  @@index([esw_id], map: "event_esw_id_index", type: Hash)
  @@index([event_owner_id])
  @@index([housing_company_id])
  @@index([notify_frequency])
  @@index([official_accepted_email_template_id])
  @@index([official_applied_email_template_id])
  @@index([official_declined_email_template_id])
  @@index([official_waitlisted_email_template_id])
  @@index([plaid_teams_account_id])
  @@index([sales_manager_id])
  @@index([sport_sanctioning_id])
  @@index([sport_variation_id])
  @@index([sport_id], map: "index_sport_id1")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_action_clip_streaming {
  id                  Int       @default(autoincrement())
  created             DateTime? @default(now()) @db.Timestamp(6)
  modified            DateTime? @default(now()) @db.Timestamp(6)
  event_id            Int       @unique
  pin_codes_generated Boolean   @default(false)
  pin_codes_sent      Boolean   @default(false)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_booth {
  event_booth_id Int      @id(map: "unique_event_booth_id") @default(autoincrement())
  created        DateTime @default(now()) @db.Timestamp(6)
  modified       DateTime @default(now()) @db.Timestamp(6)
  event_id       Int
  title          String   @db.VarChar(100)
  description    String?
  fee            Decimal  @db.Decimal(8, 2)
  is_enabled     Boolean  @default(false)

  @@index([event_id], map: "index_event_booth_event_id")
  @@index([event_booth_id], map: "index_event_booth_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_camp {
  event_camp_id  Int              @unique(map: "unique_event_camp_id") @default(autoincrement())
  created        DateTime?        @default(now()) @db.Timestamp(6)
  modified       DateTime?        @default(now()) @db.Timestamp(6)
  name           String
  event_id       Int
  types_order    String?
  purchase_start DateTime?        @db.Timestamp(6)
  purchase_end   DateTime?        @db.Timestamp(6)
  age_from       Int?
  age_to         Int?
  date_start     DateTime?        @db.Timestamp(6)
  date_end       DateTime?        @db.Timestamp(6)
  dependency     Json?            @db.Json
  description    String?
  deleted        DateTime?        @db.Timestamp(6)
  sort_order     Decimal?         @db.Decimal
  age_date       DateTime?        @db.Timestamp(6)
  visibility     camp_visibility? @default(published)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_change {
  event_change_id    Int       @unique(map: "unique_event_notifications_id") @default(autoincrement())
  created            DateTime  @default(now()) @db.Timestamp(6)
  modified           DateTime  @default(now()) @db.Timestamp(6)
  event_id           Int
  action             String    @db.VarChar(50)
  roster_club_id     Int?
  roster_team_id     Int?
  division_id        Int?
  purchase_id        Int?
  sent_to_event      DateTime? @db.Timestamp(6)
  comments           String?
  event_owner_id     Int?
  club_director_id   Int?
  sent_to_club       DateTime? @db.Timestamp(6)
  event_email_id     Int?
  sent_to_members    DateTime? @db.Timestamp(6)
  user_id            Int?
  old_housing_status String?
  new_housing_status String?
  published          Boolean?  @default(true)
  system_job_id      Int?
  master_staff_id    Int?

  @@index([event_email_id], map: "event_change_email__id_index")
  @@index([created], map: "index_created2")
  @@index([event_id], map: "index_event_id4")
  @@index([roster_club_id], map: "index_roster_club_id3")
  @@index([roster_team_id], map: "index_roster_team_id4")
}

model event_chat {
  id        String   @unique @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  moment    DateTime @default(now()) @db.Timestamp(6)
  event_id  Int
  user_name String
  user_ip   String   @db.VarChar
  message   String   @db.VarChar

  @@index([event_id])
  @@index([id])
  @@index([moment])
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_clothes {
  event_clothes_id Int       @id @default(autoincrement())
  common_item_id   String
  event_id         Int
  gender           String
  member_type      String
  deleted          DateTime? @db.Timestamp(6)
  created          DateTime? @default(now()) @db.Timestamp(6)
  modified         DateTime? @default(now()) @db.Timestamp(6)

  @@unique([event_id, gender, common_item_id, member_type], map: "event_clothes_unique")
  @@index([gender], map: "index_gender")
  @@index([member_type], map: "index_member_type")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_email {
  event_email_id           Int      @unique(map: "unique_event_email_id") @default(autoincrement())
  created                  DateTime @default(now()) @db.Timestamp(6)
  modified                 DateTime @default(now()) @db.Timestamp(6)
  event_id                 Int
  recipient_type           String?  @db.VarChar(20)
  roster_club_id           Int?
  roster_team_id           Int?
  event_official_id        Int?
  email_from               String?  @db.VarChar(200)
  email_to                 String?  @db.VarChar(200)
  email_subject            String?
  email_bcc                String?  @db.VarChar(200)
  email_text               String?
  email_html               String?
  reason_type              String?  @db.VarChar(50)
  email_cc                 String?  @db.VarChar(200)
  email_template_id        Int?
  tickets_customer_user_id Int?
  master_staff_id          Int?
  purchase_id              Int?
  ticket_coupons           Json?    @default("[]")
  email_id                 Int?

  @@index([email_to], map: "event_email_email_to_index")
  @@index([event_email_id], map: "event_email_id_index")
  @@index([ticket_coupons], map: "event_email_ticket_coupons_gin_index", type: Gin)
  @@index([ticket_coupons(ops: JsonbPathOps)], map: "event_email_ticket_coupons_gin_jsonb_path_ops_index", type: Gin)
  @@index([event_id], map: "index_event_email_event_id")
  @@index([event_official_id], map: "index_event_email_event_official_id")
  @@index([roster_team_id], map: "index_event_email_roster_team_id")
  @@index([email_template_id], map: "index_event_template_id")
}

model event_email_trigger {
  id                   Int       @unique(map: "unique_event_email_trigger_id") @default(autoincrement())
  created              DateTime? @default(now()) @db.Timestamp(6)
  modified             DateTime? @default(now()) @db.Timestamp(6)
  email_template_type  String
  email_template_group String
  email_template_id    Int
  event_id             Int

  @@unique([email_template_type, email_template_group, event_id], map: "unique_group_type_per_event")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_exhibitor {
  event_exhibitor_id Int                     @id @default(autoincrement())
  created            DateTime?               @default(now()) @db.Timestamp(6)
  modified           DateTime?               @default(now()) @db.Timestamp(6)
  status             event_exhibitor_status?
  event_id           Int
  sponsor_id         Int
  is_exhibitor       Boolean?                @default(false)
  is_sponsor         Boolean?                @default(false)
  is_non_profit      Boolean?                @default(false)
  is_other           Boolean?                @default(false)
  event_dates        Json?                   @default("{}")
  booths             Int[]                   @default([])
  comment            String?

  @@unique([event_id, sponsor_id], map: "event_exhibitor_unique")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_journal {
  id          String   @id @unique @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  moment      DateTime @default(dbgenerated("clock_timestamp()")) @db.Timestamp(6)
  event_id    Int
  table_name  String   @db.VarChar(255)
  method      String   @db.VarChar(255)
  created_by  String   @db.VarChar(255)
  data        String   @db.VarChar
  skip_update Boolean  @default(false)

  @@index([event_id])
  @@index([method])
  @@index([moment])
  @@index([skip_update], map: "index_skip_update")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_location {
  event_location_id Int      @id @default(autoincrement())
  created           DateTime @default(now()) @db.Timestamp(6)
  modified          DateTime @default(now()) @db.Timestamp(6)
  event_id          Int
  name              String
  short_name        String   @db.VarChar(8)
  sort_order        Int?
  address           String?
  city              String?
  state             String?  @db.VarChar(2)
  zip               String?  @db.VarChar(7)
  courts_from       Int?
  courts_to         Int?
  number            Int?     @default(1)

  @@unique([event_id, number], map: "event_location_event_id_number_unique")
  @@unique([event_id, name], map: "event_location_name_Unique")
  @@unique([event_id, short_name], map: "event_location_short_name_Unique")
  @@index([event_location_id], map: "index_event_location_id")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model event_media {
  event_media_id Int              @id @default(autoincrement())
  created        DateTime?        @default(now()) @db.Timestamp(6)
  modified       DateTime?        @default(now()) @db.Timestamp(6)
  file_type      event_media_type
  event_id       Int
  division_id    Int?
  file_path      String
  file_name      String
  file_ext       String
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_note {
  event_note_id  Int      @unique(map: "unique_event_notes_id") @default(autoincrement())
  created        DateTime @default(now()) @db.Timestamp(6)
  modified       DateTime @default(now()) @db.Timestamp(6)
  roster_club_id Int
  roster_team_id Int?
  event_id       Int
  event_owner_id Int?
  note           String

  @@index([created], map: "index_created1")
  @@index([roster_club_id], map: "index_roster_club_id2")
  @@index([roster_team_id], map: "index_roster_team_id3")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This table contains exclusion constraints and requires additional setup for migrations. Visit https://pris.ly/d/exclusion-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_official {
  event_official_id                   Int                      @id(map: "unique_event_official_id") @default(autoincrement())
  created                             DateTime                 @default(now()) @db.Timestamp(6)
  modified                            DateTime                 @default(now()) @db.Timestamp(6)
  official_id                         Int
  event_id                            Int
  travel_method                       String?                  @db.VarChar(20)
  able_to_transport_others            Boolean?
  need_hotel_room                     Boolean?
  roommate_preference                 String?
  schedule_availability               Json?                    @db.Json
  departure_time                      String?
  additional_restrictions             String?
  is_staff                            Boolean?
  is_official                         Boolean?
  head_official                       Boolean?
  work_status                         official_work_status?    @default(pending)
  payment_option                      official_payment_option?
  bank_account_number                 String?                  @db.VarChar(50)
  bank_account_routing                String?                  @db.VarChar(50)
  security_pin                        String?                  @db.VarChar(10)
  rating                              event_official_rating?
  deleted                             DateTime?                @db.Timestamp(6)
  schedule_name                       String?
  rank                                official_rank?
  hotel_nights_required               Json?
  is_email_notifications_receiver     Boolean?                 @default(false)
  is_email_contact_provider           Boolean                  @default(false)
  staff_arrival_datetime              DateTime?                @db.Timestamp(6)
  staff_payment_option                official_payment_option?
  staff_deleted                       DateTime?                @db.Timestamp(6)
  staff_work_status                   official_work_status?    @default(pending)
  departure_datetime                  DateTime?                @db.Timestamp(6)
  safesport_manual_ok                 String?
  bg_manual_ok                        String?
  arbiterpay_username                 String?                  @db.VarChar(50)
  arbiterpay_account_number           String?                  @db.VarChar(50)
  is_official_participation_confirmed DateTime?                @db.Timestamp(6)
  use_clinic                          Boolean?                 @default(false)

  @@unique([event_id, security_pin], map: "security_pin")
  @@index([event_id], map: "index_event_official_event_id")
  @@index([official_id], map: "index_official_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_official_additional_payment {
  event_official_additional_payment_id    Int                 @id @default(autoincrement())
  created                                 DateTime?           @default(now()) @db.Timestamp(6)
  modified                                DateTime?           @default(now()) @db.Timestamp(6)
  amount                                  Decimal?            @db.Decimal(10, 2)
  official_additional_payment_category_id Int
  event_official_id                       Int
  event_id                                Int
  member_type                             payout_member_types @default(officials)

  @@unique([official_additional_payment_category_id, event_official_id, event_id], map: "event_official_additional_payment_unique")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_official_additional_role {
  created                     DateTime @default(now()) @db.Timestamp(6)
  modified                    DateTime @default(now()) @db.Timestamp(6)
  event_official_id           Int
  official_additional_role_id Int

  @@index([event_official_id], map: "event_official_additional_role_event_official_id_index")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_official_group {
  event_official_group_id Int       @unique(map: "unique_event_official_group_id") @default(autoincrement())
  created                 DateTime? @default(now()) @db.Timestamp(6)
  modified                DateTime? @default(now()) @db.Timestamp(6)
  pattern_id              Int?
  group_name              String
  event_id                Int?

  @@index([event_id], map: "index_event_id8")
}

model event_official_history {
  event_official_history_id Int       @id @default(autoincrement())
  event_official_id         Int
  created                   DateTime? @default(now()) @db.Timestamp(6)
  modified                  DateTime? @db.Timestamp(6)
  action                    String
  comment                   String
  user_id                   Int?
}

model event_official_rate {
  event_official_rate_id Int            @id @default(autoincrement())
  created                DateTime?      @default(now()) @db.Timestamp(6)
  modified               DateTime?      @default(now()) @db.Timestamp(6)
  event_id               Int
  match_type             String         @db.VarChar(100)
  rank                   official_rank?
  rate                   Decimal?       @db.Decimal(10, 2)
  deprecated             Boolean?

  @@unique([event_id, match_type, rank], map: "event_official_rate_unique")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_official_schedule {
  creared                    DateTime  @default(now()) @db.Timestamp(6)
  division_id                Int
  event_id                   Int
  event_official_id          Int
  event_official_schedule_id Int       @id @default(autoincrement())
  match_name                 String
  modified                   DateTime  @default(now()) @db.Timestamp(6)
  ref_num                    Int       @default(1)
  published                  Boolean   @default(false)
  event_official_group_id    Int?
  court_id                   String?   @db.Uuid
  match_start_time           DateTime? @db.Timestamp(6)

  @@unique([division_id, match_name, ref_num], map: "unique_ref_num_per_match")
  @@index([event_id], map: "index_event_id5")
  @@index([event_official_group_id], map: "index_event_official_group_id")
  @@index([event_official_id], map: "index_event_official_id")
  @@index([event_official_schedule_id], map: "index_event_official_schedule_id")
  @@index([match_name], map: "index_match_name")
}

model event_operation {
  event_operation String    @id
  created         DateTime? @default(now()) @db.Timestamp(6)
  modified        DateTime? @db.Timestamp(6)
  title           String
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_owner {
  event_owner_id         Int      @id @unique(map: "unique_event_owner_id") @default(autoincrement())
  created                DateTime @default(now()) @db.Timestamp(6)
  modified               DateTime @default(now()) @db.Timestamp(6)
  user_id                Int
  approved               Boolean  @default(false)
  has_sw_fee             Boolean  @default(true)
  require_match_end_time Boolean  @default(false)

  @@index([event_owner_id], map: "index_event_owner_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_payment_method {
  event_payment_method_id  Int       @default(autoincrement())
  created                  DateTime  @default(now()) @db.Timestamptz(6)
  modified                 DateTime? @db.Timestamptz(6)
  event_id                 Int       @unique
  stripe_payment_method_id String
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_team_checkin {
  event_team_checkin_id Int       @unique(map: "unique_event_team_checkin_id") @default(autoincrement())
  event_id              Int
  roster_team_id        Int
  master_staff_id       Int
  created               DateTime? @default(now()) @db.Timestamp(6)
  modified              DateTime? @default(now()) @db.Timestamp(6)
  staff_type            Int?
  date_scanned          DateTime? @db.Timestamp(6)
  deactivated_at        DateTime? @db.Timestamp(6)

  @@index([event_id], map: "index_event_id7")
  @@index([master_staff_id], map: "index_master_staff_id")
  @@index([roster_team_id], map: "index_roster_team_id5")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_ticket {
  event_id                 Int
  label                    String      @db.VarChar(100)
  initial_price            Decimal     @db.Decimal
  prices                   Json?       @db.Json
  event_ticket_id          Int         @unique @default(autoincrement())
  created                  DateTime    @default(now()) @db.Timestamp(6)
  modified                 DateTime    @default(now()) @db.Timestamp(6)
  published                Boolean?    @default(false)
  current_price            Decimal?    @db.Decimal
  short_label              String?     @db.VarChar(50)
  application_fee          Decimal     @default(0) @db.Decimal
  sort_order               Int?
  description              String?
  max_count                Int?
  foreground_color         String?
  background_color         String?
  event_camp_id            Int?
  waitlisted               Boolean?    @default(false)
  waitlist_switching_count Decimal?    @db.Decimal
  has_barcode              Boolean?    @default(true)
  ticket_type              ticket_type @default(other)
  kiosk_surcharge          Decimal?    @db.Decimal
  can_be_scanned           Boolean     @default(true)
  visible_in_kiosk         Boolean     @default(true)
  related_events           Int[]       @default([])
  valid_dates              Json        @default("{}")
  is_free                  Boolean     @default(false)

  @@index([event_id], map: "event_ticket_event_id_index")
  @@index([event_ticket_id], map: "index_event_ticket_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model event_ticket_buy_entry_code_settings {
  event_ticket_buy_entry_code_settings_id Int     @default(autoincrement())
  event_id                                Int     @unique
  is_active                               Boolean @default(true)
  team_code_source_enabled                Boolean @default(true)
  team_code_settings                      Json?
  custom_code_source_enabled              Boolean @default(false)
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model event_user {
  event_user_id  Int       @default(autoincrement())
  user_id        Int
  event_owner_id Int
  event_id       Int
  role_co_owner  Boolean   @default(false)
  created        DateTime? @default(now()) @db.Timestamp(6)
  modified       DateTime? @default(now()) @db.Timestamp(6)
  deleted        DateTime? @db.Timestamp(6)

  @@index([event_user_id], map: "index_event_user_id")
  @@ignore
}

model event_user_permission {
  event_operation_id String
  event_id           Int
  user_id            Int
  created            DateTime? @default(now()) @db.Timestamp(6)
  modified           DateTime? @db.Timestamp(6)
  granter_user_id    Int
  deleted            DateTime? @db.Timestamp(6)

  @@id([event_operation_id, event_id, user_id])
}

model fastline_verification {
  fastline_verification_id Int       @id @default(autoincrement())
  created                  DateTime  @default(now()) @db.Timestamptz(6)
  modified                 DateTime? @db.Timestamptz(6)
  user_id                  Int
  device_id                String
  last_action              String?
  session_id               String    @unique
  is_verified              Boolean?  @default(false)

  @@unique([user_id, device_id], map: "fastline_verification_user_id_device_id_uindex")
}

model favorite_event {
  favorite_event_id Int       @id @default(autoincrement())
  created           DateTime? @default(now()) @db.Timestamp(6)
  modified          DateTime? @db.Timestamp(6)
  user_id           Int
  event_id          Int

  @@unique([event_id, user_id])
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model housing_company {
  housing_company_id Int      @unique(map: "unique_housing_company_id") @default(autoincrement())
  created            DateTime @default(now()) @db.Timestamp(6)
  modified           DateTime @default(now()) @db.Timestamp(6)
  name               String   @db.VarChar(200)
}

/// This model has been renamed to 'Renamedimport' during introspection, because the original name 'import' is reserved.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model Renamedimport {
  import_id Int            @id @default(autoincrement())
  created   DateTime       @default(now()) @db.Timestamp(6)
  modified  DateTime?      @db.Timestamp(6)
  process   import_process
  event_id  Int
  progress  Float?
  status    import_status? @default(running)
  output    Json?

  @@map("import")
}

model knex_migrations {
  id             Int       @id @default(autoincrement())
  name           String?   @db.VarChar(255)
  batch          Int?
  migration_time DateTime? @db.Timestamptz(6)
}

model knex_migrations_lock {
  index     Int  @id @default(autoincrement())
  is_locked Int?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model master_athlete {
  master_athlete_id    Int       @id @default(autoincrement())
  created              DateTime  @default(now()) @db.Timestamp(6)
  modified             DateTime  @default(now()) @db.Timestamp(6)
  first                String    @db.VarChar(100)
  last                 String    @db.VarChar(100)
  phonem               String?   @db.VarChar(30)
  phoneh               String?   @db.VarChar(30)
  phonep               String?   @db.VarChar(30)
  address              String?   @db.VarChar(100)
  city                 String?   @db.VarChar(100)
  state                String?   @db.VarChar(2)
  zip                  String?   @db.VarChar(10)
  country              String?   @default("US") @db.VarChar(2)
  email                String?   @db.VarChar(100)
  birthdate            DateTime? @db.Date
  gradyear             Int?
  gender               gender?
  club_owner_id        Int?
  master_club_id       Int?
  master_team_id       Int?
  deleted              DateTime? @db.Timestamp(6)
  jersey               Int?
  organization_code    String?   @db.VarChar(30)
  sport_position_id    Int?
  height               String?   @db.VarChar(10)
  age                  Int?
  height_int           Int?
  nick                 String?   @db.VarChar(100)
  address2             String?   @db.VarChar(100)
  other_phone          String?   @db.VarChar(50)
  parent1_first        String?   @db.VarChar(100)
  parent1_last         String?   @db.VarChar(100)
  parent1_email        String?   @db.VarChar(100)
  parent2_first        String?   @db.VarChar(100)
  parent2_last         String?   @db.VarChar(100)
  parent2_email        String?   @db.VarChar(100)
  membership_status    String?   @db.VarChar(20)
  season               Int?
  usav_number          Int?
  ref_cert_name        String?
  ref_end_date         String?
  score_cert_name      String?
  score_end_date       String?
  webpoint_sync        DateTime? @db.Timestamp(6)
  safesport_end_date   String?
  safesport_start_date String?
  safesport_statusid   String?
  sportengine_sync     DateTime? @db.Timestamp(6)
  seasonality          String?   @default("full")

  @@index([last], map: "index_last")
  @@index([master_club_id], map: "index_master_club_id2")
  @@index([master_team_id], map: "index_master_team_id1")
  @@index([organization_code], map: "index_organization_code")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model master_club {
  master_club_id           Int       @id(map: "club_pkey") @unique(map: "unique_master_club_id") @default(autoincrement())
  created                  DateTime  @default(now()) @db.Timestamp(6)
  modified                 DateTime  @default(now()) @db.Timestamp(6)
  club_name                String    @db.VarChar(200)
  club_owner_id            Int?
  code                     String?
  region                   String?   @db.VarChar(10)
  sport_id                 Int
  address                  String?   @db.VarChar(200)
  city                     String?   @db.VarChar(100)
  state                    String?   @db.Char(2)
  zip                      String?   @db.VarChar(10)
  country                  String?   @default("US") @db.Char(2)
  has_male_teams           Boolean   @default(false)
  has_female_teams         Boolean   @default(false)
  has_coed_teams           Boolean   @default(false)
  director_first           String?   @db.VarChar(100)
  director_last            String?   @db.VarChar(100)
  director_email           String?   @db.VarChar(100)
  director_phone           String?   @db.VarChar(20)
  director_birthdate       DateTime? @db.Date
  director_gender          gender?
  director_modified        DateTime? @db.Timestamp(6)
  director_master_staff_id Int?
  team_prefix              String?   @db.VarChar(40)
  webpoint_username        String?   @db.VarChar(100)
  webpoint_password        String?   @db.VarChar(100)
  director_usav_code       String?   @db.VarChar(40)
  webpoint_import_agree    Boolean?
  deactivated              DateTime? @db.Timestamp(6)
  webpoint_club_id         Int?
  coordinates              Json?     @default("{}") @db.Json
  profile_completed_at     DateTime? @db.Timestamp(6)
  sportengine_club_id      Int?
  administrative_email     String?   @db.VarChar(255)

  @@index([master_club_id], map: "index_master_club_id")
  @@index([sport_id], map: "index_sport_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model master_club_sanctioning {
  master_club_sanctioning_id Int      @id(map: "unique_master_club_sanctioning_id") @default(autoincrement())
  created                    DateTime @default(now()) @db.Timestamp(6)
  modified                   DateTime @default(now()) @db.Timestamp(6)
  master_club_id             Int
  sport_sanctioning_id       Int

  @@index([master_club_sanctioning_id], map: "index_master_club_sanctioning_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model master_club_sport_variation {
  master_club_sport_variation_id Int      @id(map: "unique_master_club_sport_variation_id") @default(autoincrement())
  created                        DateTime @default(now()) @db.Timestamp(6)
  modified                       DateTime @default(now()) @db.Timestamp(6)
  master_club_id                 Int
  sport_variation_id             Int

  @@unique([master_club_id, sport_variation_id], map: "master_club_sport_variation_unique")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model master_staff {
  master_staff_id      Int       @id(map: "staff_pkey") @unique(map: "unique_staff_id") @default(autoincrement())
  created              DateTime? @default(now()) @db.Timestamp(6)
  modified             DateTime? @default(now()) @db.Timestamp(6)
  first                String?   @db.VarChar(100)
  last                 String?   @db.VarChar(100)
  email                String?   @db.VarChar(100)
  phone                String?
  master_club_id       Int?
  birthdate            DateTime? @db.Date
  gender               gender?
  master_team_id       Int?
  deleted              DateTime? @db.Timestamp(6)
  organization_code    String?   @db.VarChar(20)
  cert                 String?   @db.VarChar(20)
  phoneh               String?   @db.VarChar(50)
  city                 String?   @db.VarChar(100)
  state                String?   @db.VarChar(2)
  zip                  String?   @db.VarChar(20)
  address              String?   @db.VarChar(200)
  nick                 String?   @db.VarChar(20)
  address2             String?   @db.VarChar(100)
  phonew               String?   @db.VarChar(50)
  phoneo               String?   @db.VarChar(50)
  membership_status    String?   @db.VarChar(20)
  bg_screening         String?   @db.VarChar(20)
  bg_expire_date       DateTime? @db.Timestamp(6)
  chaperone_status     String?   @db.VarChar(20)
  coach_status         String?   @db.VarChar(20)
  season               Int?
  usav_number          Int?
  safesport_end_date   String?
  safesport_start_date String?
  safesport_statusid   String?
  webpoint_modified    DateTime? @db.Timestamp(6)
  checkin_barcode      String?
  is_impact            Boolean?
  ref_cert_name        String?
  ref_end_date         String?
  score_cert_name      String?
  score_end_date       String?
  bch_ref_cert_name    String?
  bch_ref_end_date     String?
  webpoint_sync        DateTime? @db.Timestamp(6)
  sportengine_sync     DateTime? @db.Timestamp(6)

  @@index([master_club_id], map: "index_club_id1")
  @@index([organization_code], map: "index_organization_code1")
  @@index([master_staff_id], map: "index_staff_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model master_staff_role {
  master_staff_role_id Int      @id(map: "unique_staff_role_id") @default(autoincrement())
  created              DateTime @default(now()) @db.Timestamp(6)
  modified             DateTime @default(now()) @db.Timestamp(6)
  master_staff_id      Int
  role_id              Int
  master_team_id       Int?
  primary              Boolean?

  @@index([master_team_id], map: "index_master_team_id2")
  @@index([master_staff_id], map: "index_role_id")
  @@index([role_id], map: "index_role_id1")
  @@index([master_staff_role_id], map: "index_staff_role_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model master_team {
  master_team_id     Int          @id(map: "team_master_pkey") @default(autoincrement())
  created            DateTime     @default(now()) @db.Timestamp(6)
  modified           DateTime     @default(now()) @db.Timestamp(6)
  team_name          String       @db.VarChar(200)
  club_owner_id      Int?
  organization_code  String?      @db.VarChar(20)
  rank               String?      @db.VarChar(2)
  age                Int?
  sport_id           Int
  master_club_id     Int
  gender             team_gender?
  sport_variation_id Int?
  deleted            DateTime?    @db.Timestamp(6)
  season             Int?
  seasonality        String?      @default("full")

  @@index([master_club_id], map: "index_club_id")
  @@index([created], map: "index_created")
  @@index([modified], map: "index_modified")
  @@index([master_team_id], map: "index_team_master_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains an index with non-default null sort order and requires additional setup for migrations. Visit https://pris.ly/d/default-index-null-ordering for more info.
model matches {
  match_id                    String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  match_barcode               BigInt?   @default(dbgenerated("(0)::bigint"))
  pool_bracket_id             String?   @db.Uuid
  match_number                Int?      @default(0)
  display_name                String?   @db.VarChar
  division_short_name         String?   @db.VarChar
  round_id                    String?   @db.Uuid
  group_id                    String?   @db.Uuid
  is_tie_breaker              Int?      @default(0)
  duration                    Int?      @default(60)
  day                         Int?      @default(0)
  secs_start                  DateTime? @db.Timestamp(6)
  secs_end                    DateTime? @db.Timestamp(6)
  secs_finished               DateTime? @db.Timestamp(6)
  court_id                    String?   @db.Uuid
  court_sort_priority         Int?      @default(0)
  team1_roster_id             Int?      @default(0)
  team2_roster_id             Int?      @default(0)
  ref_roster_id               Int?      @default(0)
  source                      String?   @db.VarChar
  dest                        String?   @db.VarChar
  ref_source_custom           String?   @db.VarChar
  official_assigned_id        Int       @default(0)
  official_entry_id           Int?      @default(0)
  results                     Json?
  match_status                Int       @default(0)
  match_stats                 String?   @db.VarChar
  modified                    DateTime? @db.Timestamp(6)
  is_dirty                    Int?      @default(0)
  event_id                    Int?
  division_id                 Int?
  scoresheet_printed          Int?
  start_time_string           String?   @db.VarChar
  officials_required          Int       @default(0)
  officials_assigned          Int       @default(0)
  officials_required_override Int       @default(0)
  tie_breaker_type            Int       @default(0)
  medal_winner                String?   @db.VarChar(250)
  medal_loser                 String?   @db.VarChar(250)
  secs_entered                DateTime? @db.Timestamp(6)
  scoresheet_scanned          Boolean?  @default(false)
  final_rank_winner           Int?      @default(0)
  final_rank_loser            Int?      @default(0)
  footnote_play               String?
  footnote_team1              String?
  footnote_team2              String?
  wave                        String?   @db.VarChar(2)
  swb_settings                Json?
  finishes                    String?
  type                        String?

  @@index([event_id], map: "event_id_index")
  @@index([modified], map: "index_modified1")
  @@index([type], map: "index_type")
  @@index([match_id], map: "match_uuid_index")
  @@index([court_id])
  @@index([court_sort_priority])
  @@index([display_name])
  @@index([division_id])
  @@index([division_short_name])
  @@index([event_id, pool_bracket_id], map: "matches_event_id_pool_bracket_id_index")
  @@index([group_id])
  @@index([is_dirty])
  @@index([is_tie_breaker])
  @@index([match_barcode])
  @@index([match_number])
  @@index([official_assigned_id])
  @@index([official_entry_id])
  @@index([round_id])
  @@index([secs_end])
  @@index([secs_finished])
  @@index([secs_start])
  @@index([pool_bracket_id(sort: Desc)], map: "poolbracketid_index")
  @@index([ref_roster_id], map: "ret_ref_index")
  @@index([team1_roster_id], map: "rt1_index")
  @@index([team2_roster_id], map: "rt2_index")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model materialized_view_schedule {
  materialized_view_schedule_id Int       @default(autoincrement())
  created                       DateTime? @default(now()) @db.Timestamptz(6)
  modified                      DateTime? @db.Timestamptz(6)
  name                          String
  schedule                      String
  is_active                     Boolean

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ncsa_event {
  ncsa_event_id Int?
  created       DateTime  @default(now()) @db.Timestamptz(6)
  modified      DateTime? @db.Timestamptz(6)
  event_id      Int?      @unique

  @@index([ncsa_event_id, event_id], map: "ncsa_event_ncsa_event_id_event_id_index")
  @@ignore
}

model ncsa_recruit_request {
  ncsa_recruit_request_id Int       @id @default(autoincrement())
  created                 DateTime  @default(now()) @db.Timestamptz(6)
  modified                DateTime? @db.Timestamptz(6)
  event_id                Int
  request                 Json
  response                Json
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ncsa_sport {
  ncsa_sport_id Int?      @unique
  created       DateTime  @default(now()) @db.Timestamptz(6)
  modified      DateTime? @db.Timestamptz(6)
  sw_sport_id   Int
  name          String
  gender        gender

  @@index([sw_sport_id, gender], map: "ncsa_sport_sw_sport_id_gender_index")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model official {
  official_id                    Int            @id @default(autoincrement())
  created                        DateTime       @default(now()) @db.Timestamp(6)
  modified                       DateTime       @default(now()) @db.Timestamp(6)
  user_id                        Int            @unique(map: "unique_official_user_id")
  region                         String?        @db.VarChar(2)
  usav_num                       String?        @db.VarChar(20)
  background_screening           String?        @db.VarChar(20)
  rank                           official_rank?
  address                        String?        @db.VarChar(100)
  city                           String?        @db.VarChar(100)
  state                          String?        @db.VarChar(2)
  zip                            String?        @db.VarChar(10)
  advancement                    Boolean?
  webpoint_username              String?        @db.VarChar(100)
  webpoint_password              String?        @db.VarChar(100)
  is_staff                       Boolean?
  is_official                    Boolean?
  country                        String?        @db.VarChar(2)
  security_pin                   String?        @unique(map: "unique_official_security_pin")
  webpoint_data                  Json?
  aau_region                     String?
  aau_number                     String?
  special_sizing_requests        String?
  birthdate                      DateTime?      @db.Timestamp(6)
  emergency_contact_name         String?
  emergency_phone                String?
  emergency_contact_relationship String?
  safesport_end_date             String?
  safesport_start_date           String?
  safesport_statusid             String?
  profile_completed_at           DateTime?      @db.Timestamp(6)
  webpoint_modified              DateTime?      @db.Timestamp(6)
  bg_expire_date                 DateTime?      @db.Date
  mbr_expire_date                DateTime?      @db.Date
  sportengine_modified           DateTime?      @db.Timestamp(6)
  sportengine_data               Json?
  arbiter_pay_username           String?
  arbiter_pay_account_number     String?
  checkin_barcode                String?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model official_additional_payment_category {
  official_additional_payment_category_id Int       @id @default(autoincrement())
  created                                 DateTime? @default(now()) @db.Timestamp(6)
  modified                                DateTime? @default(now()) @db.Timestamp(6)
  category                                String    @db.VarChar(50)
  event_id                                Int?
  show_for_officials                      Boolean?  @default(true)
  show_for_staff                          Boolean?  @default(true)

  @@unique([category, event_id], map: "official_additional_payment_category_unique")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model official_additional_role {
  official_additional_role_id Int      @id @default(autoincrement())
  created                     DateTime @default(now()) @db.Timestamp(6)
  modified                    DateTime @default(now()) @db.Timestamp(6)
  name                        String
  description                 String?
}

model official_clothes_size {
  official_clothes_size_id Int       @id @default(autoincrement())
  official_id              Int
  common_item_id           String
  size                     String?
  created                  DateTime? @default(now()) @db.Timestamp(6)
  modified                 DateTime? @default(now()) @db.Timestamp(6)

  @@unique([official_id, common_item_id], map: "official_clothes_size_unique")
  @@index([common_item_id], map: "index_common_item_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model official_payout {
  official_payout_id Int                      @id @default(autoincrement())
  created            DateTime?                @default(now()) @db.Timestamp(6)
  modified           DateTime?                @default(now()) @db.Timestamp(6)
  date_paid          DateTime                 @db.Timestamp(6)
  payment_method     official_payment_option?
  amount             Decimal                  @db.Decimal(10, 2)
  check_number       String?
  notes              String?
  event_official_id  Int
  user_id            Int
  event_id           Int
  member_type        payout_member_types      @default(officials)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model password_recovery {
  password_recovery_id Int       @id(map: "unique_password_recovery_id") @default(autoincrement())
  created              DateTime  @default(now()) @db.Timestamp(6)
  modified             DateTime  @default(now()) @db.Timestamp(6)
  recovery_code        String?   @unique(map: "unique_recovery_code") @db.VarChar(50)
  opened_at            DateTime? @db.Timestamp(6)
  recovered_at         DateTime? @db.Timestamp(6)
  email                String    @db.VarChar(200)
  user_id              Int?
  ip_address           String?
  user_agent           String?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model poolbrackets {
  uuid                        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  division_short_name         String    @db.VarChar(10)
  round_id                    String    @db.Uuid
  group_id                    String?   @db.Uuid
  sort_priority               Int       @default(0)
  is_pool                     Int       @default(0)
  name                        String?   @db.VarChar
  short_name                  String?   @db.VarChar
  display_name                String?   @db.VarChar
  team_count                  Int       @default(0)
  match_count                 Int       @default(0)
  settings                    Json?
  template                    String?   @db.VarChar
  consolation                 Int?      @default(0)
  flow_chart                  String?   @db.VarChar
  pb_seeds                    String?   @db.VarChar
  pb_finishes                 String?   @db.VarChar
  pb_status                   Int       @default(0)
  pb_stats                    String?   @db.VarChar
  modified                    DateTime? @db.Timestamp(6)
  is_dirty                    Int       @default(0)
  event_id                    Int?
  division_id                 Int?
  custom                      String?   @db.VarChar
  pb_team_ids                 String?   @db.VarChar
  officials_required_override Int       @default(0)
  tie_breaker_type            Int       @default(0)
  swb_settings                Json?

  @@index([division_id])
  @@index([division_short_name])
  @@index([event_id])
  @@index([group_id])
  @@index([is_dirty])
  @@index([is_pool])
  @@index([pb_seeds])
  @@index([round_id])
  @@index([sort_priority])
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model purchase {
  purchase_id              Int                         @id @unique(map: "unique_purchase_id") @default(autoincrement())
  created                  DateTime                    @default(now()) @db.Timestamp(6)
  modified                 DateTime                    @default(now()) @db.Timestamp(6)
  event_id                 Int
  amount                   Decimal                     @db.Decimal(10, 2)
  roster_club_id           Int?
  date_paid                DateTime?                   @db.Timestamp(6)
  status                   String?                     @db.VarChar(10)
  stripe_charge_id         String?                     @db.VarChar(50)
  stripe_card_id           String?                     @db.VarChar(50)
  check_num                String?                     @db.VarChar(50)
  memo                     String?
  received_date            DateTime?                   @db.Timestamp(6)
  type                     purchase_type?
  email                    String?                     @db.VarChar(100)
  phone                    String?                     @db.VarChar(20)
  amount_refunded          Decimal?                    @db.Decimal(10, 2)
  dispute_created          DateTime?                   @db.Timestamp(6)
  club_owner_id            Int?
  card_name                String?
  card_last_4              String?                     @db.VarChar(4)
  sponsor_id               Int?
  notes                    String?
  payment_for              String
  sales_manager_id         Int?
  event_owner_id           Int?
  user_id                  Int?
  booth_label              String?                     @db.VarChar(10)
  canceled_date            DateTime?                   @db.Timestamp(6)
  date_refunded            DateTime?                   @db.Timestamp(6)
  ticket_barcode           Int?
  tickets_scan             String?
  scanned_at               DateTime?                   @db.Timestamp(6)
  scanner_id               String?                     @db.VarChar(60)
  scanner_location         String?                     @db.VarChar(60)
  payer_ip                 String?                     @db.VarChar(45)
  zip                      String?                     @db.VarChar(10)
  source                   payment_source?
  first                    String?                     @db.VarChar(100)
  last                     String?                     @db.VarChar(100)
  tickets_additional       Json?                       @db.Json
  stripe_transfer_status   purchase_transfer_status?
  stripe_card_fingerprint  String?
  debt                     Decimal?                    @db.Decimal
  dispute_status           String?
  stripe_payment_type      stripe_payment_type?
  additional_fee_amount    Decimal?                    @default(0) @db.Decimal
  net_profit               Decimal?                    @default(0) @db.Decimal
  stripe_fee               Decimal?                    @default(0) @db.Decimal
  stripe_balance_available DateTime?                   @db.Timestamp(6)
  collected_sw_fee         Decimal?                    @default(0) @db.Decimal
  stripe_account_id        String?
  reversed_amount          Decimal?                    @db.Decimal
  received_amount          Decimal?                    @db.Decimal
  purchase_discount        Decimal                     @default(0) @db.Decimal
  hash                     String?
  linked_purchase_id       Int?
  is_payment               Boolean?                    @default(true)
  is_ticket                Boolean?                    @default(false)
  wristband_serial         String?
  kiosk                    Json?
  stripe_percent           Decimal?                    @db.Decimal
  teams_balance_info       Json?
  deactivated_at           DateTime?                   @db.Timestamp(6)
  deactivated_by_user_id   Int?
  registration_status      ticket_registration_status? @default(active)
  payment_intent_id        String?
  payment_hub_order_uuid   String?

  @@unique([wristband_serial, event_id], map: "unique_wristband_serial_for_event")
  @@index([event_id, payment_for], map: "index_event_id1")
  @@index([purchase_id], map: "index_purchase_id")
  @@index([roster_club_id], map: "index_roster_club_id1")
  @@index([amount])
  @@index([created])
  @@index([event_id])
  @@index([linked_purchase_id], map: "purchase_linked_purchase_idx")
  @@index([payment_for])
  @@index([stripe_charge_id], map: "purchase_stripe_charge_id_index")
  @@index([ticket_barcode])
  @@index([user_id], map: "purchase_user_id_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model purchase_booth {
  purchase_booth_id Int      @unique(map: "unique_purchase_booth_id") @default(autoincrement())
  created           DateTime @default(now()) @db.Timestamp(6)
  modified          DateTime @default(now()) @db.Timestamp(6)
  purchase_id       Int
  event_booth_id    Int?
  fee               Decimal  @db.Decimal(8, 2)
  quantity          Int
  amount            Decimal  @db.Decimal(8, 2)
  title             String   @db.VarChar(100)
  description       String?
  booth_label       String?  @db.VarChar(100)
  notes             String?

  @@index([purchase_id], map: "purchase_booth_purchase_id_index")
}

model purchase_history {
  purchase_history_id    Int       @id(map: "purchase_history_purchase_history_id_pk") @unique(map: "unique_purchase_history_id") @default(autoincrement())
  created                DateTime? @default(now()) @db.Timestamp(6)
  modified               DateTime? @default(now()) @db.Timestamp(6)
  purchase_id            Int
  action                 String
  stripe_event_id        String?
  description            String?
  notes                  String?
  user_id                Int?
  amount                 Decimal?  @db.Decimal
  check_num              String?
  operation_date         DateTime? @db.Timestamp(6)
  purchase_ticket_id     Int?
  payment_hub_order_uuid String?

  @@index([purchase_id], map: "purchase_id_index_history")
  @@index([user_id], map: "user_id_index_history")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model purchase_team {
  purchase_team_id Int       @id @unique @default(autoincrement())
  created          DateTime  @default(now()) @db.Timestamp(6)
  modified         DateTime  @default(now()) @db.Timestamp(6)
  purchase_id      Int
  event_id         Int
  roster_team_id   Int
  amount           Decimal   @db.Decimal(8, 2)
  division_id      Int?
  division_fee     Decimal?  @db.Decimal(8, 2)
  surcharge        Decimal?  @db.Decimal(8, 2)
  event_fee        Decimal?  @db.Decimal(8, 2)
  canceled         DateTime? @db.Timestamp(6)
  sw_fee           Decimal?  @default(0) @db.Decimal

  @@index([event_id], map: "index_event_id2")
  @@index([purchase_id], map: "index_purchase_id1")
  @@index([purchase_team_id], map: "index_purchase_team_id")
  @@index([roster_team_id], map: "index_roster_team_id1")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model purchase_ticket {
  purchase_ticket_id    Int                         @id(map: "purchase_ticket_purchase_ticket_id_pk") @unique(map: "unique_purchase_ticket_id") @default(autoincrement())
  created               DateTime?                   @default(now()) @db.Timestamp(6)
  modified              DateTime                    @default(now()) @db.Timestamp(6)
  purchase_id           Int
  amount                Decimal                     @db.Decimal
  ticket_price          Decimal                     @db.Decimal
  quantity              Int
  canceled              DateTime?                   @db.Timestamp(6)
  available             Int                         @default(0)
  event_ticket_id       Int
  discount              Decimal?                    @default(0) @db.Decimal
  ticket_discount_id    BigInt?
  discounted_quantity   Int?
  ticket_fee            Decimal?                    @default(0) @db.Decimal
  kiosk_surcharge       Decimal?                    @db.Decimal
  registration_status   ticket_registration_status? @default(active)
  ticket_coupon_id      Int?
  has_covid_test        DateTime?                   @db.Timestamp(6)
  border_colour         String?
  ticket_buy_entry_code String?

  @@index([ticket_coupon_id], map: "index_ticket_coupon_id")
  @@index([event_ticket_id, purchase_id], map: "purchase_ticket_event_id_event_ticket_id")
  @@index([event_ticket_id], map: "purchase_ticket_event_ticket_id")
  @@index([event_ticket_id])
  @@index([purchase_id])
}

model recognition_verification {
  recognition_verification_id Int       @id @default(autoincrement())
  created                     DateTime  @default(now()) @db.Timestamptz(6)
  modified                    DateTime? @db.Timestamptz(6)
  user_id                     Int
  last_action                 String?
  session_id                  String    @unique
  is_verified                 Boolean?  @default(false)

  @@index([user_id], map: "recognition_verification_user_id_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model region {
  region   String   @id @unique(map: "unique_region") @db.Char(2)
  name     String   @unique(map: "unique_name") @db.VarChar(100)
  created  DateTime @default(now()) @db.Timestamp(6)
  modified DateTime @default(now()) @db.Timestamp(6)
  country  String?  @db.VarChar(2)

  @@index([name], map: "index_name1")
  @@index([region], map: "index_region")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model request_log {
  id          BigInt   @unique(map: "unique_id") @default(autoincrement())
  created     DateTime @default(now()) @db.Timestamp(6)
  modified    DateTime @default(now()) @db.Timestamp(6)
  type        log_type
  request_url String   @db.VarChar(100)
  verb        String   @db.VarChar(10)
  remote_ip   String?  @db.VarChar(100)
  user_agent  String?
  data        String?

  @@index([id], map: "index_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model results {
  id                String    @id
  moment            DateTime? @db.Timestamp(6)
  match_barcode     Int?      @default(0)
  data              String?
  is_dirty          Int?      @default(0)
  event_id          Int?
  json_version      Int       @default(0)
  secs_finished     DateTime? @db.Timestamp(6)
  official_entry_id Int?      @default(0)
  swb_settings      Json?

  @@index([event_id, match_barcode], map: "results_event_id_match_barcode_index")
  @@index([is_dirty])
  @@index([moment])
}

model role {
  role_id    Int      @id @default(autoincrement())
  name       String   @db.VarChar(100)
  sort_order Int
  type       String?  @db.VarChar(20)
  created    DateTime @default(now()) @db.Timestamp(6)
  modified   DateTime @default(now()) @db.Timestamp(6)
  short_name String?

  @@index([sort_order], map: "index_sort_order")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model roster_athlete {
  roster_athlete_id Int       @id @unique(map: "unique_roster_athlete_id") @default(autoincrement())
  created           DateTime  @default(now()) @db.Timestamp(6)
  modified          DateTime  @default(now()) @db.Timestamp(6)
  master_athlete_id Int
  event_id          Int?
  jersey            Int?
  sport_position_id Int?
  roster_team_id    Int?
  deleted           DateTime? @db.Timestamp(6)
  as_staff          Int?      @default(0)
  deleted_by_user   DateTime? @db.Timestamp(6)

  @@unique([master_athlete_id, event_id, as_staff], map: "unique_athlete_on_event")
  @@index([master_athlete_id], map: "index_master_athlete_id")
  @@index([deleted, event_id, deleted_by_user], map: "index_roster_athlete_deleted")
  @@index([roster_athlete_id], map: "index_roster_athlete_id")
  @@index([roster_team_id], map: "roster_athlete_roster_team_id_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model roster_club {
  roster_club_id     Int       @id @default(autoincrement())
  created            DateTime  @default(now()) @db.Timestamp(6)
  modified           DateTime  @default(now()) @db.Timestamp(6)
  master_club_id     Int
  event_id           Int
  club_name          String    @db.VarChar(200)
  deleted            DateTime? @db.Timestamp(6)
  zip                String?   @db.VarChar(10)
  distance_to_event  Float?    @db.Real
  country            String    @db.Char(2)
  state              String?   @db.Char(2)
  region             String    @db.VarChar(10)
  city               String    @db.VarChar(100)
  address            String    @db.VarChar(200)
  code               String    @db.VarChar(5)
  total_tentative    Int?
  total_accepted     Int?
  total_confirmed    Int?
  max_total_accepted Int?
  is_local           Boolean?
  is_virtual         Boolean   @default(false)

  @@unique([master_club_id, event_id], map: "unique_club_in_event")
  @@index([deleted], map: "index_roster_club_deleted")
  @@index([roster_club_id], map: "index_roster_club_id")
}

model roster_staff_role {
  roster_staff_role_id Int       @id @default(autoincrement())
  created              DateTime  @default(now()) @db.Timestamp(6)
  modified             DateTime  @default(now()) @db.Timestamp(6)
  role_id              Int?
  master_team_id       Int?
  roster_team_id       Int?
  primary              Boolean?
  master_staff_id      Int
  deleted              DateTime? @db.Timestamp(6)
  deleted_by_user      DateTime? @db.Timestamp(6)

  @@unique([master_staff_id, roster_team_id], map: "unique_role_on_event")
  @@index([master_staff_id], map: "rsr_master_staff_id_index")
  @@index([master_team_id], map: "rsr_master_team_id_index")
  @@index([role_id], map: "rsr_role_id_index")
  @@index([roster_staff_role_id], map: "rsr_roster_staff_role_id_index")
  @@index([roster_team_id], map: "rsr_roster_team_id_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model roster_team {
  age                         Decimal?                  @db.Decimal
  division_id                 Int?
  organization_code           String?                   @db.VarChar(12)
  rank                        String?                   @db.VarChar(2)
  team_name                   String                    @db.VarChar(250)
  event_id                    Int
  created                     DateTime                  @default(now()) @db.Timestamp(6)
  modified                    DateTime                  @default(now()) @db.Timestamp(6)
  roster_team_id              Int                       @id(map: "team_roster_pkey") @unique(map: "unique_team_roster_id") @default(autoincrement())
  club_owner_id               Int?
  sport_id                    Int?
  master_team_id              Int?
  date_entered                DateTime?                 @db.Timestamp(6)
  date_completed              DateTime?                 @db.Timestamp(6)
  date_paid                   DateTime?                 @db.Timestamp(6)
  status_housing              Int?                      @default(0)
  status_roster               Int?                      @default(0)
  distance                    Int?
  date_accepted               DateTime?                 @db.Timestamp(6)
  status_entry                Int?
  status_paid                 Int?
  is_local                    Boolean?
  locked                      Boolean?                  @default(false)
  deleted                     DateTime?                 @db.Timestamp(6)
  discount                    Decimal?                  @db.Decimal(8, 2)
  roster_club_id              Int?
  division_name               String?                   @db.VarChar(120)
  gender                      team_gender?
  ths_tentative_nights        Int?
  ths_confirmed_nights        Int?
  ths_id                      Int?
  ths_hotel_name              String?                   @db.VarChar(100)
  ths_hotel_status            String?                   @db.VarChar(20)
  ths_loyalty                 Int?
  ths_contract_issued         DateTime?                 @db.Timestamp(6)
  ths_when_accepted           DateTime?                 @db.Timestamp(6)
  ths_when_canceled           DateTime?                 @db.Timestamp(6)
  ths_modified                DateTime?                 @db.Timestamp(6)
  total_tentative             Int?
  total_accepted              Int?
  total_confirmed             Int?
  max_total_accepted          Int?
  date_housing                DateTime?                 @db.Timestamp(6)
  matches_won                 Int?
  matches_lost                Int?
  sets_won                    Int?
  sets_lost                   Int?
  points_won                  Int?
  points_lost                 Int?
  status_checkin              team_checkin_status?
  roster_athletes_count       Int?                      @default(0)
  roster_staff_count          Int?                      @default(0)
  online_checkin_date         DateTime?                 @db.Timestamp(6)
  is_valid_roster             Boolean?
  roster_validated_at         DateTime?                 @db.Timestamp(6)
  ths_total_loyalty_confirmed Int?                      @default(0)
  ths_total_loyalty_accepted  Int                       @default(0)
  team_alert_note             String?
  extra                       Json?
  wristbands_count_staff      Int?
  wristbands_count_athletes   Int?
  ths_trav_coord_name         String?
  ths_trav_coord_email        String?
  ths_trav_coord_phone        String?
  roster_validated_by         roster_validation_source?
  reg_method                  team_reg_method?          @default(regular)
  housing_status_changed_at   DateTime?                 @db.Timestamp(6)
  manual_club_name            String?
  team_code                   String?
  seasonality                 String?                   @default("full")
  action_clip_streaming_pin   Int?

  @@unique([event_id, action_clip_streaming_pin], map: "action_clip_streaming_pin_codes_unique")
  @@unique([event_id, team_code], map: "unique_roster_team_team_code_event_id")
  @@index([master_team_id], map: "index_master_team_id")
  @@index([deleted], map: "index_roster_team_deleted")
  @@index([division_id], map: "index_roster_team_division_id")
  @@index([roster_club_id], map: "index_roster_team_roster_club_id")
  @@index([team_name], map: "index_team_name")
  @@index([roster_team_id], map: "index_team_roster_id")
  @@index([event_id], map: "index_tournament_id")
  @@index([organization_code], map: "organization_code_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model rounds {
  uuid                        String    @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  division_short_name         String    @db.VarChar(10)
  parent                      String?   @db.Uuid
  sort_priority               Int       @default(0)
  name                        String?   @db.VarChar
  short_name                  String?   @db.VarChar
  target_team_count           Int       @default(0)
  default_pool_size           Int       @default(0)
  round_seeds                 String?   @db.VarChar
  round_finishes              String?   @db.VarChar
  round_status                Int       @default(0)
  modified                    DateTime? @db.Timestamp(6)
  is_dirty                    Int       @default(0)
  event_id                    Int?
  division_id                 Int?
  officials_required_override Int       @default(0)
  tie_breaker_type            Int       @default(0)
  swb_settings                Json?

  @@index([division_id])
  @@index([event_id])
  @@index([is_dirty])
  @@index([parent])
  @@index([sort_priority])
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model safeguard_request {
  safeguard_request_id Int       @unique(map: "unique_safeguard_request_id") @default(autoincrement())
  created              DateTime? @default(now()) @db.Timestamp(6)
  modified             DateTime? @default(now()) @db.Timestamp(6)
  request_json         Json?     @db.Json
  request_xml          String?   @db.Xml
  response_xml         String?   @db.Xml
  method               String?
  ip                   String?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sales_manager {
  sales_manager_id Int      @unique(map: "unique_sales_manager_id") @default(autoincrement())
  created          DateTime @default(now()) @db.Timestamp(6)
  modified         DateTime @default(now()) @db.Timestamp(6)
  user_id          Int      @unique(map: "unique_sales_manager_user_id")
  activated        Boolean  @default(false)
}

model settings {
  key   String @id @unique(map: "unique_key") @db.VarChar(200)
  value Json?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sms_event {
  sms_event_id          Int       @id @unique(map: "sms_event_sms_event_id_uindex") @default(autoincrement())
  modified              DateTime? @db.Timestamp(6)
  created               DateTime? @default(now()) @db.Timestamp(6)
  phone_to              String
  phone_from            String
  event                 String
  event_body            Json
  text                  String?
  sms_service_id        String
  messaging_service_sid String?
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sms_message {
  sms_message_id        Int       @id @unique(map: "sms_message_sms_message_id_uindex") @default(autoincrement())
  created               DateTime? @default(now()) @db.Timestamp(6)
  modified              DateTime? @db.Timestamp(6)
  sms_from              String?
  sms_to                String
  sms_text              String?
  sms_service_id        String?
  response              Json
  date_sent             DateTime? @db.Timestamp(6)
  date_queued           DateTime? @db.Timestamp(6)
  date_delivered        DateTime? @db.Timestamp(6)
  date_failed           DateTime? @db.Timestamp(6)
  date_undelivered      DateTime? @db.Timestamp(6)
  date_unsubscribed     DateTime? @db.Timestamp(6)
  purchase_id           Int?
  messaging_service_sid String?

  @@index([sms_service_id], map: "sms_message_sms_service_id_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sponsor {
  sponsor_id          Int      @id @default(autoincrement())
  created             DateTime @default(now()) @db.Timestamp(6)
  modified            DateTime @default(now()) @db.Timestamp(6)
  user_id             Int?
  company_name        String   @db.VarChar(200)
  first               String   @db.VarChar(100)
  last                String   @db.VarChar(100)
  email               String   @db.VarChar(100)
  mobile_phone        String   @db.VarChar(20)
  office_phone        String?  @db.VarChar(20)
  street              String?  @db.VarChar(200)
  city                String?  @db.VarChar(100)
  state               String?  @db.VarChar(2)
  zip                 String?  @db.VarChar(10)
  company_description String?
  badge_names         String?
  is_exhibitor        Boolean? @default(true)
  is_sponsor          Boolean? @default(false)
  is_non_profit       Boolean? @default(false)
  is_other            Boolean? @default(false)
  added_by_sales_id   Int?
  added_by_event_id   Int?
  website_url         String?  @db.VarChar(500)
  sponsor_title       String?
  samples_food        Boolean?
  samples_beverages   Boolean?
  company_samples     Boolean?
  added_by_user_id    Int?

  @@unique([user_id, added_by_user_id], map: "sponsor_unique_by_added_by_user_id")
}

model sport {
  sport_id   Int      @id(map: "unique_sport") @default(autoincrement())
  created    DateTime @default(now()) @db.Timestamp(6)
  modified   DateTime @default(now()) @db.Timestamp(6)
  name       String   @db.VarChar(200)
  short_name String   @db.VarChar(20)

  @@index([sport_id], map: "index_sport")
}

model sport_position {
  sport_position_id    Int      @id @unique(map: "unique_position_id") @default(autoincrement())
  created              DateTime @default(now()) @db.Timestamp(6)
  modified             DateTime @default(now()) @db.Timestamp(6)
  sport_id             Int
  name                 String   @db.VarChar(100)
  short_name           String   @db.VarChar(20)
  ua_sport_position_id Int?

  @@index([sport_position_id], map: "index_position_id")
  @@index([sport_id], map: "index_sport_id2")
}

model sport_sanctioning {
  sport_sanctioning_id Int      @id @default(autoincrement())
  created              DateTime @default(now()) @db.Timestamp(6)
  modified             DateTime @default(now()) @db.Timestamp(6)
  sport_id             Int
  name                 String   @db.VarChar(200)

  @@index([sport_id], map: "index_sport_id3")
  @@index([sport_sanctioning_id], map: "index_sport_sanctioning_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sport_variation {
  sport_variation_id Int      @id @default(autoincrement())
  created            DateTime @default(now()) @db.Timestamp(6)
  modified           DateTime @default(now()) @db.Timestamp(6)
  sport_id           Int
  name               String   @db.VarChar(100)
  sort_order         Int?

  @@index([sport_id], map: "index_sport_id5")
  @@index([sport_variation_id], map: "index_sport_variation_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model sportengine_queue {
  sportengine_queue_id Int                        @default(autoincrement())
  created              DateTime                   @default(now()) @db.Timestamptz(6)
  modified             DateTime?                  @db.Timestamptz(6)
  master_club_id       Int
  requested            DateTime?                  @db.Timestamp(6)
  response_body        String?
  responded            DateTime?                  @db.Timestamp(6)
  option               sportengine_import_option? @default(default)

  @@index([created], map: "sportengine_queue_created_index")
  @@index([master_club_id], map: "sportengine_queue_master_club_id_index")
  @@index([requested], map: "sportengine_queue_requested_index")
  @@index([responded], map: "sportengine_queue_responded_index")
  @@ignore
}

model state {
  state    String    @id @db.Char(2)
  country  String    @db.Char(2)
  name     String    @db.VarChar(100)
  created  DateTime? @default(now()) @db.Timestamp(6)
  modified DateTime? @default(now()) @db.Timestamp(6)

  @@index([country], map: "index_country")
  @@index([name], map: "index_name")
  @@index([state], map: "index_state")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_account {
  id                      Int                  @default(autoincrement())
  secret_key              String
  public_key              String
  stripe_connect          Json?                @db.Json
  account_statement       String?
  account_name            String?
  account_email           String?
  stripe_account_id       String
  created                 DateTime?            @default(now()) @db.Timestamp(6)
  modified                DateTime?            @db.Timestamp(6)
  is_test                 Boolean?             @default(false)
  event_owner_id          Int?
  is_platform             Boolean              @default(false)
  user_id                 Int?
  connected_to_account_id String?
  account_type            stripe_account_type?
  platform_client_id      String?
  dashboard_url           String?
  hidden                  Boolean              @default(false)

  @@unique([id, is_test], map: "unique_row_id")
  @@unique([stripe_account_id, is_test], map: "unique_stripe_account_id")
  @@unique([user_id, connected_to_account_id], map: "user_connected_account_unique")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_charge {
  created             DateTime? @default(now()) @db.Timestamp(6)
  modified            DateTime? @default(now()) @db.Timestamp(6)
  stripe_charge_id    String    @unique(map: "unique_stripe_charge_id")
  amount              Decimal   @db.Decimal
  fee                 Decimal?  @default(0) @db.Decimal
  balance_transaction Json
  collected_fee       Decimal?  @default(0) @db.Decimal
  type                String?
  stripe_payment_id   String?
  stripe_account_id   String?

  @@index([stripe_charge_id], map: "stripe_charge_id_index")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_dispute {
  stripe_dispute_id String?   @unique
  created           DateTime  @default(now()) @db.Timestamptz(6)
  modified          DateTime? @db.Timestamptz(6)
  amount            Decimal   @db.Decimal(8, 2)
  status            String
  stripe_charge_id  String

  @@index([stripe_charge_id], map: "stripe_dispute_stripe_charge_id_index")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_dispute_evidence {
  id                             Int       @id @default(autoincrement())
  stripe_dispute_id              String
  receipt                        Json?
  service_date                   String?
  customer_name                  String?
  refund_policy                  Json?
  shipping_date                  String?
  billing_address                String?
  shipping_address               String?
  shipping_carrier               String?
  customer_signature             Json?
  uncategorized_file             Json?
  uncategorized_text             String?
  access_activity_log            String?
  cancellation_policy            Json?
  duplicate_charge_id            String?
  product_description            String?
  customer_purchase_ip           String?
  cancellation_rebuttal          String?
  service_documentation          Json?
  customer_communication         Json?
  customer_email_address         String?
  shipping_documentation         Json?
  refund_policy_disclosure       String?
  shipping_tracking_number       String?
  refund_refusal_explanation     String?
  duplicate_charge_explanation   String?
  cancellation_policy_disclosure String?
  duplicate_charge_documentation Json?
  created                        DateTime  @default(now()) @db.Timestamptz(6)
  submitted_at                   DateTime? @db.Timestamptz(6)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_event {
  stripe_event_id Int      @id @unique(map: "unique_stripe_event_id") @default(autoincrement())
  created         DateTime @default(now()) @db.Timestamp(6)
  modified        DateTime @default(now()) @db.Timestamp(6)
  event_id        String   @db.VarChar(50)
  type            String   @db.VarChar(50)
  data            String
  stripe_id       String?  @db.VarChar(100)
  amount          Decimal? @db.Decimal(10, 2)
  status          String?  @db.VarChar(50)

  @@index([event_id], map: "index_stripe_event_event_id")
  @@index([stripe_event_id], map: "index_stripe_event_id")
  @@index([stripe_id], map: "stripe_event_stripe_id_index")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_payment_intent {
  stripe_payment_intent_id     Int       @default(autoincrement())
  created                      DateTime  @default(now()) @db.Timestamptz(6)
  modified                     DateTime? @db.Timestamptz(6)
  payment_intent_status        String
  stripe_charge_id             String?
  payment_intent_id            String
  amount                       Decimal   @db.Decimal(8, 2)
  stripe_fee                   Decimal   @db.Decimal(8, 2)
  stripe_percent               Decimal   @db.Decimal(8, 2)
  last_error_payment_method_id String?
  last_error_client_secret     String?
  stripe_card_fingerprint      String?
  stripe_dispute_id            String?

  @@index([payment_intent_status], map: "stripe_custom_payment_payment_intent_status_index")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_payment_method {
  stripe_payment_method_id String                     @id
  stripe_customer_id       String
  created                  DateTime                   @default(now()) @db.Timestamptz(6)
  modified                 DateTime?                  @db.Timestamptz(6)
  type                     stripe_payment_method_type
  card_last_4              String?                    @db.VarChar(4)
  card_brand               String?
  card_exp_month           Int?
  card_exp_year            Int?
  payment_object           Json
  fingerprint              String
  is_default               Boolean                    @default(false)

  @@unique([fingerprint, stripe_customer_id], map: "stripe_payment_method_card_fingerprint_stripe_customer_id")
  @@index([stripe_customer_id], map: "stripe_payment_method_stripe_customer_id_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model stripe_transfer {
  id                            Int       @unique(map: "unique_stripe_transfer_id") @default(autoincrement())
  created                       DateTime? @default(now()) @db.Timestamp(6)
  modified                      DateTime? @default(now()) @db.Timestamp(6)
  event_id                      Int?
  amount                        Decimal   @db.Decimal
  stripe_account_id             String?
  stripe_transfer_id            String?
  description                   String
  transfer_type                 String?
  check_num                     String?
  date_sent                     DateTime? @db.Date
  date_canceled                 DateTime? @db.Timestamp(6)
  payment_for                   String?
  destination_stripe_account_id String?

  @@index([stripe_account_id], map: "index_stripe_account_id")
  @@index([event_id], map: "index_stripe_transfer_event_id")
  @@index([stripe_transfer_id], map: "index_stripe_transfer_id")
}

model swt_api_history {
  swt_api_history_id   Int       @unique(map: "unique_swt_api_history_swt_api_history_id") @default(autoincrement())
  created              DateTime? @default(now()) @db.Timestamp(6)
  modified             DateTime? @default(now()) @db.Timestamp(6)
  request_url          String
  request_body         Json?
  request_query        Json?
  verb                 String
  ip                   String
  user_agent           String?
  action_type          String
  scanner_name         String?
  scanner_location     String?
  event_id             Int?
  ticket_barcode       String?
  response_body        Json?
  response_status_code Int?
  success              Boolean?
  exec_time            Int?
  request_params       Json?

  @@index([action_type], map: "swt_api_history_action_type")
  @@index([scanner_location], map: "swt_api_history_scanner_location")
  @@index([scanner_name], map: "swt_api_history_scanner_name")
  @@index([ticket_barcode], map: "swt_api_history_ticket_barcode")
}

model system_job {
  system_job_id             Int               @id @default(autoincrement())
  job_type                  system_job_type
  event_id                  Int?
  email_sending_id          String?
  recipient_type            String?
  email_template_id         Int?
  user_id                   Int
  started_at                DateTime?         @db.Timestamp(6)
  finished_at               DateTime?         @db.Timestamp(6)
  created                   DateTime?         @default(now()) @db.Timestamp(6)
  modified                  DateTime?         @default(now()) @db.Timestamp(6)
  status                    system_job_status @default(scheduled)
  custom_recipients_list_id Int?
  settings                  Json?             @default("{}")

  @@index([email_sending_id], map: "system_job_email_sending_id_index")
  @@index([job_type], map: "system_job_job_type_index")
  @@index([recipient_type], map: "system_job_recipient_type_index")
}

model team_status {
  team_status_id Int      @unique @default(autoincrement())
  created        DateTime @default(now()) @db.Timestamp(6)
  modified       DateTime @default(now()) @db.Timestamp(6)
  status_code    String   @db.VarChar(20)
  status_value   String   @db.VarChar(50)
  status_title   String   @db.VarChar(100)
  color          String   @db.VarChar(50)

  @@index([status_code], map: "index_status_code")
  @@index([team_status_id], map: "index_team_status_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model teams_import {
  team_code     String?
  team_name     String?
  division_name String?
  club_name     String?
  club_state    String?
  event_id      Int

  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ths_booking {
  created              DateTime  @default(now()) @db.Timestamp(6)
  event_id             Int
  modified             DateTime  @default(now()) @db.Timestamp(6)
  roster_team_id       Int
  ths_booking_id       Int       @unique(map: "unique_ths_booking_id") @default(autoincrement())
  ths_confirmed_nights Int?
  ths_contract_issued  DateTime? @db.Timestamp(6)
  ths_hotel_name       String?   @db.VarChar(100)
  ths_hotel_status     String?   @db.VarChar(20)
  ths_id               Int?      @unique(map: "ths_booking_ths_id")
  ths_loyalty          Int?
  ths_modified         DateTime? @db.Timestamp(6)
  ths_tentative_nights Int?
  ths_when_accepted    DateTime? @db.Timestamp(6)
  ths_when_canceled    DateTime? @db.Timestamp(6)

  @@index([event_id], map: "index_event_id3")
  @@index([roster_team_id], map: "index_roster_team_id2")
  @@index([ths_booking_id], map: "index_ths_booking_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ths_history {
  created              DateTime  @default(now()) @db.Timestamp(6)
  modified             DateTime  @default(now()) @db.Timestamp(6)
  event_id             Int
  roster_team_id       Int
  ths_history_id       Int       @unique(map: "unique_ths_history_id") @default(autoincrement())
  ths_confirmed_nights Int?
  ths_tentative_nights Int?
  ths_contract_issued  DateTime? @db.Timestamp(6)
  ths_hotel_name       String?   @db.VarChar(100)
  ths_hotel_status     String?   @db.VarChar(20)
  ths_id               Int?
  ths_loyalty          Int?
  ths_modified         DateTime? @db.Timestamp(6)
  ths_when_accepted    DateTime? @db.Timestamp(6)
  ths_when_canceled    DateTime? @db.Timestamp(6)

  @@index([event_id], map: "index_ths_history_event_id")
  @@index([ths_history_id], map: "index_ths_history_id")
  @@index([roster_team_id], map: "index_ths_history_roster_team_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model ticket_buy_entry_code {
  ticket_buy_entry_code_id Int       @default(autoincrement())
  created                  DateTime? @default(now()) @db.Timestamp(6)
  modified                 DateTime? @db.Timestamp(6)
  code                     String
  event_id                 Int

  @@ignore
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model ticket_coupon {
  ticket_coupon_id Int       @id @default(autoincrement())
  created          DateTime  @default(now()) @db.Timestamp(6)
  modified         DateTime? @db.Timestamp(6)
  code             String
  event_ticket_id  Int
  initial_quantity Int
  quantity         Int
  active           Boolean   @default(true)

  @@index([event_ticket_id])
  @@index([event_ticket_id], map: "ticket_coupon_event_ticket_id_index")
}

model ticket_coupon_history {
  ticket_coupon_history_id Int       @id @default(autoincrement())
  created                  DateTime  @default(now()) @db.Timestamp(6)
  modified                 DateTime? @db.Timestamp(6)
  ticket_coupon_id         Int
  action                   String
  data                     Json      @default("{}")

  @@index([ticket_coupon_id, ticket_coupon_history_id(sort: Desc)], map: "ticket_coupon_history_ticket_coupon_id_ticket_coupon_history_id")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ticket_coupon_receiver {
  ticket_coupon_receiver_id Int       @default(autoincrement())
  created                   DateTime  @default(now()) @db.Timestamptz(6)
  modified                  DateTime? @db.Timestamptz(6)
  ticket_coupon_id          Int
  roster_team_id            Int?
  email                     String?
  first                     String?
  last                      String?

  @@index([ticket_coupon_id], map: "ticket_coupon_receiver_ticket_coupon_id_index")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model ticket_discount {
  ticket_discount_id BigInt    @default(autoincrement())
  email              String
  first              String?
  last               String?
  event_id           Int
  event_ticket_id    Int
  max_count          Int       @default(0)
  used_count         Int       @default(0)
  discount           Decimal   @default(0) @db.Decimal
  notified_at        DateTime? @db.Timestamp(6)
  code               String?
  created            DateTime? @default(now()) @db.Timestamp(6)
  modified           DateTime? @default(now()) @db.Timestamp(6)

  @@unique([code, event_ticket_id], map: "unique_code_per_type")
  @@index([event_id], map: "index_event_id6")
  @@index([event_ticket_id], map: "index_event_ticket_id1")
  @@index([ticket_discount_id], map: "index_ticket_discount_id")
  @@ignore
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ticket_wallet {
  ticket_wallet_id    Int       @id @default(autoincrement())
  created             DateTime? @default(now()) @db.Timestamptz(6)
  modified            DateTime? @db.Timestamptz(6)
  purchaser_user_id   Int
  holder_user_id      Int
  ticket_barcode      Int
  shared_by_purchaser Boolean   @default(false)

  @@unique([holder_user_id, ticket_barcode], map: "ticket_wallet_holder_user_id_ticket_barcode_unique_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model ticket_wallet_link {
  ticket_wallet_link_id Int       @id @default(autoincrement())
  created               DateTime? @default(now()) @db.Timestamptz(6)
  modified              DateTime? @db.Timestamptz(6)
  shared_by_user_id     Int
  ticket_barcode        Int
  expires_at            DateTime? @default(dbgenerated("(now() + '01:00:00'::interval)")) @db.Timestamptz(6)
}

model tickets_receipt_cron_result {
  id         Int       @id @unique(map: "tickets_receipt_cron_result_id_uindex") @default(autoincrement())
  date_start DateTime  @default(now()) @db.Timestamp(6)
  date_end   DateTime? @db.Timestamp(6)
  results    Json
}

model unsubscribe_token {
  unsubscribe_token_id Int       @id @default(autoincrement())
  created              DateTime  @default(now()) @db.Timestamptz(6)
  modified             DateTime? @db.Timestamptz(6)
  token                String    @unique @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  email                String
  metadata             Json      @default("{}")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user {
  gender                         gender?
  country                        String?   @db.VarChar(2)
  created                        DateTime? @default(now()) @db.Timestamp(6)
  email                          String    @db.VarChar(100)
  first                          String?   @db.VarChar(100)
  last                           String?   @db.VarChar(100)
  phone_mob                      String?   @db.VarChar(20)
  pwd_salt                       String?   @db.VarChar(100)
  role_club_director             Boolean
  role_event_owner               Boolean
  role_spectator                 Boolean
  role_staff                     Boolean
  zip                            String?   @db.VarChar(10)
  modified                       DateTime  @default(now()) @db.Timestamp(6)
  pwd_hash                       String?   @db.VarChar(100)
  user_id                        Int       @id @default(autoincrement())
  role_sponsor                   Boolean   @default(false)
  activated                      Boolean?  @default(false)
  activation_code                String?   @db.VarChar(50)
  remember_me                    String?
  role_sales_manager             Boolean?  @default(false)
  is_sw_owner                    Boolean?  @default(false)
  housing_company_id             Int?
  event_owner_request            DateTime? @db.Timestamp(6)
  event_owner_request_granter_id Int?
  has_usav_admin_role            Boolean?
  has_god_role                   Boolean?
  has_admin_role                 Boolean?
  is_recognition_verified        Boolean?  @default(false)
  deleted_at                     DateTime? @db.Timestamp(6)
  recognition_image              String?
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_action {
  user_id        Int
  created        DateTime         @default(now()) @db.Timestamp(6)
  modified       DateTime?        @db.Timestamp(6)
  action         user_action_type
  user_agent     String
  ip             String
  session_id     String
  action_details Json?            @default("{}")
  utc_offset     Int?             @db.SmallInt

  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_stripe_customer {
  user_stripe_customer_id Int       @default(autoincrement())
  created                 DateTime  @default(now()) @db.Timestamptz(6)
  modified                DateTime? @db.Timestamptz(6)
  user_id                 Int       @unique
  stripe_customer_id      String    @unique
  stripe_customer_object  Json
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model webpoint_adult {
  webpoint_adult_id    Int       @id @unique(map: "unique_webpoint_adult_id") @default(autoincrement())
  created              DateTime  @default(now()) @db.Timestamp(6)
  modified             DateTime  @default(now()) @db.Timestamp(6)
  bg_screening         String?   @db.VarChar(2044)
  bg_expire_date       DateTime? @db.Timestamp(6)
  chaperone_status     String?   @db.VarChar(2044)
  cap_client           String?   @db.VarChar(2044)
  coach_status         String?   @db.VarChar(2044)
  master_club_id       Int
  usav_code            String    @db.VarChar(2044)
  first                String?   @db.VarChar(2044)
  last                 String?   @db.VarChar(2044)
  nick                 String?   @db.VarChar(2044)
  address              String?   @db.VarChar(2044)
  address2             String?   @db.VarChar(2044)
  city                 String?   @db.VarChar(2044)
  state                String?   @db.VarChar(2044)
  zip                  String?   @db.VarChar(2044)
  zip4                 String?   @db.VarChar(2044)
  region               String?   @db.VarChar(2044)
  email                String?   @db.VarChar(2044)
  cell_phone           String?   @db.VarChar(2044)
  work_phone           String?   @db.VarChar(2044)
  home_phone           String?   @db.VarChar(2044)
  other_phone          String?   @db.VarChar(2044)
  birthdate            DateTime? @db.Timestamp(6)
  expire_date          DateTime? @db.Timestamp(6)
  waiver_received      DateTime? @db.Timestamp(6)
  status               String?   @db.VarChar(2044)
  member_id            Int?
  member_type          String?   @db.VarChar(2044)
  club_id              Int?
  club_code            String?   @db.VarChar(2044)
  club_name            String?   @db.VarChar(2044)
  membership_code      String?   @db.VarChar(2044)
  impact               Boolean?  @default(false)
  gender               gender?
  usav_number          Int?
  safesport_end_date   String?
  safesport_start_date String?
  safesport_statusid   String?
  webpoint_modified    DateTime? @db.Timestamp(6)
  ref_cert_name        String?
  ref_end_date         String?
  score_cert_name      String?
  score_end_date       String?
  bch_ref_cert_name    String?
  bch_ref_end_date     String?

  @@index([master_club_id], map: "webpoint_adult_master_club_id_index")
  @@index([membership_code], map: "webpoint_adult_org_code")
  @@index([usav_number], map: "webpoint_adult_usav_number_index")
}

model webpoint_athlete {
  webpoint_athlete_id Int       @id @default(autoincrement())
  master_club_id      Int
  parent1_first       String?   @db.VarChar(200)
  parent1_last        String?   @db.VarChar(200)
  parent1_email       String?   @db.VarChar(200)
  parent2_first       String?   @db.VarChar(100)
  parent2_last        String?   @db.VarChar(100)
  parent2_email       String?   @db.VarChar(200)
  level_of_play       String?   @db.VarChar(10)
  gradyear            Int?
  grade_in_school     String?   @db.VarChar
  usav_code           String?   @db.VarChar(20)
  created             DateTime  @default(now()) @db.Timestamp(6)
  modified            DateTime  @default(now()) @db.Timestamp(6)
  first               String?   @db.VarChar(2044)
  last                String?   @db.VarChar(2044)
  nick                String?   @db.VarChar(2044)
  address             String?   @db.VarChar(2044)
  address2            String?   @db.VarChar(2044)
  city                String?   @db.VarChar(2044)
  state               String?   @db.VarChar(2044)
  zip                 String?   @db.VarChar(2044)
  zip4                String?   @db.VarChar(2044)
  region              String?   @db.VarChar(2044)
  email               String?   @db.VarChar(2044)
  cell_phone          String?   @db.VarChar(2044)
  work_phone          String?   @db.VarChar(2044)
  home_phone          String?   @db.VarChar(2044)
  other_phone         String?   @db.VarChar(2044)
  birthdate           DateTime? @db.Timestamp(6)
  expire_date         DateTime? @db.Timestamp(6)
  waiver_received     DateTime? @db.Timestamp(6)
  status              String?   @db.VarChar(2044)
  member_id           Int?
  gender              String?   @db.VarChar(2044)
  member_type         String?   @db.VarChar(2044)
  club_id             Int?
  club_code           String?   @db.VarChar(2044)
  club_name           String?   @db.VarChar(2044)
  membership_code     String?   @db.VarChar(2044)
  height              String?   @db.VarChar(10)
  age                 Int?
  usav_number         Int?

  @@index([master_club_id], map: "webpoint_athlete_master_club_id_index")
  @@index([member_id], map: "webpoint_athlete_member_id_index")
  @@index([usav_code], map: "webpoint_athlete_usav_code_index")
  @@index([usav_number], map: "webpoint_athlete_usav_number_index")
}

model webpoint_cert_name {
  id              Int     @id @default(autoincrement())
  rang            Int?
  wp_name         String?
  cert_name       String?
  cert_short_name String?
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model webpoint_parse {
  webpoint_parse_id     Int       @id @unique(map: "unique_webpoint_parse_id") @default(autoincrement())
  first                 String?   @db.VarChar(2044)
  last                  String?   @db.VarChar(2044)
  nick                  String?   @db.VarChar(2044)
  address               String?   @db.VarChar(2044)
  address2              String?   @db.VarChar(2044)
  city                  String?   @db.VarChar(2044)
  state                 String?   @db.VarChar(2044)
  zip                   String?   @db.VarChar(2044)
  zip4                  String?   @db.VarChar(2044)
  region                String?   @db.VarChar(2044)
  email                 String?   @db.VarChar(2044)
  cell_phone            String?   @db.VarChar(2044)
  work_phone            String?   @db.VarChar(2044)
  home_phone            String?   @db.VarChar(2044)
  other_phone           String?   @db.VarChar(2044)
  birthdate             DateTime? @db.Timestamp(6)
  handed                String?   @db.VarChar(2044)
  parent1_first         String?   @db.VarChar(2044)
  parent1_last          String?   @db.VarChar(2044)
  parent1_email         String?   @db.VarChar(2044)
  parent2_first         String?   @db.VarChar(2044)
  parent2_last          String?   @db.VarChar(2044)
  parent2_email         String?   @db.VarChar(2044)
  expire_date           DateTime? @db.Timestamp(6)
  waiver_received       DateTime? @db.Timestamp(6)
  level_of_play         String?   @db.VarChar(2044)
  ethics_received       String?   @db.VarChar(2044)
  approach              String?   @db.VarChar(2044)
  status                String?   @db.VarChar(2044)
  gradyear              Int?
  usav_code             String?   @db.VarChar(2044)
  member_id             Int?
  shoe_size             String?   @db.VarChar(2044)
  hat_size              String?   @db.VarChar(2044)
  shirt_size            String?   @db.VarChar(2044)
  spandex_size          String?   @db.VarChar(2044)
  waist_size            String?   @db.VarChar(2044)
  jacket_size           String?   @db.VarChar(2044)
  jersey_size           String?   @db.VarChar(2044)
  short_size            String?   @db.VarChar(2044)
  pant_size             String?   @db.VarChar(2044)
  gpa                   String?   @db.VarChar(2044)
  act                   String?   @db.VarChar(2044)
  sat                   String?   @db.VarChar(2044)
  verbal_sat            String?   @db.VarChar(2044)
  math_sat              String?   @db.VarChar(2044)
  reach                 String?   @db.VarChar(2044)
  inseam                String?   @db.VarChar(2044)
  created_date          DateTime? @db.Timestamp(6)
  activation_date       DateTime? @db.Timestamp(6)
  fingerprint_date      DateTime? @db.Timestamp(6)
  weighted_gpa          String?   @db.VarChar(2044)
  member_type           String?   @db.VarChar(2044)
  bg_screening          String?   @db.VarChar(2044)
  bg_expire_date        DateTime? @db.Timestamp(6)
  club_id               Int?
  club_code             String?   @db.VarChar(2044)
  club_name             String?   @db.VarChar(2044)
  club_dir_status       String?   @db.VarChar(2044)
  safe_sport_status     String?   @db.VarChar(2044)
  player_status         String?   @db.VarChar(2044)
  two_handed_reach      String?   @db.VarChar(2044)
  one_hand_reach        String?   @db.VarChar(2044)
  safe_sport_start_date String?   @db.VarChar(2044)
  grade_in_school       String?   @db.VarChar(2044)
  chaperone_status      String?   @db.VarChar(2044)
  cap_client            String?   @db.VarChar(2044)
  region_info           String?   @db.VarChar(2044)
  height                String?   @db.VarChar(2044)
  membership_code       String?   @db.VarChar(2044)
  coach_status          String?   @db.VarChar(2044)
  membership_option     String?   @db.VarChar(2044)
  player_block          String?   @db.VarChar(2044)
  created               DateTime  @default(now()) @db.Timestamp(6)
  modified              DateTime  @default(now()) @db.Timestamp(6)
  gender                gender?
  usav_number           Int?
}

model webpoint_queue {
  webpoint_queue_id Int                     @unique(map: "unique_webpoint_queue_id") @default(autoincrement())
  created           DateTime                @default(now()) @db.Timestamptz(6)
  modified          DateTime                @default(now()) @db.Timestamptz(6)
  master_club_id    Int
  requested         DateTime?               @db.Timestamp(6)
  response_body     String?
  responded         DateTime?               @db.Timestamp(6)
  option            webpoint_import_option? @default(default)

  @@index([created], map: "index_created3")
  @@index([master_club_id], map: "index_master_club_id1")
  @@index([created], map: "webpoint_queue_created_index")
  @@index([requested], map: "webpoint_queue_requested_index")
  @@index([responded], map: "webpoint_queue_responded_index")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model webpoint_request_log {
  id         Int       @id @default(autoincrement())
  hash       String?
  usav_code  String?
  response   Json?     @db.Json
  event_id   Int?
  ip         String?
  user_agent String?
  caller     String?
  created    DateTime? @db.Timestamp(6)
  modified   DateTime? @db.Timestamp(6)
}

model webpoint_team_member {
  id                 Int      @unique @default(autoincrement())
  master_club_id     Int?
  team_name          String   @db.VarChar
  team_usav_code     String   @db.VarChar
  member_usav_code   String   @db.VarChar
  member_jersey      String   @db.VarChar
  member_title       String   @db.VarChar
  member_name        String   @db.VarChar
  is_dirty           Boolean? @default(true)
  moment             DateTime @default(now()) @db.Timestamp(6)
  member_usav_number Int?

  @@index([master_club_id], map: "webpoint_team_member_master_club_id_index")
  @@index([member_usav_code])
  @@index([member_usav_number], map: "webpoint_team_member_member_usav_number_index")
}

model zip_location {
  zip      String    @id @db.VarChar(10)
  country  String    @db.VarChar(20)
  location Json      @db.Json
  created  DateTime? @default(now()) @db.Timestamp(6)
  modified DateTime? @default(now()) @db.Timestamp(6)

  @@index([zip], map: "index_zip1")
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum camp_visibility {
  hidden
  eo
  published
}

enum custom_payment_for {
  uncollected_fee
  other
}

enum custom_payment_for_type {
  tickets
  teams
  booths
  other
}

enum custom_payment_session_mode {
  default
  offline
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum custom_payment_status {
  canceled
  paid
  pending
  disputed
}

enum event_exhibitor_status {
  pending
  approved
  declined
}

enum event_media_type {
  main_logo   @map("main-logo")
  small_logo  @map("small-logo")
  cover_image @map("cover-image")
  flowchart
}

enum event_notify_frequency {
  never
  hourly
  daily
  immediately
}

enum event_official_rating {
  UnRated
  International
  National
  Jr_National   @map("Jr National")
  Regional
  Provisional
  In_Region     @map("In Region")
}

enum event_teams_visibility {
  hide
  club
  public
}

enum event_teams_visibility_level {
  hidden
  accepted
  all
}

enum event_type {
  regional
  national
  qualifier
}

enum extra_fee_collection_mode {
  auto
  custom_payment
}

enum gender {
  male
  female
  unspecified
}

enum import_process {
  manual_vip_tickets @map("manual-vip-tickets")
}

enum import_status {
  running
  finished
  error
}

enum log_type {
  swt_api_access
  swt_api_error
  swt_crash_report
  stripe_connect
  event_update
  results_save
  club_update
  club_create
}

enum official_payment_option {
  direct_deposit
  on_site
  mailed
  arbiterpay
}

enum official_rank {
  Provisional
  In_Region     @map("In Region")
  Regional
  Jr__National  @map("Jr. National")
  International
  Other
  National
  UnRated
}

enum official_work_status {
  pending
  approved
  declined
  waitlisted
}

enum online_team_checkin_mode {
  default
  primary_staff_barcodes
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum payer_option {
  seller
  buyer
}

enum payment_source {
  site
  api
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum payout_member_types {
  officials
  staff
}

enum purchase_transfer_status {
  pending
  available
}

enum purchase_type {
  check
  cash
  free
  waitlist
  ach
  card
  pending_payment @map("pending-payment")
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum refund_request_status {
  pending
  approved
  declined
}

enum reg_method {
  club
  doubles
}

enum roster_validation_source {
  system
  eo
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum sportengine_import_option {
  default
  insert
}

enum stripe_account_type {
  standard
  express
  custom
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum stripe_payment_method_type {
  alipay
  au_becs_debit
  bacs_debit
  bancontact
  card
  eps
  fpx
  giropay
  ideal
  oxxo
  p24
  sepa_debit
  sofort
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum stripe_payment_type {
  default
  connect
}

enum system_job_status {
  scheduled
  started
  recipients_list_generation
  sending_emails
  done
}

enum system_job_type {
  email_sending
}

enum team_checkin_status {
  checkedin
  notcheckedin
  pending
  alert
}

enum team_gender {
  male
  female
  coed
}

enum team_reg_method {
  regular
  private_link
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum ths_teams_access_level {
  entered
  paid
  accepted
}

enum ticket_registration_status {
  active
  canceled
}

enum ticket_type {
  weekend
  daily
  other
}

enum user_action_type {
  login
  logout
}

/// This enum is commented in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
enum webpoint_import_option {
  default
  insert
}
