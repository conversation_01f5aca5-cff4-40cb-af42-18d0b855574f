version: '3.8'
services:
  app:
    container_name: esw-graphql-api
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - '${PORT:-3080}:3080'
    environment:
      APP_ENV: ${APP_ENV}
      INSTANCES: ${INSTANCES}
      DATABASE_URL: ${DATABASE_URL}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      SENTRY_ORG: ${SENTRY_ORG}
      SENTRY_PROJECT: ${SENTRY_PROJECT}
      SENTRY_AUTH_TOKEN: ${SENTRY_AUTH_TOKEN}
      SENTRY_DSN: ${SENTRY_DSN}
      SENTRY_RELEASE: ${SENTRY_RELEASE}
      USE_SEARCH_SERVICES: ${USE_SEARCH_SERVICES}
      SESSION_PREFIX: ${SESSION_PREFIX}
      SESSION_SECRET: ${SESSION_SECRET}
      SESSION_COOKIE_KEY: ${SESSION_COOKIE_KEY}
