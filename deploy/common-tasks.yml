- name: Copy files to the server
  synchronize:
    src: ../../
    dest: '{{ deploy_folder }}'
    rsync_opts:
      - '--no-motd'
      - '--exclude=.git'
      - '--exclude=deploy'
      - '--exclude=.gitlab-ci.yml'
      - '--delete'

- name: Create environment variables file
  copy:
    dest: '{{ deploy_folder }}/.env'
    content: |
      APP_ENV={{APP_ENV}}
      INSTANCES={{INSTANCES}}
      PORT={{PORT}} 
      DATABASE_URL={{DATABASE_URL}}
      REDIS_HOST={{REDIS_HOST}}
      REDIS_PORT={{REDIS_PORT}}
      SENTRY_ORG=sentry
      SENTRY_PROJECT=esw-api
      SENTRY_AUTH_TOKEN={{SENTRY_AUTH_TOKEN}}
      SENTRY_DSN={{SENTRY_DSN}}
      SENTRY_RELEASE={{COMMIT_SHA}}
      US<PERSON>_SEARCH_SERVICES={{USE_SEARCH_SERVICES}}
      DEBUG_MODE={{DEBUG_MODE}}
      SESSION_PREFIX={{SESSION_PREFIX}}
      SESSION_SECRET={{SESSION_SECRET}}
      SESSION_COOKIE_KEY={{SESSION_COOKIE_KEY}}

- name: Build Docker image(s)
  shell: "{{ docker_compose_command }} -f {{ deploy_folder }}/{{ docker_compose_file }} --env-file {{ deploy_folder }}/.env build"
  register: build_output

- name: Show build output
  debug:
    msg: "{{ build_output.stdout_lines }}"

- name: Stop and remove application container(s)
  shell: "{{ docker_compose_command }} -f {{ deploy_folder }}/{{ docker_compose_file }} down"
  register: down_output

- name: Show down output
  debug:
    msg: "{{ down_output.stdout_lines }}"

- name: Run compose
  shell: "{{ docker_compose_command }} -f {{ deploy_folder }}/{{ docker_compose_file }} --env-file {{ deploy_folder }}/.env up -d"
  register: up_output

- name: Show up output
  debug:
    msg: "{{ up_output.stdout_lines }}"

- name: Clean unused images
  shell: docker image prune -a --filter label=esw.graphql.api=true --force
  register: prune_output

- name: Show prune output
  debug:
    msg: "{{ prune_output.stdout_lines }}"

- name: Upload sourcemaps to Sentry
  shell: "{{ docker_compose_command }} -f {{ deploy_folder }}/{{ docker_compose_file }} --env-file {{ deploy_folder }}/.env exec app yarn sentry:sourcemaps"
  register: sourcemaps_output

- name: Show sourcemaps upload output
  debug:
    msg: "{{ sourcemaps_output.stdout_lines }}"
