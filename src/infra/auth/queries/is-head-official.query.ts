import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type IsHeadOfficialQueryParams = {
	eventKey: EventKey;
	officialId: number;
};

export const getIsHeadOfficialQuery = ({ eventKey, officialId }: IsHeadOfficialQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	const query = `
		SELECT 
			eof.head_official 
		FROM event_official eof 
		JOIN event e
			ON e.event_id = eof.event_id
		WHERE 
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND eof.official_id = ${sqlVars.useValue(officialId)}
			AND eof.head_official IS TRUE
			AND eof.deleted IS NULL
			AND e.deleted IS NULL
	`;

	return [query, sqlVars.getValues()];
};
