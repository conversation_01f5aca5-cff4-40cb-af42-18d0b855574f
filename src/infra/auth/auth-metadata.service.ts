import { ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { ACCESSED_EVENT_KEY_METADATA } from './constants';

@Injectable()
export class AuthMetadataService {
	constructor(private readonly reflector: Reflector) {}

	getAccessedEventKeyPropertyName(context: ExecutionContext): string | null {
		return this.reflector.get<string>(ACCESSED_EVENT_KEY_METADATA, context.getHandler());
	}
}
