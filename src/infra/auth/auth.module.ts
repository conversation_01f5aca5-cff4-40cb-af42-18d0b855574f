import { Global, Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';
import { SessionModule } from '@/infra/session/session.module';

import { AuthMetadataService } from './auth-metadata.service';
import { IsAuthenticatedGuard, IsOfficialGuard, IsHeadOfficialGuard } from './guards';
import { OfficialAuthService } from './official-auth.service';

@Global()
@Module({
	imports: [PrismaModule, CacheModule, SessionModule, EventHelperModule],
	providers: [AuthMetadataService, OfficialAuthService, IsAuthenticatedGuard, IsOfficialGuard, IsHeadOfficialGuard],
	exports: [AuthMetadataService, OfficialAuthService, EventHelperModule],
})
export class AuthModule {}
