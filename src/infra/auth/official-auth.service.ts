import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { PrismaService } from '@/infra/prisma/prisma.service';
import { CacheCategories, CacheScopes } from '@/shared/constants/cache';
import { EventKey } from '@/shared/utils/event';

import { getIsHeadOfficialQuery } from './queries/is-head-official.query';

@Injectable()
export class OfficialAuthService {
	constructor(private prisma: PrismaService, private cacheService: ExtendedCacheService) {}

	async isHeadOfficial(eventKey: EventKey, officialId: number): Promise<boolean> {
		const cacheKeyConfig = this._getHeadOfficialCacheKeyConfig(eventKey, officialId);
		return this.cacheService.getOrFetch<boolean>(cacheKeyConfig, () => this._checkHeadOfficialStatus(eventKey, officialId));
	}

	private async _checkHeadOfficialStatus(eventKey: EventKey, officialId: number): Promise<boolean> {
		const [query, values] = getIsHeadOfficialQuery({ eventKey, officialId });
		const result = await this.prisma.$queryRawUnsafe<Array<{ head_official: boolean }>>(query, ...values);
		return result.length > 0;
	}

	private _getHeadOfficialCacheKeyConfig(eventKey: EventKey, officialId: number): ItemCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.HeadOfficial,
			categoryKey: String(officialId),
		};
	}
}
