import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

import { SessionService } from '@/infra/session/session.service';
import { UserSession } from '@/infra/session/types';

@Injectable()
export abstract class BaseAuthGuard implements CanActivate {
	constructor(protected sessionService: SessionService) {}

	protected _getUserSession(context: ExecutionContext): UserSession | null {
		return this.sessionService.getUserSessionFromContext(context);
	}

	protected _getArgs<T = unknown>(context: ExecutionContext): T | null {
		if (context.getType<string>() === 'graphql') {
			const gqlContext = GqlExecutionContext.create(context);
			return gqlContext.getArgs<T>();
		}
		return null;
	}

	abstract canActivate(context: ExecutionContext): boolean | Promise<boolean>;
}
