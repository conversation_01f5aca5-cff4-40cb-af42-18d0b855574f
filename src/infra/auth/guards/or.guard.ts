import { CanActivate, ExecutionContext, Injectable, Type, mixin } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';

export function OrGuard(...guardTypes: Type<CanActivate>[]): Type<CanActivate> {
	@Injectable()
	class OrGuardMixin implements CanActivate {
		constructor(private moduleRef: ModuleRef) {}

		async canActivate(context: ExecutionContext): Promise<boolean> {
			// Resolve each guard from the DI container
			const guardsInstances = guardTypes.map((guard) => this.moduleRef.get(guard, { strict: false }));

			const results = await Promise.all(
				guardsInstances.map(
					(guard) => Promise.resolve(guard.canActivate(context)).catch(() => false), // Handle rejections as false
				),
			);

			return results.some((result) => result === true);
		}
	}

	return mixin(OrGuardMixin);
}
