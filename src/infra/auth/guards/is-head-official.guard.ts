import { Injectable, ExecutionContext } from '@nestjs/common';

import { AuthMetadataService } from '@/infra/auth/auth-metadata.service';
import { SessionService } from '@/infra/session/session.service';
import { EventKey } from '@/shared/utils/event';

import { OfficialAuthService } from '../official-auth.service';
import { BaseAuthGuard } from './base-auth.guard';

@Injectable()
export class IsHeadOfficialGuard extends BaseAuthGuard {
	constructor(
		protected sessionService: SessionService,
		private officialAuthService: OfficialAuthService,
		private authMetadataService: AuthMetadataService,
	) {
		super(sessionService);
	}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		const eventKey = this._getEventKey(context);
		if (!eventKey) {
			return false;
		}

		const userSession = this._getUserSession(context);
		if (!Number(userSession?.official_id)) {
			return false;
		}

		return this.officialAuthService.isHeadOfficial(eventKey, userSession.official_id);
	}

	private _getEventKey(context: ExecutionContext): EventKey | null {
		const args = this._getArgs(context);
		const propertyName = this.authMetadataService.getAccessedEventKeyPropertyName(context);

		const keyValue = args?.[propertyName];
		if (!EventKey.isValidKeyValue(keyValue)) {
			return null;
		}

		return EventKey.fromKeyValue(keyValue);
	}
}
