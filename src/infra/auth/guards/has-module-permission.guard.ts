import { Injectable, ExecutionContext, Type, mixin } from '@nestjs/common';

import { EventHelperService } from '@/infra/eventHelper/eventHelper.service';
import { SessionService } from '@/infra/session/session.service';
import { EventKey } from '@/shared/utils/event';

import { AuthMetadataService } from '../auth-metadata.service';
import { ModulePermissions } from '../constants';
import { BaseAuthGuard } from './base-auth.guard';

export function HasModulePermissionGuard(...permissions: ModulePermissions[]): Type<any> {
	@Injectable()
	class HasModulePermissionMixin extends BaseAuthGuard {
		constructor(
			protected sessionService: SessionService,
			private authMetadataService: AuthMetadataService,
			private eventHelperService: EventHelperService,
		) {
			super(sessionService);
		}

		async canActivate(context: ExecutionContext): Promise<boolean> {
			const userSession = this._getUserSession(context);
			if (!userSession) {
				return false;
			}

			// God role has access to everything
			if (userSession.has_god_role) {
				return true;
			}

			const event_id = await this._getAccessedEventId(context);
			if (!event_id) {
				return false;
			}

			// Check if user owns the event directly
			if (userSession.events?.includes(event_id)) {
				return true;
			}

			// Check for shared event access and co-owner role
			const sharedEventAccess = userSession.shared_events?.[event_id];
			if (!sharedEventAccess?.role_co_owner) {
				return false;
			}

			// Co-owners must have at least one of the required permissions
			return permissions.some((permission) => sharedEventAccess.permissions?.[permission]);
		}

		private async _getAccessedEventId(context: ExecutionContext): Promise<number | null> {
			const eventKey = this._getEventKey(context);
			if (!eventKey) {
				return null;
			}

			const eventIdentifiers = await this.eventHelperService.getEventIdentifiers(eventKey);
			if (!eventIdentifiers) {
				return null;
			}

			return Number(eventIdentifiers.event_id);
		}

		private _getEventKey(context: ExecutionContext): EventKey | null {
			const args = this._getArgs(context);
			const propertyName = this.authMetadataService.getAccessedEventKeyPropertyName(context);

			const keyValue = args?.[propertyName];
			if (!EventKey.isValidKeyValue(keyValue)) {
				return null;
			}

			return EventKey.fromKeyValue(keyValue);
		}
	}

	return mixin(HasModulePermissionMixin);
}
