import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';

import { BaseAuthGuard } from './base-auth.guard';

@Injectable()
export class IsAuthenticatedGuard extends BaseAuthGuard {
	canActivate(context: ExecutionContext): boolean {
		const userSession = this._getUserSession(context);

		if (userSession?.activated && !userSession.is_api_auth) {
			return true;
		}

		throw new UnauthorizedException('Not authenticated');
	}
}
