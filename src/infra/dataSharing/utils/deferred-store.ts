type DidResolve = boolean;
export type DeferredResolverFunction<T> = (value: T) => DidResolve;

export interface DeferredPromise<T> {
	promise: Promise<T>;
	resolver: DeferredResolverFunction<T>;
	pending: boolean;
}

/**
 * Stores multiple deferred resolvers for type T, allowing them to resolve
 * asynchronously while still collecting their resolved values.
 */
export class DeferredStore<T> {
	private readonly _deferredPromises: DeferredPromise<T>[] = [];

	createDeferredResolver(): DeferredResolverFunction<T> {
		const entry = this._createDeferredPromise();
		this._deferredPromises.push(entry);
		return entry.resolver;
	}

	/**
	 * Awaits the resolution of all currently registered deferred Promises,
	 * including any new ones added while waiting.
	 */
	async getResolvedValues(): Promise<T[]> {
		return this._resolveAllPromises();
	}

	private _createDeferredPromise(): DeferredPromise<T> {
		let resolver: DeferredResolverFunction<T>;
		let isPending = true;

		const promise = new Promise<T>((resolve) => {
			resolver = (value: T) => {
				if (isPending) {
					isPending = false;
					resolve(value);
					return true; // Return true if this was the first resolution.
				} else {
					return false;
				}
			};
		});

		return {
			promise,
			resolver,
			get pending() {
				return isPending;
			},
		};
	}

	private async _resolveAllPromises(): Promise<T[]> {
		const promises = this._deferredPromises.map(({ promise }) => promise);
		const results = await Promise.all(promises);

		// If new or still-pending promises appeared during the await, recurse.
		const stillPending = this._deferredPromises.some(({ pending }) => pending);
		return stillPending ? this._resolveAllPromises() : results;
	}
}
