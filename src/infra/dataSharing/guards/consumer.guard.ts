import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { CONSUMING_DATA_TYPES_KEY } from '../constants';
import { DataSharingService } from '../dataSharing.service';
import { DataTypes } from '../types';

@Injectable()
export class ConsumerGuard implements CanActivate {
	constructor(private reflector: Reflector, private dataSharingService: DataSharingService) {}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		const dataTypes = this._getConsumingDataTypes(context);
		await this.dataSharingService.consume(dataTypes, context);
		return true;
	}

	private _getConsumingDataTypes(context: ExecutionContext): DataTypes[] {
		return this.reflector.getAllAndMerge<DataTypes[]>(CONSUMING_DATA_TYPES_KEY, [context.getHand<PERSON>(), context.getClass()]);
	}
}
