import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { PROVIDING_DATA_TYPES_KEY } from '../constants';
import { DataSharingService } from '../dataSharing.service';
import { DataTypes } from '../types';

@Injectable()
export class ProviderGuard implements CanActivate {
	constructor(private reflector: Reflector, private dataSharingService: DataSharingService) {}

	async canActivate(context: ExecutionContext): Promise<boolean> {
		const dataTypes = this._getProvidingDataTypes(context);
		await this.dataSharingService.registerHandlerResolvers(dataTypes, context);
		return true;
	}

	private _getProvidingDataTypes(context: ExecutionContext): DataTypes[] {
		return this.reflector.getAllAndMerge<DataTypes[]>(PROVIDING_DATA_TYPES_KEY, [context.getHandler(), context.getClass()]);
	}
}
