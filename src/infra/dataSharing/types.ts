import { EventOfficialSchedule } from '@/modules/eventOfficialSchedule/entities';
import { Match } from '@/modules/match/entities';

import { DeferredStore, DeferredResolverFunction } from './utils/deferred-store';

export type DeferredStoresMap = Map<DataTypes, DeferredStore<DataTypeValues[DataTypes]>>;
export type ResolvedValuesMap = Map<DataTypes, DataTypeValues[DataTypes][]>;

export type HandlerResolvers = DeferredResolverFunction<DataTypeValues[DataTypes]>[];
export type HandlerResolversStore = HandlerResolvers[];
export type HandlerResolversStoreMap = Map<unknown, HandlerResolversStore>;

export const enum DataTypes {
	Matches = 'matches',
	OfficialsSchedules = 'officialsSchedules',
}

export interface DataTypeValues {
	[DataTypes.Matches]: Match[];
	[DataTypes.OfficialsSchedules]: EventOfficialSchedule[];
}

export type DataProviders<Ts extends readonly DataTypes[]> = { [I in keyof Ts]: (value: DataTypeValues[Ts[I] & DataTypes]) => void };
export type ProvidedData<T extends DataTypes> = (DataTypeValues[T] | undefined)[];
