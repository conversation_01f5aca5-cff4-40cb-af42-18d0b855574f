import { ExecutionContext, Injectable } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

import { STORES_MAP_CONTEXT_KEY, RESOLVED_VALUES_MAP_CONTEXT_KEY, HANDLER_RESOLVERS_STORE_CONTEXT_KEY } from './constants';
import { DataTypes, DataTypeValues, HandlerResolvers, HandlerResolversStore, HandlerResolversStoreMap } from './types';
import { DeferredStoresMap, ResolvedValuesMap } from './types';
import { DeferredStore } from './utils/deferred-store';

@Injectable()
export class DataSharingService {
	async registerHandlerResolvers(dataTypes: DataTypes[], context: ExecutionContext): Promise<void> {
		const gqlContext = GqlExecutionContext.create(context).getContext();
		const resolvers = dataTypes.map((dataType) => {
			const store = this._getOrCreateDataTypeStore(dataType, gqlContext);
			return store.createDeferredResolver();
		});
		const handlerResolversStore = this._getOrCreateHandlerResolversStore(context.getHandler(), gqlContext);
		handlerResolversStore.push(resolvers);
		// Processing all resolvers within the same tick of the event loop
		await new Promise((resolve) => setImmediate(resolve));
	}

	getHandlerNextResolvers(context: ExecutionContext): HandlerResolvers {
		const gqlContext = GqlExecutionContext.create(context).getContext();
		const handlerResolversStore = this._getOrCreateHandlerResolversStore(context.getHandler(), gqlContext);
		return handlerResolversStore.shift();
	}

	cleanupResolvers(resolvers: HandlerResolvers, context: ExecutionContext): void {
		resolvers.forEach((resolver) => {
			const resolvedOnCleanup = resolver(undefined);
			if (resolvedOnCleanup) {
				const { fieldName } = GqlExecutionContext.create(context).getInfo();
				// TODO Log into Sentry
				console.warn(`Deferred resolver(s) for ${fieldName} was not resolved before cleanup`);
			}
		});
	}

	async consume(dataTypes: DataTypes[], context: ExecutionContext): Promise<void> {
		// Wait for all resolvers to be processed before consuming
		await new Promise((resolve) => setImmediate(resolve));

		const gqlContext = GqlExecutionContext.create(context).getContext();

		for (const dataType of dataTypes) {
			const store = this._getOrCreateDataTypeStore(dataType, gqlContext);
			const values = await store.getResolvedValues();
			this._setDataTypeValues(dataType, values, gqlContext);
		}
	}

	private _getOrCreateDataTypeStore(
		dataTypes: DataTypes,
		gqlContext: Record<string, DeferredStoresMap>,
	): DeferredStore<DataTypeValues[DataTypes]> {
		if (!(STORES_MAP_CONTEXT_KEY in gqlContext)) {
			gqlContext[STORES_MAP_CONTEXT_KEY] = new Map();
		}
		const storesMap = gqlContext[STORES_MAP_CONTEXT_KEY];
		if (!storesMap.has(dataTypes)) {
			storesMap.set(dataTypes, new DeferredStore());
		}
		return storesMap.get(dataTypes);
	}

	private _getOrCreateHandlerResolversStore(
		handlerFunction: unknown,
		gqlContext: Record<string, HandlerResolversStoreMap>,
	): HandlerResolversStore {
		if (!(HANDLER_RESOLVERS_STORE_CONTEXT_KEY in gqlContext)) {
			gqlContext[HANDLER_RESOLVERS_STORE_CONTEXT_KEY] = new Map();
		}
		const handlerResolversStoreMap = gqlContext[HANDLER_RESOLVERS_STORE_CONTEXT_KEY];
		if (!handlerResolversStoreMap.has(handlerFunction)) {
			handlerResolversStoreMap.set(handlerFunction, []);
		}
		return handlerResolversStoreMap.get(handlerFunction);
	}

	private _setDataTypeValues(
		dataType: DataTypes,
		values: DataTypeValues[DataTypes][],
		gqlContext: Record<string, ResolvedValuesMap>,
	): void {
		if (!(RESOLVED_VALUES_MAP_CONTEXT_KEY in gqlContext)) {
			gqlContext[RESOLVED_VALUES_MAP_CONTEXT_KEY] = new Map();
		}
		const resolvedValuesMap = gqlContext[RESOLVED_VALUES_MAP_CONTEXT_KEY];
		resolvedValuesMap.set(dataType, values);
	}
}
