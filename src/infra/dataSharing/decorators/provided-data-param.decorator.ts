import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

import { RESOLVED_VALUES_MAP_CONTEXT_KEY } from '../constants';
import { DataTypes, ResolvedValuesMap } from '../types';

export const ProvidedDataParam = createParamDecorator((entity: DataTypes, context: ExecutionContext) => {
	const gqlExecutionContext = GqlExecutionContext.create(context);
	const gqlContext = gqlExecutionContext.getContext<Record<string, ResolvedValuesMap>>();

	const resolvedValuesMap = gqlContext[RESOLVED_VALUES_MAP_CONTEXT_KEY];

	return resolvedValuesMap?.get(entity);
});
