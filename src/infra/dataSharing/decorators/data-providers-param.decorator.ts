import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

import { HANDLER_RESOLVERS_CONTEXT_KEY } from '../constants';
import { HandlerResolvers } from '../types';

export const DataProvidersParam = createParamDecorator((_, context: ExecutionContext) => {
	const gqlContext = GqlExecutionContext.create(context).getContext<Record<string, HandlerResolvers>>();

	const resolvers = gqlContext[HANDLER_RESOLVERS_CONTEXT_KEY];
	if (!resolvers) {
		throw new Error('Data provider not registered, make sure to use @ProvidesData decorator');
	}

	return resolvers;
});
