import { SetMetadata, UseGuards, applyDecorators, UseInterceptors } from '@nestjs/common';

import { PROVIDING_DATA_TYPES_KEY } from '../constants';
import { ProviderGuard } from '../guards';
import { ParamsProviderInterceptor } from '../interceptors';
import { DataTypes } from '../types';

export function ProvidesData(...dataTypes: DataTypes[]) {
	return applyDecorators(
		SetMetadata(PROVIDING_DATA_TYPES_KEY, dataTypes),
		UseGuards(ProviderGuard),
		UseInterceptors(ParamsProviderInterceptor),
	);
}
