import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { HANDLER_RESOLVERS_CONTEXT_KEY } from '../constants';
import { DataSharingService } from '../dataSharing.service';
import { HandlerResolvers } from '../types';

@Injectable()
export class ParamsProviderInterceptor implements NestInterceptor {
	constructor(private dataSharingService: DataSharingService) {}

	intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
		const resolvers = this.dataSharingService.getHandlerNextResolvers(context);
		const gqlContext = GqlExecutionContext.create(context).getContext<Record<string, HandlerResolvers>>();
		gqlContext[HANDLER_RESOLVERS_CONTEXT_KEY] = resolvers;

		const cleanup = () => this.dataSharingService.cleanupResolvers(resolvers, context);

		return next.handle().pipe(
			tap({
				next: cleanup,
				error: cleanup,
			}),
		);
	}
}
