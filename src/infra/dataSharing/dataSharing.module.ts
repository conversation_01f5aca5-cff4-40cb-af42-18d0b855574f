import { Module } from '@nestjs/common';

import { DataSharingService } from './dataSharing.service';
import { ProviderGuard, ConsumerGuard } from './guards';
import { ParamsProviderInterceptor } from './interceptors';

@Module({
	imports: [],
	providers: [DataSharingService, ParamsProviderInterceptor, ProviderGuard, ConsumerGuard],
	exports: [DataSharingService, ParamsProviderInterceptor, ProviderGuard, ConsumerGuard],
})
export class DataSharingModule {}
