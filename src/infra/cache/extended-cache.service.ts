import KeyvRedis, { KeyvRedisEntry, RedisClientType } from '@keyv/redis';
import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';

import { InjectRedisClient } from '@/infra/redis/decorators';
import { CacheCategories, CacheScopes } from '@/shared/constants/cache';

import {
	CACHE_DEFAULT_TTL,
	GET_BATCH_SIZE,
	SET_BATCH_SIZE,
	CLEAR_BATCH_SIZE,
	EXT_CACHE_SERVICE_NAMESPACE,
	EXT_CACHE_SERVICE_KEY_SEPARATOR,
} from './constants';

export type ItemsCacheKeyConfig = {
	scope: string;
	scopeKey: string;
	category: string;
	ttl?: number;
};

export type ItemCacheKeyConfig = ItemsCacheKeyConfig & {
	categoryKey: string;
};

@Injectable()
export class ExtendedCacheService {
	private readonly _store: KeyvRedis<unknown>;

	constructor(@InjectRedisClient() redis: RedisClientType) {
		this._store = new KeyvRedis(redis, {
			namespace: EXT_CACHE_SERVICE_NAMESPACE,
			keyPrefixSeparator: EXT_CACHE_SERVICE_KEY_SEPARATOR,
			clearBatchSize: CLEAR_BATCH_SIZE,
		});
	}

	private _getCacheKey(config: ItemCacheKeyConfig, defaultValue = 'none'): string {
		const { scope = defaultValue, scopeKey = defaultValue, category = defaultValue, categoryKey = defaultValue } = config;
		return [scope, scopeKey, category, categoryKey].filter(Boolean).join(EXT_CACHE_SERVICE_KEY_SEPARATOR);
	}

	private _getTTL({ ttl }: ItemsCacheKeyConfig): number | undefined {
		return ttl === 0 ? undefined : ttl || CACHE_DEFAULT_TTL;
	}

	private async _deleteAllByCacheKey(cacheKey: string): Promise<void> {
		// Applying the namespace to get key in a format "esw:ecs:event:22343:match"
		const keyPrefix = this._store.createKeyPrefix(cacheKey, EXT_CACHE_SERVICE_NAMESPACE);
		// TODO Replace with Array.fromAsync when available
		const keys: string[] = [];
		for await (const [recordKey] of this._store.iterator(keyPrefix)) {
			// Received key is in a format "next:1234" and appending the key prefix to get the full key
			keys.push(`${cacheKey}:${recordKey}`);
		}
		// Deleting all keys in a single batch
		await this._store.deleteMany(keys);
	}

	setItem(config: ItemCacheKeyConfig, data: unknown): Promise<void> {
		return this._store.set(this._getCacheKey(config), JSON.stringify(data), this._getTTL(config));
	}

	async setItems(config: ItemsCacheKeyConfig, items: Record<string, unknown>): Promise<void> {
		const ttl = this._getTTL(config);
		const entries = Object.entries(items).map<KeyvRedisEntry<string>>(([categoryKey, data]) => ({
			key: this._getCacheKey({ ...config, categoryKey }),
			value: JSON.stringify(data),
			ttl,
		}));
		// Process all items in chunks to avoid exceeding the Redis command length limit
		await Promise.all(_.chunk(entries, SET_BATCH_SIZE).map((chunk) => this._store.setMany(chunk)));
	}

	async getItem<T>(config: ItemCacheKeyConfig): Promise<T | null> {
		const data = await this._store.get(this._getCacheKey(config));
		return data ? JSON.parse(data as string) : null;
	}

	async getItems<T>(config: ItemsCacheKeyConfig, categoryKeys: string[]): Promise<[Record<string, T>, string[]]> {
		const items: Record<string, T> = {};
		const missingCategoryKeys: string[] = [];
		await Promise.all(
			_.chunk(categoryKeys, GET_BATCH_SIZE).map(async (categoryKeysChunk) => {
				const keys = categoryKeysChunk.map((categoryKey) => this._getCacheKey({ ...config, categoryKey }));
				const results = await this._store.getMany(keys);
				results.forEach((result, index) => {
					const categoryKey = categoryKeysChunk[index];
					if (result) {
						items[categoryKey] = JSON.parse(result as string);
					} else {
						missingCategoryKeys.push(categoryKey);
					}
				});
			}),
		);
		return [items, missingCategoryKeys];
	}

	async getOrFetch<T>(config: ItemCacheKeyConfig | null, fetchCallback: () => Promise<T>): Promise<T> {
		if (!config) {
			return fetchCallback(); // No caching if config is null
		}

		const item = await this.getItem<T>(config);
		if (item) return item;

		const fetchedItem = await fetchCallback();
		await this.setItem(config, fetchedItem);
		return fetchedItem;
	}

	async getOrFetchMany<T>(
		config: ItemsCacheKeyConfig | null,
		categoryKeys: string[],
		fetchCallback: (missingKeys: string[]) => Promise<Record<string, T>>,
	): Promise<Record<string, T>> {
		if (!config) {
			return fetchCallback(categoryKeys); // No caching if config is null
		}

		const [cachedItems, missingKeys] = await this.getItems<T>(config, categoryKeys);
		if (missingKeys.length === 0) {
			return cachedItems;
		}

		const fetchedItems = await fetchCallback(missingKeys);
		await this.setItems(config, fetchedItems);
		return { ...cachedItems, ...fetchedItems };
	}

	clearCategory(scope: CacheScopes, scopeKey: string, category: CacheCategories): Promise<void> {
		const cacheKey = this._getCacheKey({ scope, scopeKey, category, categoryKey: '' }, '');
		return this._deleteAllByCacheKey(cacheKey);
	}

	clearScope(scope: CacheScopes, scopeKey = ''): Promise<void> {
		const cacheKey = this._getCacheKey({ scope, scopeKey, category: '', categoryKey: '' }, '');
		return this._deleteAllByCacheKey(cacheKey);
	}

	clearAll(): Promise<void> {
		return this._store.clear();
	}
}
