import configuration from '@/infra/configuration';

const { REDIS_APP_NAMESPACE, REDIS_KEY_SEPARATOR } = configuration;

export const GET_BATCH_SIZE = 200;
export const SET_BATCH_SIZE = 100;
export const CLEAR_BATCH_SIZE = 500;
export const CACHE_DEFAULT_TTL = configuration.CACHE_DEFAULT_TTL;

export const CACHE_SERVICE_NAMESPACE = REDIS_APP_NAMESPACE + ':cs';
export const CACHE_SERVICE_KEY_SEPARATOR = REDIS_KEY_SEPARATOR;

export const EXT_CACHE_SERVICE_NAMESPACE = REDIS_APP_NAMESPACE + ':ecs';
export const EXT_CACHE_SERVICE_KEY_SEPARATOR = REDIS_KEY_SEPARATOR;
