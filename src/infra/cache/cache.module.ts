import { createKeyv, RedisClientType } from '@keyv/redis';
import { CacheModule as CacheDefaultModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';

import configuration from '@/infra/configuration';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { REDIS_CLIENT } from '@/infra/redis/constants';

import { CacheController } from './cache.controller';
import { CacheService } from './cache.service';
import { ExtendedCacheService } from './extended-cache.service';

const { CACHE_DEFAULT_TTL, REDIS_APP_NAMESPACE, REDIS_KEY_SEPARATOR } = configuration;

// TODO Add global
@Module({
	imports: [
		CacheDefaultModule.registerAsync({
			useFactory: async (redisClient: RedisClientType) => {
				return {
					isGlobal: true,
					stores: [
						// Default store for cache-manager
						createKeyv(redisClient, {
							namespace: REDIS_APP_NAMESPACE,
							keyPrefixSeparator: REDIS_KEY_SEPARATOR,
						}),
					],
					ttl: CACHE_DEFAULT_TTL,
				};
			},
			inject: [REDIS_CLIENT],
		}),
		EventHelperModule,
	],
	providers: [CacheService, ExtendedCacheService],
	exports: [CacheService, ExtendedCacheService],
	controllers: [CacheController],
})
export class CacheModule {}
