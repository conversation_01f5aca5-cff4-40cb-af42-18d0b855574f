import { Controller, Delete, Get, Param, Query } from '@nestjs/common';

import { EventHelperService } from '@/infra/eventHelper/eventHelper.service';
import { CacheCategories, CacheCommonKeys, CacheScopes } from '@/shared/constants/cache';
import { EventKey } from '@/shared/utils/event';

import { CacheService } from './cache.service';
import { EventKeyValueParamDto } from './dto/event-key-value-param.dto';
import { ExtendedCacheService } from './extended-cache.service';

@Controller('cache')
export class CacheController {
	constructor(
		private cacheService: CacheService,
		private extendedCacheService: ExtendedCacheService,
		private eventHelperService: EventHelperService,
	) {}

	@Delete('event/list')
	async clearEventsList() {
		try {
			await this.extendedCacheService.clearScope(CacheScopes.Event, CacheCommonKeys.All);
			return { msg: 'Cache cleared for events list' };
		} catch (e) {
			return { msg: 'Error clearing cache for events list' };
		}
	}

	@Delete('event/:eventKey')
	async clearEvent(@Param() param: EventKeyValueParamDto) {
		const eventKey = EventKey.fromKeyValue(param.eventKey);
		const eventIdentifiers = await this.eventHelperService.getEventIdentifiers(eventKey);
		if (!eventIdentifiers) {
			return { msg: `Event not found for key: ${eventKey.value}` };
		}
		const { event_id, esw_id } = eventIdentifiers;

		try {
			await Promise.all([
				this.cacheService.clearEvent(event_id),
				this.cacheService.clearEvent(esw_id),
				this.extendedCacheService.clearScope(CacheScopes.Event, event_id),
				this.extendedCacheService.clearScope(CacheScopes.Event, esw_id),
			]);
			return { msg: `Cache cleared for event: ${esw_id} / ${event_id}` };
		} catch (e) {
			console.log(e);
			return { msg: `Error clearing cache for event: ${esw_id} / ${event_id}` };
		}
	}

	@Delete('event/:eventId/matches')
	async clearEventMatches(@Param() param: EventKeyValueParamDto) {
		const eventKey = EventKey.fromKeyValue(param.eventKey);
		const eventIdentifiers = await this.eventHelperService.getEventIdentifiers(eventKey);
		if (!eventIdentifiers) {
			return { msg: `Event not found for key: ${eventKey.value}` };
		}
		const { event_id, esw_id } = eventIdentifiers;

		try {
			await Promise.all([
				this.extendedCacheService.clearCategory(CacheScopes.Event, event_id, CacheCategories.Match),
				this.extendedCacheService.clearCategory(CacheScopes.Event, esw_id, CacheCategories.Match),
			]);
			return { msg: `Cache cleared for event matches: ${esw_id} / ${event_id}` };
		} catch (e) {
			return { msg: `Error clearing cache for event matches: ${esw_id} / ${event_id}` };
		}
	}

	@Delete('all')
	async clearAll() {
		try {
			await Promise.all([this.cacheService.clearAll(), this.extendedCacheService.clearAll()]);
			return { msg: 'All cache cleared' };
		} catch (e) {
			return { msg: 'Error clearing all cache' };
		}
	}

	@Get()
	async getKeys(@Query('eventid') eventId: string) {
		return this.cacheService.getKeys(eventId);
	}
}
