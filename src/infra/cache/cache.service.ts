import KeyvRedis, { RedisClientType } from '@keyv/redis';
import { Injectable } from '@nestjs/common';

import { InjectRedisClient } from '@/infra/redis/decorators';

import { CLEAR_BATCH_SIZE, CACHE_SERVICE_NAMESPACE, CACHE_SERVICE_KEY_SEPARATOR, CACHE_DEFAULT_TTL } from './constants';

declare global {
	interface BigInt {
		toJSON?(): string;
	}
}
BigInt.prototype.toJSON = function () {
	const int = Number.parseInt(this.toString());
	return int ?? this.toString();
};

@Injectable()
export class CacheService {
	private readonly _store: KeyvRedis<unknown>;

	constructor(@InjectRedisClient() redis: RedisClientType) {
		this._store = new KeyvRedis(redis, {
			namespace: CACHE_SERVICE_NAMESPACE,
			keyPrefixSeparator: CACHE_SERVICE_KEY_SEPARATOR,
			clearBatchSize: CLEAR_BATCH_SIZE,
		});
	}

	private getCacheKey(methodName: string, id: string) {
		if (id) {
			return `${methodName}_${id}`;
		}
		return methodName;
	}

	async getCache(methodName: string, params?: Record<any, any>) {
		const data = await this._store.get(this.getCacheKey(methodName, JSON.stringify(params)));
		return data ? JSON.parse(data as string) : null;
	}

	setCache(data: any, methodName: string, params?: Record<any, any>) {
		return this._store.set(this.getCacheKey(methodName, JSON.stringify(params)), JSON.stringify(data), CACHE_DEFAULT_TTL);
	}

	async getKeys(eventId: string): Promise<string[]> {
		const keys: string[] = [];
		const searchValue = `"eventId":"${eventId}"`;
		for await (const [recordKey] of this._store.iterator(CACHE_SERVICE_NAMESPACE)) {
			if (recordKey.includes(searchValue)) {
				keys.push(recordKey);
			}
		}
		return keys;
	}

	async clearEvent(eventId: string) {
		const keys = await this.getKeys(eventId);
		await this._store.deleteMany(keys);
		return { msg: 'Keys for event: ' + eventId + ' removed successfully' };
	}

	async clearAll() {
		await this._store.clear();
		return { msg: 'All keys removed successfully' };
	}
}
