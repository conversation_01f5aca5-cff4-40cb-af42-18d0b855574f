import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export const getEventIdentifiersQuery = (eventKey: EventKey): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	let query = 'SELECT e.event_id, e.esw_id FROM event e WHERE ';

	if (eventKey.isESWID) {
		query += `e.esw_id = ${sqlVars.useValue(eventKey.value)}`;
	} else {
		query += `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`;
	}

	return [query, sqlVars.getValues()];
};
