import configuration from '@/infra/configuration';

const { REDIS_APP_NAMESPACE, REDIS_KEY_SEPARATOR } = configuration;

export const HELPER_SERVICE_NAMESPACE = REDIS_APP_NAMESPACE + ':ehs';
export const HELPER_SERVICE_KEY_SEPARATOR = REDIS_KEY_SEPARATOR;
export const EIDS_KEY_PREFIX = 'eids' + REDIS_KEY_SEPARATOR;
export const EIDS_NO_RESULT_TTL = 60000; // 1 minute
export const EIDS_RESULT_TTL = 24 * 60 * 60 * 1000; // 24 hours
