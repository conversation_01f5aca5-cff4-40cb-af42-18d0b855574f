import KeyvRedis, { RedisClientType } from '@keyv/redis';
import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { InjectRedisClient } from '@/infra/redis/decorators';
import { EventKey } from '@/shared/utils/event';

import { HELPER_SERVICE_NAMESPACE, HELPER_SERVICE_KEY_SEPARATOR, EIDS_RESULT_TTL, EIDS_NO_RESULT_TTL, EIDS_KEY_PREFIX } from './constants';
import { getEventIdentifiersQuery } from './queries/event-identifiers.query';
import { EventIdentifiers } from './types';

@Injectable()
export class EventHelperService {
	private readonly _store: KeyvRedis<unknown>;

	constructor(@InjectRedisClient() redis: RedisClientType, private prisma: PrismaService) {
		this._store = new KeyvRedis(redis, {
			namespace: HELPER_SERVICE_NAMESPACE,
			keyPrefixSeparator: HELPER_SERVICE_KEY_SEPARATOR,
		});
	}

	async getEventIdentifiers(eventKey: EventKey): Promise<EventIdentifiers | null> {
		const cacheKey = this._getCacheKey(eventKey.value);
		const cacheEids = await this._getEidsFromCache(cacheKey);
		if (cacheEids !== undefined) {
			return cacheEids;
		}

		const dbEids = await this._getEidsFromDB(eventKey);
		if (!dbEids) {
			await this._setEidsNoResultCache(cacheKey);
			return null;
		}
		await this._setEidsCache(dbEids);
		return dbEids;
	}

	private _getCacheKey(keyValue: string): string {
		return `${EIDS_KEY_PREFIX}${keyValue}`;
	}

	private async _getEidsFromCache(cacheKey: string): Promise<EventIdentifiers | null | undefined> {
		const eIds = await this._store.get(cacheKey);
		if (eIds === undefined) {
			return undefined;
		}
		return JSON.parse(eIds as string) as EventIdentifiers | null;
	}

	private _setEidsCache(eventIdentifiers: EventIdentifiers): Promise<void> {
		const serializedEids = JSON.stringify(eventIdentifiers);
		return this._store.setMany(
			Object.values(eventIdentifiers).map((keyValue) => ({
				key: this._getCacheKey(keyValue),
				value: serializedEids,
				ttl: EIDS_RESULT_TTL,
			})),
		);
	}

	private _setEidsNoResultCache(cacheKey: string): Promise<void> {
		// Apply a short TTL to prevent the database from being hit too frequently
		return this._store.set(cacheKey, JSON.stringify(null), EIDS_NO_RESULT_TTL);
	}

	private async _getEidsFromDB(eventKey: EventKey): Promise<EventIdentifiers | null> {
		const [query, values] = getEventIdentifiersQuery(eventKey);
		const results = await this.prisma.$queryRawUnsafe<EventIdentifiers[]>(query, ...values);
		if (!results?.length) return null;
		return {
			event_id: String(results[0].event_id),
			esw_id: results[0].esw_id,
		};
	}
}
