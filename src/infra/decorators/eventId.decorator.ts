import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

export const EventId = createParamDecorator((_, context: ExecutionContext) => {
	const ctx = GqlExecutionContext.create(context);
	const args = ctx.getArgs();
	const _isESWID = /^[0-9A-F]{9}$/i.test(args.id);
	const eventID = _isESWID ? (args.id as string) : parseInt(args.id, 10);
	return {
		eventID,
		isESWID: _isESWID,
	};
});
