import './instrument'; // Should be imported first
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import * as compression from 'compression';

import configuration from '@/infra/configuration';
import { AppModule } from '@/infra/framework/app.module';

const { PORT } = configuration;

async function bootstrap() {
	const app = await NestFactory.create(AppModule);
	app.enableCors({
		origin: '*',
		credentials: true,
	});
	app.use(
		compression({
			level: 8,
			threshold: 1024,
		}),
	);
	app.useGlobalPipes(new ValidationPipe());

	app.setGlobalPrefix('/api/esw/');
	await app.listen(PORT);
}
bootstrap();
