import { ApolloServerPlugin, GraphQLRequestContextDidEncounterErrors } from '@apollo/server';
import * as Sentry from '@sentry/node';

import { GraphQLSentryError } from './graphql-sentry.error';

export class GraphQLSentryPlugin<T> implements ApolloServerPlugin {
	async requestDidStart() {
		return {
			async didEncounterErrors(context: GraphQLRequestContextDidEncounterErrors<T>) {
				if (!context || !context.errors) return;
				context.errors.forEach((error) => {
					const customError = new GraphQLSentryError<T>(error, context);
					const { level, type, operationName, message } = customError;
					Sentry.captureException(customError, {
						level,
						tags: {
							operationName,
							type,
						},
						fingerprint: [operationName, type, message],
					});
				});
			},
		};
	}
}
