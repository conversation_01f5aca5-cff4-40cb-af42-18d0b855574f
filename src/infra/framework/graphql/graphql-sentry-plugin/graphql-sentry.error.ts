import { GraphQLRequestContextDidEncounterErrors } from '@apollo/server';
import { BadRequestException } from '@nestjs/common';
import { SeverityLevel } from '@sentry/nestjs';
import { GraphQLError } from 'graphql/error';

export class GraphQLSentryError<T> extends Error {
	public type: string;
	public operationName: string;
	public level: SeverityLevel;
	public originalError: Error;

	constructor(gqlError: GraphQLError, context: GraphQLRequestContextDidEncounterErrors<T>) {
		const originalError = gqlError.originalError || gqlError;
		const operationName = context.request.operationName || 'UnnamedOperation';
		let type = 'InternalServerError';
		let level: SeverityLevel = 'error';
		let message = originalError.message;
		if (originalError instanceof BadRequestException) {
			type = 'BadRequestException';
			level = 'warning';
			const response = originalError.getResponse();
			if (typeof response === 'object') {
				message = String(response['message']);
			}
		} else if (originalError instanceof GraphQLError) {
			type = 'GraphQLError';
			level = 'warning';
		}

		super(message);
		this.type = type;
		this.operationName = operationName;
		this.level = level;
		this.originalError = originalError;
		this.stack = originalError.stack;
		this.name = `${type} (${operationName})`;
	}
}
