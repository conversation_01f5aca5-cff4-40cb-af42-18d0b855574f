import { BadRequestException } from '@nestjs/common';
import type { GraphQLFormattedError } from 'graphql';
import { GraphQLError } from 'graphql/error';

import configuration from '@/infra/configuration';

const { IS_LOCAL } = configuration;

export const formatGraphQLError = (formattedError: GraphQLFormattedError, unknownError: unknown): GraphQLFormattedError => {
	// Doing nothing if we are in development
	if (IS_LOCAL) return formattedError;
	const { message, extensions = {} } = formattedError;
	const { code, originalError } = extensions;

	const error = unknownError instanceof GraphQLError ? unknownError.originalError || unknownError : unknownError;

	if (error instanceof BadRequestException) {
		return {
			message,
			extensions: {
				code,
				originalError: {
					message: (originalError as Record<string, string>)?.message,
				},
			},
		};
	} else if (error instanceof GraphQLError) {
		return {
			message,
			extensions: {
				code,
			},
		};
	}

	return {
		message: 'Internal server error',
		extensions: { code },
	};
};
