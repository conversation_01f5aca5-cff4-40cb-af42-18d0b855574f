import { join } from 'path';

import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { GraphQLModule } from '@nestjs/graphql';
import { SentryModule, SentryGlobalFilter } from '@sentry/nestjs/setup';

import { AuthModule } from '@/infra/auth/auth.module';
import { CacheModule } from '@/infra/cache/cache.module';
import { DataSharingModule } from '@/infra/dataSharing/dataSharing.module';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { RedisModule } from '@/infra/redis/redis.module';
import { AthleteModule } from '@/modules/athlete/athlete.module';
import { ClubModule } from '@/modules/club/club.module';
import { CourtModule } from '@/modules/court/court.module';
import { DivisionModule } from '@/modules/division/division.module';
import { DivisionStandingModule } from '@/modules/divisionStanding/division-standing.module';
import { EventModule } from '@/modules/event/event.module';
import { EventMediaModule } from '@/modules/eventMedia/event-media.module';
import { EventOfficialModule } from '@/modules/eventOfficial/event-official.module';
import { EventOfficialScheduleModule } from '@/modules/eventOfficialSchedule/event-official-schedule.module';
import { MatchModule } from '@/modules/match/match.module';
import { MatchReferenceModule } from '@/modules/matchReference/match-reference.module';
import { RosterModule } from '@/modules/roster/roster.module';
import { RoundModule } from '@/modules/round/round.module';
import { StaffModule } from '@/modules/staff/staff.module';
import { TeamModule } from '@/modules/team/team.module';

import { formatGraphQLError } from './graphql/graphql-format';
import { GraphQLSentryPlugin } from './graphql/graphql-sentry-plugin';

@Module({
	imports: [
		SentryModule.forRoot(),
		RedisModule,
		AuthModule,
		GraphQLModule.forRoot<ApolloDriverConfig>({
			driver: ApolloDriver,
			path: '/api/esw/graphql',
			autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
			plugins: [new GraphQLSentryPlugin()],
			formatError: formatGraphQLError,
			fieldResolverEnhancers: ['guards'],
		}),
		DataSharingModule,
		CacheModule,
		ClubModule,
		EventModule,
		EventHelperModule,
		RosterModule,
		TeamModule,
		MatchModule,
		MatchReferenceModule,
		DivisionStandingModule,
		AthleteModule,
		StaffModule,
		DivisionModule,
		RoundModule,
		CourtModule,
		EventMediaModule,
		EventOfficialModule,
		EventOfficialScheduleModule,
	],
	providers: [
		{
			provide: APP_FILTER,
			useClass: SentryGlobalFilter,
		},
	],
})
export class AppModule {}
