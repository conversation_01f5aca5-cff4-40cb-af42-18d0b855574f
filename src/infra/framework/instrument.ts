import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';

import configuration from '@/infra/configuration';

const { SENTRY_DSN, SENTRY_RELEASE, APP_ENV, IS_LOCAL } = configuration;

Sentry.init({
	dsn: SENTRY_DSN,
	release: SENTRY_RELEASE,
	environment: APP_ENV,
	integrations: [
		nodeProfilingIntegration(),
		Sentry.extraErrorDataIntegration(),
		Sentry.graphqlIntegration(),
		Sentry.dataloaderIntegration(),
	],
	tracesSampleRate: 0.005,
	profileSessionSampleRate: 0.005,
	profileLifecycle: 'trace',
	enabled: !IS_LOCAL,
	debug: false,
});
