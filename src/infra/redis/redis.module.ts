import { createClient, RedisClientType } from '@keyv/redis';
import { Global, Module } from '@nestjs/common';

import configuration from '@/infra/configuration';

import {
	REDIS_CLIENT,
	REDIS_SESSION_CLIENT,
	REDIS_LOCK,
	REDIS_LOCK_RETRY_DELAY,
	REDIS_RECONNECT_DELAY_STEP,
	REDIS_RECONNECT_DELAY_MAX,
} from './constants';
import { RedisLockManager } from './utils/lockManager';

const { REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_SESSION_HOST, REDIS_SESSION_PORT, REDIS_SESSION_DB } = configuration;
const reconnectStrategy = (times: number) => Math.min(times * REDIS_RECONNECT_DELAY_STEP, REDIS_RECONNECT_DELAY_MAX);

@Global()
@Module({
	imports: [],
	providers: [
		{
			provide: REDIS_CLIENT,
			useFactory: async () => {
				const client = createClient({
					socket: {
						host: REDIS_HOST,
						port: REDIS_PORT,
						reconnectStrategy,
					},
					database: REDIS_DB,
				});
				client.on('error', console.error);
				await client.connect();
				return client;
			},
		},
		{
			provide: REDIS_SESSION_CLIENT,
			useFactory: async () => {
				const client = createClient({
					socket: {
						host: REDIS_SESSION_HOST,
						port: REDIS_SESSION_PORT,
						reconnectStrategy,
					},
					database: REDIS_SESSION_DB,
				});
				client.on('error', console.error);
				await client.connect();
				return client;
			},
		},
		{
			provide: REDIS_LOCK,
			useFactory: (redisClient: RedisClientType) => {
				return new RedisLockManager(redisClient, REDIS_LOCK_RETRY_DELAY);
			},
			inject: [REDIS_CLIENT],
		},
	],
	exports: [REDIS_CLIENT, REDIS_SESSION_CLIENT, REDIS_LOCK],
})
export class RedisModule {}
