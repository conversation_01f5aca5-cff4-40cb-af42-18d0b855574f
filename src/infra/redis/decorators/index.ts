import { Inject } from '@nestjs/common';

import { REDIS_CLIENT, REDIS_SESSION_CLIENT, REDIS_LOCK } from '../constants';

export const InjectRedisClient = (): ParameterDecorator => {
	return Inject(REDIS_CLIENT);
};

export const InjectRedisSessionClient = (): ParameterDecorator => {
	return Inject(REDIS_SESSION_CLIENT);
};

export const InjectRedisLock = (): ParameterDecorator => {
	return Inject(REDIS_LOCK);
};
