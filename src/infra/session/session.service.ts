import { ExecutionContext, Injectable } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

import { ExpressGqlContext, Session, UserSession } from './types';

@Injectable()
export class SessionService {
	getUserSessionFromContext(context: ExecutionContext): UserSession | null {
		if (context.getType<string>() === 'graphql') {
			return this._getUserSessionFromGqlContext(context);
		}
		return null;
	}

	private _getUserSessionFromGqlContext(context: ExecutionContext): UserSession | null {
		return this._getSessionFromGqlContext(context)?.passport?.user || null;
	}

	private _getSessionFromGqlContext(context: ExecutionContext): Session | null {
		const gqlContext = GqlExecutionContext.create(context).getContext();
		if (!this._isValidGqlContext(gqlContext)) return null;
		return gqlContext.req.session || null;
	}

	private _isValidGqlContext(gqlContext: unknown): gqlContext is ExpressGqlContext {
		return gqlContext && typeof gqlContext === 'object' && 'req' in gqlContext;
	}
}
