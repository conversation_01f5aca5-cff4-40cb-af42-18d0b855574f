import { Request } from 'express';

declare module 'express-session' {
	interface SessionData {
		passport?: {
			user?: UserSession;
		};
	}
}

export type ExpressGqlContext = {
	req: Request;
};

export type Session = Request['session'];

export type UserSession = {
	user_id: number;
	activated: boolean;
	is_api_auth: boolean;
	role_staff: boolean;
	has_god_role: boolean;
	official_id: number;
	event_owner_id: number;
	events: number[];
	shared_events: Record<string, SharedEventOptions>;
};

type SharedEventOptions = {
	role_co_owner: boolean;
	permissions: Record<string, boolean>;
};
