import { RedisClientType } from '@keyv/redis';
import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { RedisStore } from 'connect-redis';
import * as session from 'express-session';

import configuration from '@/infra/configuration';
import { InjectRedisSessionClient } from '@/infra/redis/decorators';

import { COOKIE_MAX_AGE } from './constants';
import { SessionService } from './session.service';

const { SESSION_PREFIX, SESSION_COOKIE_KEY, SESSION_SECRET } = configuration;

@Module({
	imports: [],
	providers: [SessionService],
	exports: [SessionService],
})
export class SessionModule implements NestModule {
	constructor(@InjectRedisSessionClient() private redisClient: RedisClientType) {}

	configure(consumer: MiddlewareConsumer) {
		const redisStore = new RedisStore({
			client: this.redisClient,
			prefix: SESSION_PREFIX,
		});

		consumer
			.apply(
				session({
					store: redisStore,
					resave: false,
					saveUninitialized: false,
					name: SESSION_COOKIE_KEY,
					secret: SESSION_SECRET,
					cookie: {
						maxAge: COOKIE_MAX_AGE,
					},
				}),
			)
			.forRoutes('*any');
	}
}
