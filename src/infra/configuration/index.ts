import { config } from 'dotenv';
const APP_ENV = process.env.APP_ENV || 'local';
config({ path: `.env.${APP_ENV}` });

const configuration = {
	APP_ENV,
	IS_LOCAL: APP_ENV === 'local',
	PORT: 3080,
	POSTGRES_USER: process.env.POSTGRES_USER,
	POSTGRES_HOST: process.env.POSTGRES_HOST,
	POSTGRES_DB: process.env.POSTGRES_DB,
	POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD,
	POSTGRES_PORT: parseInt(process.env.POSTGRES_PORT, 10),
	REDIS_HOST: process.env.REDIS_HOST,
	REDIS_PORT: parseInt(process.env.REDIS_PORT, 10),
	REDIS_DB: parseInt(process.env.REDIS_DB, 10) || 0,
	REDIS_APP_NAMESPACE: 'esw',
	REDIS_KEY_SEPARATOR: ':',
	CACHE_DEFAULT_TTL: parseInt(process.env.REDIS_TTL, 10) || 300000, // 5 minutes
	REDIS_SESSION_HOST: process.env.REDIS_SESSION_HOST || process.env.REDIS_HOST,
	REDIS_SESSION_PORT: parseInt(process.env.REDIS_SESSION_PORT || process.env.REDIS_PORT, 10),
	REDIS_SESSION_DB: parseInt(process.env.REDIS_SESSION_DB, 10) || 1,
	SESSION_PREFIX: process.env.SESSION_PREFIX || 'sess-dev:',
	SESSION_SECRET: process.env.SESSION_SECRET,
	SESSION_COOKIE_KEY: process.env.SESSION_COOKIE_KEY || 'sw.sid',
	SENTRY_DSN: process.env.SENTRY_DSN,
	SENTRY_RELEASE: process.env.SENTRY_RELEASE,
	USE_SEARCH_SERVICES: process.env.USE_SEARCH_SERVICES === 'true',
} as const;

export default configuration;
