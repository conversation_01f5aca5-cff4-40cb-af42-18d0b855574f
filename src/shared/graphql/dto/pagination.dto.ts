import { ArgsType, Field } from '@nestjs/graphql';
import { <PERSON>I<PERSON>, <PERSON>, <PERSON> } from 'class-validator';

@ArgsType()
export class PaginationInputDto {
	@Field(() => Number)
	@IsInt()
	@Min(1, { message: '"page" must be a positive integer greater than 0' })
	page: number;

	@Field(() => Number)
	@IsInt()
	@Min(1, { message: '"pageSize" must be a positive integer greater than 0' })
	@Max(20, { message: '"pageSize" must be less than or equal to 20' })
	pageSize: number;
}
