import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import { EventKey } from '@/shared/utils/event';

export const IsEventKeyValue = (validationOptions?: ValidationOptions) => {
	return (object: any, propertyName: string) => {
		registerDecorator({
			name: 'IsEventKeyValue',
			target: object.constructor,
			propertyName: propertyName,
			options: validationOptions,
			validator: {
				validate(value: any) {
					return EventKey.isValidKeyValue(value);
				},
				defaultMessage(validationArguments?: ValidationArguments): string {
					return `"${validationArguments.property}" must be either a valid ESW ID or a numeric event ID string`;
				},
			},
		});
	};
};
