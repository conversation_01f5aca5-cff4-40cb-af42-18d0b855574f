import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import { isNumericId } from '@/shared/utils/event';

export const IsNumericId = (validationOptions?: ValidationOptions) => {
	return (object: any, propertyName: string) => {
		registerDecorator({
			name: 'isNumericId',
			target: object.constructor,
			propertyName: propertyName,
			options: validationOptions,
			validator: {
				validate(value: any) {
					return isNumericId(value);
				},
				defaultMessage(validationArguments?: ValidationArguments): string {
					return `"${validationArguments.property}" must be a valid numeric ID`;
				},
			},
		});
	};
};
