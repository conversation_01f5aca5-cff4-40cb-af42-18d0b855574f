import { registerDecorator } from 'class-validator';

const DEFAULT_MAX_LENGTH = 100;

export const IsSearchField = (minLength: number, maxLength = DEFAULT_MAX_LENGTH) => {
	return (object: any, propertyName: string) => {
		registerDecorator({
			name: 'isSearchField',
			target: object.constructor,
			propertyName,
			validator: {
				validate(value: any): boolean {
					if (typeof value !== 'string') return false;
					if (!value) return true; // Empty string is allowed
					const { length } = value.trim();
					return length >= minLength && length <= maxLength;
				},
				defaultMessage(): string {
					return `"${propertyName}" must be a string whose length is between ${minLength} and ${maxLength} characters`;
				},
			},
		});
	};
};
