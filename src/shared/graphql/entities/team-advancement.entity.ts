import { Field, ObjectType } from '@nestjs/graphql';

import { MatchReference } from './match-reference.entity';

@ObjectType()
export class TeamAdvancement {
	@Field({ nullable: true })
	team_name?: string;

	@Field({ nullable: true })
	team_id?: number;

	@Field(() => MatchReference, { nullable: true })
	next_match?: MatchReference;

	@Field(() => MatchReference, { nullable: true })
	next_ref?: MatchReference;
}
