import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class MatchReference {
	@Field({ nullable: true })
	court?: string;

	@Field({ nullable: true })
	match_id?: string;

	@Field({ nullable: true })
	week_day?: string;

	@Field({ nullable: true })
	secs_start?: number;

	@Field({ nullable: true })
	display_name?: string;

	@Field({ nullable: true })
	start_time_string?: string;
}
