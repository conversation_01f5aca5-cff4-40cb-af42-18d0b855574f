const isESWID = (id: unknown): boolean => {
	return typeof id === 'string' && /^[0-9A-F]{9}$/i.test(id);
};

export const isNumericId = (id: unknown): boolean => {
	return typeof id === 'string' && /^\d{1,9}$/.test(id);
};

export class EventKey {
	readonly isESWID: boolean;

	static isValidKeyValue(value: unknown): value is string {
		return isESWID(value) || isNumericId(value);
	}

	static fromKeyValue(eventKey: string): EventKey {
		return new EventKey(eventKey);
	}

	protected constructor(public readonly value: string) {
		if (!EventKey.isValidKeyValue(value)) {
			throw new Error(`"${value}" is not a valid event key value`);
		}
		this.isESWID = isESWID(value);
	}
}
