import { PageInfo } from '@/shared/graphql/entities/page-info.entity';

export type PageParams = {
	page: number;
	pageSize: number;
};

export type PageResults<T> = {
	items: T[];
	itemCount: number;
};

export const createPageInfo = <T extends PageParams>({ page, pageSize }: T, itemCount: number): PageInfo => {
	return {
		page,
		page_size: pageSize,
		page_count: Math.ceil(itemCount / pageSize),
		item_count: itemCount,
	};
};

export const toOffsetLimit = <T extends PageParams>({ page, pageSize }: T): { offset: number; limit: number } => {
	return {
		offset: (page - 1) * pageSize,
		limit: pageSize,
	};
};
