type KeysOfType<T, Allowed> = {
	[K in keyof T]-?: Exclude<T[K], null | undefined> extends Allowed ? K : never;
}[keyof T];

type Primitive = string | number | boolean;

export type StringCastKeys<T> = KeysOfType<T, Primitive>;
export type NumberCastKeys<T> = KeysOfType<T, Primitive>;
export type DateCastKeys<T> = KeysOfType<T, string | number | Date>;

export const convertDateToTimeStamp = <T extends Partial<Record<K, string | number | Date>>, K extends keyof T>(
	data: T[],
	keys: K[],
): T[] => {
	return data.map((item) => {
		keys.forEach((key) => {
			item[key] = new Date(item[key]).getTime() as unknown as T[K];
		});
		return item;
	});
};

export const castFieldsToNumber = <T extends Partial<Record<K, Primitive>>, K extends keyof T>(data: T[], keys: K[]): T[] => {
	data.forEach((item) => {
		keys.forEach((field) => {
			if (item[field] !== undefined && item[field] !== null) {
				item[field] = Number(item[field]) as T[K];
			}
		});
	});
	return data;
};

export const castFieldsToString = <T extends Partial<Record<K, Primitive>>, K extends keyof T>(data: T[], keys: K[]): T[] => {
	data.forEach((item) => {
		keys.forEach((field) => {
			if (item[field] !== undefined && item[field] !== null) {
				item[field] = String(item[field]) as T[K];
			}
		});
	});
	return data;
};
