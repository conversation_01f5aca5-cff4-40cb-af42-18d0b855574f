import * as _ from 'lodash';

// Limiting the _.group by to accept only existing number or string keys
export const groupBy = _.groupBy as unknown as <T extends Partial<Record<K, string | number>>, K extends keyof T>(
	data: T[],
	key: K,
) => Record<T[K], T[]>;

// Limiting the _.keyBy to accept only existing number or string keys
export const keyBy = _.keyBy as unknown as <T extends Partial<Record<K, string | number>>, K extends keyof T>(
	data: T[],
	key: K,
) => Record<T[K], T>;
