/**
 * Ensures all specified keys exist in the resulting object, with values from the source object or a fallback value.
 *
 * @param {K[]} keys - Array of keys that should exist in the resulting object
 * @param {Record<K, T>} source - Source object containing values for some or all of the keys
 * @param {F} fallback - Fallback value to use for keys not present in the source object
 * @returns {Record<K, T | F>} A new object containing all specified keys with values from source or fallback
 */
export const completeObject = <T, F, K extends string | number | symbol>(
	keys: readonly K[],
	source: Record<K, T>,
	fallback: F,
): Record<K, T | F> => {
	return Object.fromEntries(keys.map((key) => [key, source[key] ?? fallback])) as Record<K, T | F>;
};

/**
 * Removes the specified keys from the object in place.
 *
 * @param obj - The object to modify
 * @param keys - The keys to remove from the object
 */
export const stripKeysInPlace = <T extends object>(obj: T, keys: string[]): void => {
	keys.forEach((k) => delete (obj as any)[k]);
};
