export const getRosterTeamForEventQuery = (isESWID: boolean) => `
SELECT rt.roster_team_id,
		rt.team_name,
		rt.organization_code,
		rc.club_name,
		rc.state,
		e.event_id, 
		e.long_name,
		(
			SELECT coalesce(
					array_to_json(array_agg(row_to_json(teamathletes))),
					'[]'::json
				)
			FROM (
					SELECT ma.first,
						ma.last,
						COALESCE(ra.jersey, ma.jersey) "uniform",
						COALESCE(sp.short_name, spm.short_name) "short_position",
						ma.gradyear,
						ma.height,
						ma.master_athlete_id
					FROM master_athlete ma
						INNER JOIN roster_athlete ra ON ra.master_athlete_id = ma.master_athlete_id
						AND ra.event_id = rt.event_id
						AND ra.deleted IS NULL
						AND ra.deleted_by_user IS NULL
						LEFT JOIN sport_position sp ON sp.sport_position_id = ra.sport_position_id
						LEFT JOIN "sport_position" spm ON spm.sport_position_id = ma.sport_position_id
					WHERE ra.roster_team_id = rt.roster_team_id
						AND ma.deleted IS NULL
						AND (
							ra.as_staff = $3
							OR ra.as_staff IS NULL
						)
					ORDER BY COALESCE(ra.jersey, ma.jersey) ASC
				) teamathletes
		) "roster_athletes",
		(
			SELECT coalesce(
					array_to_json(array_agg(row_to_json(teamstaff))),
					'[]'::json
				)
			FROM (
					SELECT ms.first,
						ms.last,
						COALESCE(r.name, rm.name) "role_name",
						r.sort_order,
						ms.master_staff_id
					FROM master_staff ms
						INNER JOIN roster_staff_role rsr ON rsr.master_staff_id = ms.master_staff_id
						AND rsr.roster_team_id = rt.roster_team_id
						AND rsr.deleted IS NULL
						AND rsr.deleted_by_user IS NULL
						LEFT JOIN "master_staff_role" msr ON msr.master_staff_id = rsr.master_staff_id
						AND msr.master_team_id = rsr.master_team_id
						LEFT JOIN "role" r ON r.role_id = rsr.role_id
						LEFT JOIN "role" rm ON rm.role_id = msr.role_id
					WHERE ms.deleted IS NULL
				) teamstaff
		) "roster_staff"
	FROM roster_team rt
		INNER JOIN "event" e ON e.event_id = rt.event_id
		LEFT JOIN roster_club rc ON rt.roster_club_id = rc.roster_club_id
	WHERE ${isESWID ? 'e.esw_id' : 'e.event_id'} = $1
		AND rt.status_entry = $2
		AND rt.event_id = e.event_id
		AND rt.deleted IS NULL
`;
