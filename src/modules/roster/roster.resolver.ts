import { Args, ID, Query, Resolver } from '@nestjs/graphql';

import { EventId } from '@/infra/decorators/eventId.decorator';

import { EventTeamRoster } from './entities';
import { RosterService } from './roster.service';

@Resolver()
export class RosterResolver {
	constructor(private readonly rosterService: RosterService) {}

	@Query(() => [EventTeamRoster])
	roster(@Args('id', { type: () => ID }) id: string, @EventId() { eventID, isESWID }: { eventID: string | number; isESWID: boolean }) {
		return this.rosterService.getRoster(eventID, isESWID);
	}
}
