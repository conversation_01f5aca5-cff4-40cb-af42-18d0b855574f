import { Injectable } from '@nestjs/common';

import { CacheService } from '@/infra/cache/cache.service';

import { RosterRepository } from './roster.repository';

@Injectable()
export class RosterService {
	constructor(private rosterRepository: RosterRepository, private cacheService: CacheService) {}

	async getRoster(eventId: string | number, isESWID: boolean) {
		const CACHE_KEY = 'getRoster';
		const cacheResponse = await this.cacheService.getCache(CACHE_KEY, { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.rosterRepository.getRoster(eventId, isESWID);
		this.cacheService.setCache(response, CACHE_KEY, { eventId });
		return response;
	}
}
