import { Modu<PERSON> } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { RosterRepository } from './roster.repository';
import { RosterResolver } from './roster.resolver';
import { RosterService } from './roster.service';

@Module({
	imports: [PrismaModule, CacheModule],
	providers: [RosterResolver, RosterService, RosterRepository],
})
export class RosterModule {}
