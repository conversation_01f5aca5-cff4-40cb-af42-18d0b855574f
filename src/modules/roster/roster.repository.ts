import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';

import { EventTeamRoster } from './entities';
import { getRosterTeamForEventQuery } from './queries/roster.query';

@Injectable()
export class RosterRepository {
	constructor(private prisma: PrismaService) {}

	getRoster(eventId: string | number, isESWID: boolean) {
		const TEAM_ACCEPTED_STATUS = 12;
		const TEAM_EMPTY_STATUS = 0;
		return this.prisma.$queryRawUnsafe<EventTeamRoster[]>(
			getRosterTeamForEventQuery(isESWID),
			eventId,
			TEAM_ACCEPTED_STATUS,
			TEAM_EMPTY_STATUS,
		);
	}
}
