import { ObjectType, Field, ID, Int } from '@nestjs/graphql';

@ObjectType()
class EventTeamRosterAthlete {
	@Field(() => ID, { name: 'id' })
	master_athlete_id: string;

	@Field({ nullable: true })
	first: string;

	@Field({ nullable: true })
	last: string;

	@Field({ nullable: true })
	short_position: string;

	@Field(() => Int, { nullable: true })
	uniform: number;

	@Field(() => Int, { nullable: true })
	gradyear: number;
}

@ObjectType()
class EventTeamRosterStaff {
	@Field(() => ID, { name: 'id' })
	master_staff_id: string;

	@Field({ nullable: true })
	first: string;

	@Field({ nullable: true })
	last: string;

	@Field({ nullable: true })
	role_name: string;

	@Field(() => Int, { nullable: true })
	sort_order: number;
}

@ObjectType()
export class EventTeamRoster {
	@Field(() => ID)
	event_id: string;

	@Field({ nullable: true, name: 'event_name' })
	long_name: string;

	@Field({ nullable: true })
	state: string;

	@Field({ nullable: true })
	club_name: string;

	@Field({ nullable: true })
	organization_code: string;

	@Field({ nullable: true })
	team_name: string;

	@Field({ nullable: true })
	roster_team_id: string;

	@Field(() => [EventTeamRosterAthlete], { nullable: true })
	roster_athletes: EventTeamRosterAthlete[];

	@Field(() => [EventTeamRosterStaff], { nullable: true })
	roster_staff: EventTeamRosterStaff[];
}
