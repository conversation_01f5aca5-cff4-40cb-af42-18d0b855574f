export const getDivisionsForEventQuery = () => `
	SELECT 
	e.esw_id "event_id", d.name, 
	d.short_name, d.max_teams, d.division_id, d.gender, 
	e.name "event_name", 
	COUNT(rt.roster_team_id)::INT "teams_count",
	d.has_flow_chart,
	(
			CASE
					WHEN (NULLIF(em.file_path, '') IS NOT NULL) THEN concat(em.file_path, '.', em.file_ext)
					ELSE NULL
			END
	) AS flowchart_path,
	EXISTS(
			SELECT 1
			FROM division_standing ds
			WHERE ds.division_id = d.division_id AND ds.rank <> 0
			LIMIT 1
	) "is_game_over"
	FROM division d 
	INNER JOIN "event" e 
	ON e.event_id = d.event_id 
	LEFT JOIN roster_team rt 
	ON rt.division_id = d.division_id 
	AND d.published IS TRUE 
	AND rt.deleted IS NULL 
	AND rt.status_entry = 12 
	LEFT JOIN event_media em
	ON em.event_id = e.event_id
	AND em.division_id = d.division_id
	AND em.file_type = 'flowchart'
	WHERE e.esw_id = $1
	AND d.published = true
	GROUP BY d.event_id, d.name, d.short_name, d.created,
	d.modified, d.max_teams, d.division_id, e.name, e.esw_id, flowchart_path
	ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level
`;
