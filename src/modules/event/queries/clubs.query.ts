export const getClubsForEventQuery = () => `
	SELECT 
			rc.roster_club_id, rc.club_name, rc.state, rc.code as club_code,
			COUNT(rt.roster_team_id)::INT teams_count
	FROM roster_club rc 
	INNER JOIN "event" e 
			ON e.event_id = rc.event_id
	INNER JOIN roster_team rt 
			ON rt.roster_club_id = rc.roster_club_id 
	WHERE e.esw_id = $1 
		AND rc.deleted IS NULL 
		AND rt.status_entry = 12 
		AND rt.deleted IS NULL 
	GROUP BY rc.roster_club_id, rc.club_name, rc.state 
	HAVING COUNT(rt.roster_team_id) > 0 
	ORDER BY rc.club_name
`;
