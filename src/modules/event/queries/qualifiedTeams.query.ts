export const getQualifiedTeamsForEventQuery = () => `
  SELECT rt.extra->>'earned_at' AS "earned_at",
    rt.team_name,
    d.name AS "division",
    rt.organization_code AS "team_code",
    (rt.extra->>'prev_qual_age'::TEXT) || ' ' || INITCAP(rt.extra->>'prev_qual_division'::TEXT) AS "bid_earned"
  FROM "roster_team" AS rt
    INNER JOIN "event" AS e ON (e.esw_id = $1)
    INNER JOIN "division" AS d ON (d.division_id = rt.division_id)
  WHERE ((rt.extra->>'prev_qual')::BOOLEAN IS TRUE)
    AND (rt.event_id = e.event_id)
    AND rt.deleted IS NULL
  ORDER BY d.name,
    rt.organization_code
`;
