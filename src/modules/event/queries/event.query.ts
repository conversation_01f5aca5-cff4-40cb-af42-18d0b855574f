export const getEventQuery = (isESWID: boolean) => `
	SELECT (
		CASE
			WHEN e.schedule_published IS TRUE THEN e.esw_id
			ELSE e.event_id::VARCHAR
		END
	) "event_id",
	e.event_id "id",
	e.name,
	e.long_name,
	e.reg_fee,
	e.hide_seeds,
	to_char(e.date_start, 'MM/DD/YYYY') date_start,
	to_char(e.date_end, 'MM/DD/YYYY') date_end,
	e.timezone,
	e.has_coed_teams,
	e.has_female_teams,
	e.has_male_teams,
	e.email,
	e.website,
	e.event_notes,
	e.city,
	e.state,
	e.address,
	e.schedule_published,
	e.registration_method,
	e.has_match_barcodes,
	(e."tickets_settings"->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE  as is_require_recipient_name_for_each_ticket,
	COALESCE(e.teams_settings, '{}'::JSONB) AS teams_settings,
	COALESCE(
		e.require_match_end_time,
		eo.require_match_end_time,
		FALSE
	) require_match_end_time,
	(
		EXTRACT(
			EPOCH
			FROM e.date_reg_open
		) * 1000
	)::BIGINT date_reg_open,
	e.allow_teams_registration,
	e.social_links,
	(
		EXTRACT(
			EPOCH
			FROM e.date_reg_close
		) * 1000
	)::BIGINT date_reg_close,
	(
		SELECT true
		FROM roster_team rt
		WHERE rt.event_id = e.event_id
			AND (rt.extra->>'prev_qual')::BOOLEAN IS TRUE
			AND rt.deleted IS NULL
		LIMIT 1
	) IS TRUE "is_with_prev_qual",
	(
		SELECT ARRAY_TO_JSON(
				ARRAY(
					SELECT DISTINCT((secs_start::DATE))
					FROM matches
					WHERE matches.event_id = e.event_id
						AND matches.day > 0
					ORDER BY secs_start
				)
			)
	) "match_days",
	(
		EXTRACT(
			EPOCH
			FROM e.roster_deadline
		) * 1000
	)::BIGINT roster_deadline,
	(
		CASE
			WHEN (
				e.registration_method = 'doubles'
				AND e.published IS TRUE
				AND e.teams_use_clubs_module IS TRUE
				AND (
					COALESCE(MAX(d.date_reg_close), e.date_reg_close) >= (NOW() AT TIME ZONE e.timezone)
				)
			) THEN TRUE
			ELSE FALSE
		END
	) "doubles_reg_available",
	e.event_tickets_code "tickets_code",
	(
		SELECT ARRAY_TO_JSON(
				ARRAY(
					SELECT DISTINCT((secs_start::DATE))
					FROM matches
					WHERE matches.event_id = e.event_id
						AND matches.day > 0
					ORDER BY secs_start
				)
			)
	) "days",
	COALESCE(
		e.allow_ticket_sales IS TRUE
		AND e.tickets_published IS TRUE
		AND e.event_tickets_code IS NOT NULL
		AND (NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_end
		AND (NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_start
		AND e.tickets_purchase_date_end <= e.date_end
		AND e.deleted IS NULL
		AND e.live_to_public IS TRUE,
		FALSE
	)::BOOLEAN "tickets_published",
	(
		CASE
			WHEN (e."has_rosters" IS TRUE) THEN (
				SELECT (SUM(d."count") > 0)
				FROM (
						SELECT COALESCE(COUNT(*), 0) "count"
						FROM "roster_athlete" ra
						WHERE ra."event_id" = e.event_id
							AND ra."deleted" IS NULL
							AND ra."deleted_by_user" IS NULL
						UNION ALL
						SELECT COALESCE(COUNT(*), 0) "count"
						FROM "roster_staff_role" rsr
							INNER JOIN "roster_team" rt ON rt.roster_team_id = rsr.roster_team_id
						WHERE rsr."deleted" IS NULl
							AND rsr."deleted_by_user" IS null
							AND rt."event_id" = e.event_id
					) "d"
			)
			ELSE FALSE
		END
	) "has_rosters",
	e.teams_use_clubs_module IS TRUE "has_clubs",
	(
		SELECT array_to_json(array_agg(fees.reg_fee))
		FROM (
				SELECT distinct(dv.reg_fee)
				FROM division "dv"
				WHERE dv.event_id = e.event_id
					AND dv.reg_fee <> 0
					AND dv.closed IS NULL
					AND dv.published IS TRUE
			) as fees
	) as division_fees,
	(
		SELECT name AS sport_sanctioning
		FROM sport_sanctioning
		WHERE sport_sanctioning_id = e.sport_sanctioning_id
	),
	(
		SELECT COALESCE(
				ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("l"))),
				'[]'::JSON
			)
		FROM (
				SELECT el.name location_name,
					el.address,
					el.city,
					el.state,
					el.zip,
					el.number
				FROM "event_location" "el"
				WHERE e.event_id = el.event_id
				ORDER BY el.number
			) l
	) locations
	FROM "event" e
	LEFT JOIN event_owner eo ON eo.event_owner_id = e.event_owner_id
	LEFT JOIN "division" d ON d.event_id = e.event_id
	AND d.closed IS NULL
	AND d.locked IS NOT TRUE
	AND d.published IS TRUE
	WHERE e.live_to_public IS TRUE
	AND ${isESWID ? 'e.esw_id' : 'e.event_id'} = $1
	GROUP BY e.event_id,
	eo.event_owner_id
`;

export const getEventsQuery = () => `
	SELECT (
		CASE
			WHEN e.schedule_published IS TRUE THEN e.esw_id
			ELSE e.event_id::VARCHAR
		END
	) "event_id",
	e.name,
	e.long_name,
	e.date_start,
	e.date_end,
	e.timezone,
	e.city,
	e.state,
	e.address,
	e.has_coed_teams,
	e.has_female_teams,
	e.has_male_teams,
	e.email,
	e.website,
	concat(em.file_path, '.', em.file_ext) as small_logo,
	TRUE has_athletes,
	TRUE has_staff,
	(
		e.date_start <= (NOW() AT TIME ZONE e.timezone)
		AND (NOW() AT TIME ZONE e.timezone) <= e.date_end
		AND e.schedule_published IS TRUE
		AND e.is_test IS NOT TRUE
		AND e.deleted IS NULL
	) "current",
	e.schedule_published
	FROM "event" e
	LEFT JOIN event_media em on em.event_id = e.event_id AND em.file_type = 'small-logo'
	WHERE e.live_to_public IS TRUE
	AND e.allow_teams_registration IS TRUE
	AND e.esw_id IS NOT NULL
	and e.registration_method <> 'doubles'
	ORDER BY e.date_start
`;
