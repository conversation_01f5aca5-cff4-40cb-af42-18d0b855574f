export const getPoolsForEventDivisionQuery = () => `
	SELECT r.uuid r_uuid,
		r.sort_priority r_sort_priority,
		pb.sort_priority,
		rr.sort_priority rr_sort_priority,
		r.name r_name,
		r.short_name r_short_name,
		pb.is_pool,
		pb.name pb_name,
		pb.short_name pb_short_name,
		pb.display_name,
		pb.team_count,
		pb.uuid,
		pb.settings,
		rr.name rr_name,
		rr.short_name rr_short_name,
		r.division_short_name,
		NULLIF(pb.pb_stats, '') IS NULL is_empty_pb_stats,
		(
			SELECT extract(
					epoch
					from m.secs_start
				)::Int
			FROM matches m
			WHERE m.pool_bracket_id = pb.uuid
				AND m.event_id = e.event_id
			ORDER BY m.secs_start ASC,
				m.match_number ASC
			LIMIT 1
		) date_start, (
			SELECT c.name
			FROM matches m
				INNER JOIN courts c ON c.uuid = m.court_id
			WHERE m.pool_bracket_id = pb.uuid
				AND m.event_id = e.event_id
				AND c.event_id = e.event_id --  AND m.match_number = 1 -- <PERSON> asked to fix that for pools where M1 is not first match in time
			ORDER BY m.secs_start ASC,
				m.match_number ASC
			LIMIT 1
		) court_start, (
			SELECT array_to_json(array_agg(row_to_json(union_teams)))
			FROM (
					SELECT rt.team_name opponent_team_name,
						rt.organization_code opponent_organization_code,
						rt.roster_team_id opponent_team_id,
						dst.info,
						dst.rank,
						dst.sets_pct,
						rt.extra->>'show_previously_accepted_bid' show_previously_accepted_bid,
						NULLIF(
							CAST(
								pool_bracket_stats.value->>'points_ratio' AS NUMERIC
							),
							0
						) IS NULL as is_empty_team_pb_stats,
						pool_bracket_stats.value->>'sets_won' as sets_won,
						pool_bracket_stats.value->>'sets_lost' as sets_lost,
						pool_bracket_stats.value->>'matches_won' as matches_won,
						pool_bracket_stats.value->>'matches_lost' as matches_lost,
						ROUND(
							CAST(
								pool_bracket_stats.value->>'points_ratio' AS NUMERIC
							),
							3
						) points_ratio
					FROM roster_team rt
						LEFT JOIN matches m ON m.team2_roster_id = rt.roster_team_id
						LEFT JOIN division_standing dst ON m.event_id = dst.event_id
						AND m.division_id = dst.division_id
						AND m.team2_roster_id = dst.team_id
						LEFT JOIN json_each(COALESCE(NULLIF(pb.pb_stats, ''), '{}')::JSON) pool_bracket_stats ON (pool_bracket_stats.value->>'team_id')::INT = rt.roster_team_id
					WHERE m.pool_bracket_id = pb.uuid
						AND m.team2_roster_id = rt.roster_team_id
						AND rt.event_id = e.event_id
						AND m.event_id = e.event_id
						AND dst.event_id = e.event_id
					UNION
					SELECT rt.team_name opponent_team_name,
						rt.organization_code opponent_organization_code,
						rt.roster_team_id opponent_team_id,
						dst.info,
						dst.rank,
						dst.sets_pct,
						rt.extra->>'show_previously_accepted_bid' show_previously_accepted_bid,
						NULLIF(
							CAST(
								pool_bracket_stats.value->>'points_ratio' AS NUMERIC
							),
							0
						) IS NULL as is_empty_team_pb_stats,
						pool_bracket_stats.value->>'sets_won' as sets_won,
						pool_bracket_stats.value->>'sets_lost' as sets_lost,
						pool_bracket_stats.value->>'matches_won' as matches_won,
						pool_bracket_stats.value->>'matches_lost' as matches_lost,
						ROUND(
							CAST(
								pool_bracket_stats.value->>'points_ratio' AS NUMERIC
							),
							3
						) points_ratio
					FROM roster_team rt
						LEFT JOIN matches m ON m.team1_roster_id = rt.roster_team_id
						LEFT JOIN division_standing dst ON m.event_id = dst.event_id
						AND m.division_id = dst.division_id
						AND m.team1_roster_id = dst.team_id
						LEFT JOIN json_each(COALESCE(NULLIF(pb.pb_stats, ''), '{}')::JSON) pool_bracket_stats ON (pool_bracket_stats.value->>'team_id')::INT = rt.roster_team_id
					WHERE m.pool_bracket_id = pb.uuid
						AND m.team1_roster_id = rt.roster_team_id
						AND rt.event_id = e.event_id
						AND m.event_id = e.event_id
						AND dst.event_id = e.event_id
						AND rt.event_id = e.event_id
				) union_teams
		) AS teams
	FROM rounds r
		INNER JOIN "event" e ON e.event_id = r.event_id
		LEFT JOIN poolbrackets pb ON pb.round_id = r.uuid
		AND pb.event_id = e.event_id
		LEFT JOIN rounds rr ON rr.uuid = pb.group_id
		AND rr.event_id = e.event_id
		LEFT JOIN division d ON r.division_id = d.division_id
		AND d.event_id = e.event_id
		AND d.published IS TRUE
	WHERE e.esw_id = $1
		AND r.division_id = CAST($2 AS INTEGER)
		AND r.event_id = e.event_id
	ORDER BY r.sort_priority,
		rr.sort_priority,
		pb.sort_priority
`;
