export const getUpcomingMatchesForEventQuery = () => `
	SELECT
		e.event_id,
				d.division_id,
				d.short_name AS division_name,
				rc.roster_club_id AS club_id,
				rc.club_name,
				rc.state AS club_state,
				rc.code AS club_code,
				rt.roster_team_id AS team_id,
				rt.organization_code AS team_code,
				rt.team_name AS team_name,
				opp_rt.roster_team_id AS opponent_id,
				opp_rt.team_name AS opponent_name,
				m.secs_start,
				m.secs_finished,
				c.short_name AS court_name,
				ds.matches_won,
				ds.matches_lost,
				ds.sets_won,
				ds.sets_lost,
				ds.sets_pct,
				ds.info::JSON->'seed_current' AS seed
	FROM
		EVENT e
	JOIN roster_club rc ON
		e.event_id = rc.event_id
		AND rc.deleted IS NULL
	JOIN roster_team rt ON
		rt.event_id = e.event_id
		AND rt.roster_club_id = rc.roster_club_id
		AND rt.deleted IS NULL
	JOIN matches m ON
		m.event_id = e.event_id
		AND rt.roster_team_id IN (m.team1_roster_id, m.team2_roster_id)
	JOIN division d ON
		d.event_id = e.event_id
		AND m.division_id = d.division_id
	LEFT JOIN roster_team opp_rt ON
		opp_rt.roster_team_id = (
							CASE
									WHEN rt.roster_team_id = m.team1_roster_id THEN m.team2_roster_id
			ELSE m.team1_roster_id
		END
				)
	JOIN courts c ON
		m.court_id = c.uuid
	JOIN division_standing ds ON
		e.event_id = ds.event_id
		AND ds.division_id = m.division_id
		AND ds.team_id = rt.roster_team_id
	WHERE
		e.deleted IS NULL
		AND e.esw_id = $1
`;
