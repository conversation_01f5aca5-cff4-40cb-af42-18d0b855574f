export const getTeamsForEventQuery = () => `
	select d."name" as division_name,
		d.short_name as division_short_name,
		club_name,
		rt.team_name,
		rt.organization_code,
		rt.roster_club_id,
		rt.roster_team_id,
		rt.gender,
		rc.state as club_state,
		d.division_id
	from roster_team rt
		inner join division d USING(division_id)
		inner join roster_club rc USING(roster_club_id)
		inner join event e on e.event_id = rt.event_id
	where e.esw_id = $1
		and rt.status_entry = '12'
`;

export const getSingleTeamForEventQuery = () => `
	SELECT 
		rt.roster_team_id,
		rt.master_team_id,
		rt.team_name,
		rt.division_id,
		rt.matches_won,
		rt.matches_lost,
		rt.manual_club_name,
		rt.sets_won,
		rt.sets_lost,
		rt.organization_code,
		rt.points_won,
		rt.points_lost,
		rt.roster_club_id,
		rc.club_name,
		rc.state,
		(
			SELECT array_to_json(array_agg(row_to_json(union_matches)))
			FROM (
					SELECT 'team1' match_type,
						m.match_id,
						extract(
							epoch
							from m.secs_finished
						)::BIGINT as unix_finished,
						m.results,
						m.display_name,
						to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,
						extract(
							epoch
							from m.secs_start
						)::BIGINT * 1000 date_start,
						m.division_id,
						m.division_short_name,
						c.name court_name,
						pb.display_name pool_name,
						m.pool_bracket_id,
						pb.is_pool,
						rt2.team_name opponent_team_name,
						rt2.organization_code opponent_organization_code,
						pb.name pb_name,
						pb.settings,
						r.name round_name,
						m.footnote_play,
						m.footnote_team1,
						m.footnote_team2
					FROM matches m
						INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid
						AND pb.event_id = e.event_id
						LEFT JOIN courts c ON c.uuid = m.court_id
						AND c.event_id = e.event_id
						LEFT JOIN LATERAL (
							SELECT _rt.team_name,
								_rt.organization_code
							FROM roster_team _rt
							WHERE m.team2_roster_id = _rt.roster_team_id
								AND _rt.status_entry = 12
								AND _rt.event_id = e.event_id
							LIMIT 1
						) rt2 ON TRUE
						LEFT JOIN rounds r ON r.uuid = m.round_id
						AND r.event_id = e.event_id
					WHERE m.team1_roster_id = rt.roster_team_id
						AND m.secs_finished IS NULL
						AND m.event_id = e.event_id
					UNION
					SELECT 'team2' match_type,
						m.match_id,
						extract(
							epoch
							from m.secs_finished
						)::BIGINT as unix_finished,
						m.results,
						m.display_name,
						to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,
						extract(
							epoch
							from m.secs_start
						)::BIGINT * 1000 date_start,
						m.division_id,
						m.division_short_name,
						c.name court_name,
						pb.display_name pool_name,
						m.pool_bracket_id,
						pb.is_pool,
						rt2.team_name opponent_team_name,
						rt2.organization_code opponent_organization_code,
						pb.name pb_name,
						pb.settings,
						r.name round_name,
						m.footnote_play,
						m.footnote_team1,
						m.footnote_team2
					FROM matches m
						INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid
						AND pb.event_id = e.event_id
						LEFT JOIN courts c ON c.uuid = m.court_id
						AND c.event_id = e.event_id
						LEFT JOIN LATERAL (
							SELECT _rt.team_name,
								_rt.organization_code
							FROM roster_team _rt
							WHERE m.team1_roster_id = _rt.roster_team_id
								AND _rt.status_entry = 12
								AND _rt.event_id = e.event_id
							LIMIT 1
						) rt2 ON TRUE
						LEFT JOIN rounds r ON r.uuid = m.round_id
						AND r.event_id = e.event_id
					WHERE m.team2_roster_id = rt.roster_team_id
						AND m.secs_finished IS NULL
						AND m.event_id = e.event_id
					UNION
					SELECT 'ref' match_type,
						m.match_id,
						extract(
							epoch
							from m.secs_finished
						)::BIGINT as unix_finished,
						m.results,
						m.display_name,
						to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,
						extract(
							epoch
							from m.secs_start
						)::BIGINT * 1000 date_start,
						m.division_id,
						m.division_short_name,
						c.name court_name,
						pb.display_name pool_name,
						m.pool_bracket_id,
						pb.is_pool,
						rt2.team_name opponent_team_name,
						rt2.organization_code opponent_organization_code,
						pb.name pb_name,
						pb.settings,
						r.name round_name,
						m.footnote_play,
						m.footnote_team1,
						m.footnote_team2
					FROM matches m
						INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid
						AND pb.event_id = e.event_id
						LEFT JOIN courts c ON c.uuid = m.court_id
						AND c.event_id = e.event_id
						LEFT JOIN LATERAL (
							SELECT _rt.team_name,
								_rt.organization_code
							FROM roster_team _rt
							WHERE m.team1_roster_id = _rt.roster_team_id
								AND _rt.status_entry = 12
								AND _rt.event_id = e.event_id
							LIMIT 1
						) rt2 ON TRUE
						LEFT JOIN rounds r ON r.uuid = m.round_id
						AND r.event_id = e.event_id
					WHERE m.ref_roster_id = rt.roster_team_id
						AND m.secs_finished IS NULL
						AND m.event_id = e.event_id
					ORDER BY date_start ASC
				) union_matches
		) AS upcoming,
		(
			SELECT array_to_json(array_agg(row_to_json(pool_matches)))
			FROM (
					SELECT pb.uuid,
						pb.is_pool,
						pb.name pb_name,
						r.name round_name,
						pb.pb_stats,
						r.sort_priority,
						pb.sort_priority,
						(
							SELECT array_to_json(array_agg(row_to_json(union_matches)))::text
							FROM (
									SELECT 'team1' match_type,
										m2.match_id,
										extract(
											epoch
											from m2.secs_finished
										)::BIGINT as unix_finished,
										m2.results,
										m2.display_name,
										to_char(m2.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,
										extract(
											epoch
											from m2.secs_start
										)::BIGINT * 1000 date_start,
										m2.division_id,
										m2.division_short_name,
										m2.pool_bracket_id,
										rt2.team_name opponent_team_name,
										rt2.organization_code opponent_organization_code,
										rt2.roster_team_id opponent_team_id
									FROM matches m2
										INNER JOIN poolbrackets pb2 ON m2.pool_bracket_id = pb2.uuid
										AND pb2.event_id = e.event_id
										LEFT JOIN LATERAL (
											SELECT _rt.roster_team_id,
												_rt.team_name,
												_rt.organization_code
											FROM roster_team _rt
											WHERE m2.team2_roster_id = _rt.roster_team_id
												AND _rt.status_entry = 12
												AND _rt.event_id = e.event_id
											LIMIT 1
										) rt2 ON TRUE
									WHERE m2.pool_bracket_id = pb.uuid
										AND m2.event_id = e.event_id
										AND m2.team1_roster_id = rt.roster_team_id
									UNION
									SELECT 'team2' match_type,
										m2.match_id,
										extract(
											epoch
											from m2.secs_finished
										)::BIGINT as unix_finished,
										m2.results,
										m2.display_name,
										to_char(m2.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,
										extract(
											epoch
											from m2.secs_start
										)::BIGINT * 1000 date_start,
										m2.division_id,
										m2.division_short_name,
										m2.pool_bracket_id,
										rt2.team_name opponent_team_name,
										rt2.organization_code opponent_organization_code,
										rt2.roster_team_id opponent_team_id
									FROM matches m2
										INNER JOIN poolbrackets pb2 ON m2.pool_bracket_id = pb2.uuid
										AND pb2.event_id = e.event_id
										LEFT JOIN LATERAL (
											SELECT _rt.roster_team_id,
												_rt.team_name,
												_rt.organization_code
											FROM roster_team _rt
											WHERE m2.team1_roster_id = _rt.roster_team_id
												AND _rt.status_entry = 12
												AND _rt.event_id = e.event_id
											LIMIT 1
										) rt2 ON TRUE
									WHERE m2.pool_bracket_id = pb.uuid
										AND m2.event_id = e.event_id
										AND m2.team2_roster_id = rt.roster_team_id
									ORDER BY date_start ASC
								) union_matches
						) AS matches
					FROM matches m
						INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid
						AND pb.event_id = e.event_id
						LEFT JOIN rounds r ON r.uuid = m.round_id
						AND r.event_id = e.event_id
					WHERE (
							m.team1_roster_id = rt.roster_team_id
							OR m.team2_roster_id = rt.roster_team_id
						)
						AND m.event_id = e.event_id
						AND m.secs_finished IS NOT NULL
					GROUP BY r.name,
						pb.name,
						pb.is_pool,
						pb.uuid,
						r.sort_priority,
						pb.sort_priority
					ORDER BY r.sort_priority,
						pb.sort_priority ASC
				) pool_matches
		) "results",
		(
			SELECT coalesce(
					array_to_json(array_agg(row_to_json(teamathletes))),
					'[]'::json
				)
			FROM (
					SELECT ma.first,
						ma.last,
						(
							CASE
								WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey)
								ELSE COALESCE(ra.jersey, ma.jersey)
							END
						) AS "uniform",
						COALESCE(sp.short_name, spm.short_name) "short_position",
						ma.gradyear,
						ma.height
					FROM master_athlete ma
						INNER JOIN roster_athlete ra ON ra.master_athlete_id = ma.master_athlete_id
						AND ra.event_id = rt.event_id
						AND ra.deleted IS NULL
						AND ra.deleted_by_user IS NULL
						LEFT JOIN sport_position sp ON sp.sport_position_id = ra.sport_position_id
						LEFT JOIN "sport_position" spm ON spm.sport_position_id = ma.sport_position_id
					WHERE ra.roster_team_id = rt.roster_team_id
						AND ma.deleted IS NULL
						AND (
							ra.as_staff = 0
							OR ra.as_staff IS NULL
						)
					ORDER BY 
						CASE 
							WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey, 0)
							ELSE COALESCE(ra.jersey, ma.jersey, 0)
						END,
						ma.last,
						ma.first
				) teamathletes
		) "athletes",
		(
			SELECT coalesce(
					array_to_json(array_agg(row_to_json(teamstaff) ORDER BY sort_order NULLS LAST, is_athlete_staff ASC, last, first)),
					'[]'::json
				)
			FROM (
					SELECT ms.first,
						ms.last,
						COALESCE(r.name, rm.name) "role_name",
						COALESCE(r.sort_order, rm.sort_order) AS sort_order,
						FALSE AS is_athlete_staff
					FROM master_staff ms
						INNER JOIN roster_staff_role rsr ON rsr.master_staff_id = ms.master_staff_id
						AND rsr.roster_team_id = rt.roster_team_id
						AND rsr.deleted IS NULL
						AND rsr.deleted_by_user IS NULL
						LEFT JOIN "master_staff_role" msr ON msr.master_staff_id = rsr.master_staff_id
						AND msr.master_team_id = rsr.master_team_id
						LEFT JOIN "role" r ON r.role_id = rsr.role_id
						LEFT JOIN "role" rm ON rm.role_id = msr.role_id
					WHERE ms.deleted IS NULL
					UNION
					SELECT ma.first,
						ma.last,
						'Staff' AS role_name,
						NULL AS sort_order,
						TRUE AS is_athlete_staff
					FROM master_athlete ma
						INNER JOIN roster_athlete ra ON ra.master_athlete_id = ma.master_athlete_id
						AND ra.event_id = rt.event_id
						AND ra.deleted IS NULL
						AND ra.deleted_by_user IS NULL
					WHERE ra.roster_team_id = rt.roster_team_id
						AND ma.deleted IS NULL
						AND ra.as_staff > 0
				) teamstaff
		) "staff",
		(
			SELECT m.finishes
			FROM matches m
				LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id
			WHERE (
					team2_roster_id = CAST($2 AS INTEGER)
					OR team1_roster_id = CAST($2 AS INTEGER)
					OR ref_roster_id = CAST($2 AS INTEGER)
				)
				AND m.event_id = e.event_id
				AND m.secs_finished IS NULL
				AND pb.is_pool = 0
			ORDER BY m.secs_start DESC
			LIMIT 1
		) "bracket_finishes",
		(
			SELECT COALESCE(
				json_agg(
					json_build_object(
						'uuid'   , pb.uuid,
						'name'   , pb.name,
						'is_pool', pb.is_pool,
						'pb_finishes', pb.pb_finishes
					)
				),
				'[]'::json
			)
			FROM (
				SELECT DISTINCT
					pb.uuid,
					pb.name,
					pb.is_pool,
					pb.pb_finishes
				FROM 
					matches m
				JOIN poolbrackets pb
					ON pb.uuid = m.pool_bracket_id
				WHERE 
					m.event_id = rt.event_id
					AND ( 
			    	m.team1_roster_id = rt.roster_team_id
						OR m.team2_roster_id = rt.roster_team_id
						OR m.ref_roster_id   = rt.roster_team_id 
					)
			) pb
		) AS pb_info
	FROM roster_team rt
		INNER JOIN "event" e ON e.event_id = rt.event_id
		LEFT JOIN roster_club rc ON rt.roster_club_id = rc.roster_club_id
	WHERE e.esw_id = $1
		AND rt.status_entry = 12
		AND rt.roster_team_id = CAST($2 AS INTEGER)
		AND rt.event_id = e.event_id
		AND rt.deleted IS NULL
`;
