export const getAthletesForEventQuery = () => `
	SELECT rt.roster_team_id,
		rt.division_id,
		rt.team_name,
		ma.first,
		ma.last,
		ma.gender,
		mc.club_name,
		COALESCE(ma.state, mc.state) state,
		COALESCE(ra.jersey, ma.jersey) "jersey",
		rt.organization_code "team_organization_code",
		EXTRACT(YEAR FROM AGE(ma.birthdate)) AS age
	FROM "roster_team" rt
		INNER JOIN "event" e ON e.event_id = rt.event_id
		INNER JOIN master_team mt ON mt.master_team_id = rt.master_team_id
		INNER JOIN master_club mc ON mc.master_club_id = mt.master_club_id
		INNER JOIN "roster_athlete" ra ON ra.roster_team_id = rt.roster_team_id
		AND ra.deleted IS NULL
		AND ra.deleted_by_user IS NULL
		INNER JOIN master_athlete ma ON ma.master_athlete_id = ra.master_athlete_id
		AND ma.deleted IS NULL
		JOIN state s ON s.state = mc.state
	WHERE e.esw_id = $1
		AND rt.status_entry = 12
		AND rt.deleted IS NULL
`;
