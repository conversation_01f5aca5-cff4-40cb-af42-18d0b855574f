import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export const getEventsSearchIndexEntriesQuery = (): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const query = `
		SELECT
			e.event_id,
			EXTRACT(EPOCH FROM (e.date_start AT TIME ZONE e.timezone))::BIGINT * 1000 AS date_start,
			EXTRACT(EPOCH FROM (e.date_end AT TIME ZONE e.timezone))::BIGINT * 1000 AS date_end,
			COALESCE(e.state, '') AS state,
			TO_CHAR(e.date_start, 'FMDD') AS day,
			TO_CHAR(e.date_start, 'YYYY') AS year,
			LOWER(
				COALESCE(e.long_name, '') || '|' ||
				COALESCE(e.city, '') || '|' ||
				COALESCE(e.address, '') || '|' ||
				TO_CHAR(e.date_start, 'Mon Month DD FMDD YYYY')
			) AS search_content,
			ROW_NUMBER() OVER (
				ORDER BY 
					(e.date_start AT TIME ZONE e.timezone) ASC,
					e.event_id ASC
			) AS sort_order
		FROM
			event e
		WHERE 
			e.live_to_public IS TRUE
			AND e.allow_teams_registration IS TRUE
			AND e.registration_method <> 'doubles'
			AND e.esw_id IS NOT NULL
			AND e.date_start IS NOT NULL
			AND e.date_end IS NOT NULL
	`;

	return [query, sqlVars.getValues()];
};
