import { RawSqlVarHelper, RawSqlValue } from '@/shared/utils/sql';

export type EventsQueryParams = {
	eventsIds: string[];
};

export const getEventsQuery = ({ eventsIds }: EventsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const eventsIdsValues = eventsIds.map((id) => sqlVars.useValue(Number(id)));
	const query = `
		SELECT 
			(
				CASE
					WHEN e.schedule_published IS TRUE THEN e.esw_id
					ELSE e.event_id::VARCHAR
				END
			) "event_id",
			e.event_id AS id,
			e.name,
			e.long_name,
			e.date_start,
			e.date_end,
			e.timezone,
			e.city,
			e.state,
			e.address,
			e.has_coed_teams,
			e.has_female_teams,
			e.has_male_teams,
			e.email,
			e.website,
			concat(em.file_path, '.', em.file_ext) as small_logo,
			TRUE has_athletes,
			TRUE has_staff,
			(
				e.date_start <= (NOW() AT TIME ZONE e.timezone)
				AND (NOW() AT TIME ZONE e.timezone) <= e.date_end
				AND e.schedule_published IS TRUE
				AND e.is_test IS NOT TRUE
				AND e.deleted IS NULL
			) "current",
			e.schedule_published,
			COUNT(*) OVER() AS "item_count"
		FROM 
			event e
		LEFT JOIN event_media em 
			ON em.event_id = e.event_id 
			AND em.file_type = 'small-logo'
		WHERE 
			e.event_id IN (${eventsIdsValues.join(',')})
			AND e.live_to_public IS TRUE
			AND e.allow_teams_registration IS TRUE
			AND e.esw_id IS NOT NULL
			AND e.registration_method <> 'doubles'
  `;

	return [query, sqlVars.getValues()];
};
