export const getFeatureMatchesForEventQuery = () => `
	SELECT m.match_id,
		m.division_id ,
		m.team1_roster_id as team1_id,
		rt1.team_name AS team1_name,
		m.team2_roster_id as team2_id,
		rt2.team_name AS team2_name,
		c.name as court_name,
		c.short_name as court_short_name,
		m.secs_start as match_start
	FROM matches m
		INNER JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id
		INNER JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id
		INNER JOIN event e ON e.event_id = m.event_id
		INNER JOIN courts c on c.uuid = m.court_id
	WHERE e.esw_id = $1 and m.secs_start > now()
`;
