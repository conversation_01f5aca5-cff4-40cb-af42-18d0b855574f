export const getPoolForQuery = () => `
SELECT pb.event_id,
  pb.uuid,
  pb.division_short_name,
  concat_ws(' ', r.name, rr.name, pb.name) AS display_name,
  pb.display_name display_name_short,
  pb.team_count,
  pb.is_pool,
  pb.consolation,
  pb.pb_finishes,
  pb.pb_seeds,
  pb.pb_stats,
  pb.sort_priority,
  pb.round_id,
	pb.settings,
	pb.division_id,
  (
    pb.event_id IN (72, 67, 19018)
    and pb.settings::JSON->>'SetCount' = '2'
    and pb.settings::JSON->>'PlayAllSets' = 'true'
  ) hide_winner,
  (
    SELECT array_to_json(array_agg(row_to_json(upmatches)))
    FROM (
        SELECT m.match_id,
          m.source,
          m.team1_roster_id,
          rt1.team_name team1_name,
          rt1.organization_code team1_code,
          rt1.master_team_id team1_master_id,
          m.team2_roster_id,
          rt2.team_name team2_name,
          rt2.organization_code team2_code,
          rt2.master_team_id team2_master_id,
          m.ref_roster_id,
          rt3.team_name ref_team_name,
          rt3.organization_code ref_team_code,
          m.display_name,
          m.division_short_name,
          extract(
            epoch
            from m.secs_start
          )::BIGINT * 1000 date_start,
          c.name court_name,
          m.results,
          m.footnote_play,
          m.footnote_team1,
          m.footnote_team2
        FROM matches m
          LEFT JOIN LATERAL (
            SELECT 
              rt.team_name,
              rt.organization_code,
              rt.master_team_id
            FROM roster_team rt
            WHERE rt.roster_team_id = m.team1_roster_id
            LIMIT 1
          ) rt1 ON true
          LEFT JOIN LATERAL (
            SELECT 
              rt.team_name,
              rt.organization_code,
              rt.master_team_id
            FROM roster_team rt
            WHERE rt.roster_team_id = m.team2_roster_id
            LIMIT 1
          ) rt2 ON true
          LEFT JOIN LATERAL (
            SELECT 
              rt.team_name,
              rt.organization_code,
              rt.master_team_id
            FROM roster_team rt
            WHERE rt.roster_team_id = m.ref_roster_id
            LIMIT 1
          ) rt3 ON true
          LEFT JOIN courts c ON c.uuid = m.court_id
        WHERE m.pool_bracket_id = pb.uuid
          AND m.secs_finished IS NULL
        ORDER BY m.secs_start,
          c.sort_priority,
          m.match_number
      ) upmatches
  ) "upcoming_matches",
  (
    SELECT array_to_json(array_agg(row_to_json(resmatches)))
    FROM (
        SELECT m.match_id,
          m.team1_roster_id,
          rt1.team_name team1_name,
          rt1.organization_code team1_code,
          rt1.master_team_id team1_master_id,
          m.team2_roster_id,
          rt2.team_name team2_name,
          rt2.organization_code team2_code,
          rt2.master_team_id team2_master_id,
          m.ref_roster_id,
          rt3.team_name ref_team_name,
          rt3.organization_code ref_team_code,
          m.display_name,
          m.division_short_name,
          extract(
            epoch
            from m.secs_start
          )::BIGINT * 1000 date_start,
          c.name court_name,
          m.results
        FROM matches m
          LEFT JOIN LATERAL (
            SELECT rt.team_name,
            	rt.organization_code,
              rt.master_team_id
            FROM roster_team rt
            WHERE rt.roster_team_id = m.team1_roster_id
            LIMIT 1
          ) rt1 ON true
          LEFT JOIN LATERAL (
            SELECT rt.team_name,
              rt.organization_code,
              rt.master_team_id
            FROM roster_team rt
            WHERE rt.roster_team_id = m.team2_roster_id
            LIMIT 1
          ) rt2 ON true
          LEFT JOIN LATERAL (
            SELECT rt.team_name,
              rt.organization_code
            FROM roster_team rt
            WHERE rt.roster_team_id = m.ref_roster_id
            LIMIT 1
          ) rt3 ON true
          LEFT JOIN courts c ON c.uuid = m.court_id
        WHERE m.pool_bracket_id = pb.uuid
          AND m.secs_finished IS NOT NULL
        ORDER BY m.secs_start,
          c.sort_priority,
          m.match_number
      ) resmatches
  ) "results",
  (
    SELECT array_to_json(array_agg(row_to_json(matches_standings)))
    FROM (
        SELECT pb.display_name,
          d.short_name "division_short_name",
          d.name "division_name",
          pb.pb_stats
        FROM poolbrackets pb
          LEFT JOIN division d ON d.division_id = pb.division_id
        WHERE pb.event_id = e.event_id
          AND pb.uuid = CAST($1 AS UUID)
      ) matches_standings
  ) "standings"
FROM poolbrackets pb
  INNER JOIN "event" e ON e.event_id = pb.event_id
  LEFT JOIN rounds r ON pb.round_id = r.uuid
  LEFT JOIN rounds rr ON pb.group_id = rr.uuid
WHERE pb.uuid = CAST($1 AS UUID)
  AND e.esw_id = $2
`;

export const getPoolSiblingsQuery = () => `
	SELECT pb.uuid,
		CONCAT(rr.short_name, ' ', pb.name) AS name,
		pb.is_pool
	FROM poolbrackets pb
		INNER JOIN rounds r ON r.uuid = pb.round_id
		LEFT JOIN rounds rr ON pb.group_id = rr.uuid
	WHERE pb.division_short_name = $1
		AND pb.event_id = $2
	ORDER BY r.sort_priority,
		rr.sort_priority,
		pb.sort_priority
`;

export const getFirstPoolIdByTeamId = () => `
	SELECT
		m.pool_bracket_id
	FROM matches m
	WHERE
		m.team1_roster_id = $1::INTEGER
		OR 
		m.team2_roster_id = $1::INTEGER
	GROUP BY m.pool_bracket_id
	ORDER BY MIN(m.secs_start);
`;
