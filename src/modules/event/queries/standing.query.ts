export const getDivisionDetailsForEventQuery = () => `
	SELECT o.heading_group,
		ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(o))) "teams"
	FROM (
			SELECT CAST(ds.info->>'seed_current' AS INTEGER) "seed_current",
				rt.roster_team_id,
				rt.team_name,
				rt.organization_code,
				rt.extra->>'show_accepted_bid' "show_accepted_bid",
				rt.extra->>'show_previously_accepted_bid' "show_previously_accepted_bid",
				rc.club_name,
				rc.city,
				rc.state,
				d.short_name "division_short_name",
				d.name "division_name",
				d.level "division_level",
				d.division_id "division_id",
				ds.matches_won,
				ds.matches_lost,
				ds.sets_won,
				ds.sets_lost,
				ds.points_won,
				ds.points_lost,
				ds.sets_pct,
				ds.points_ratio,
				ds.rank,
				ds.info,
				COALESCE(NULLIF(ds.info->>'heading', ''), 'None') "heading_group",
				CAST(ds.info->>'heading_sort_priority' AS INTEGER) "heading_sorting_group"
			FROM roster_team rt
				inner join "event" e on e.event_id = rt.event_id
				LEFT JOIN roster_club rc ON rc.roster_club_id = rt.roster_club_id
				LEFT JOIN division d ON rt.division_id = d.division_id
				LEFT JOIN division_standing ds ON rt.division_id = ds.division_id
				AND ds.team_id = rt.roster_team_id
			WHERE e.esw_id = $1
				AND rt.division_id = ANY($2::integer[])
				AND rt.deleted IS NULL
				AND d.published IS TRUE
			ORDER BY CAST(ds.info->>'heading_sort_priority' AS INTEGER),
				ds.info->>'heading',
				ds.rank,
				CASE
					WHEN e.hide_seeds IS TRUE THEN rt.team_name
				END,
				CASE
					WHEN e.hide_seeds IS NOT TRUE THEN CAST(ds.info->>'seed_current' AS INTEGER)
				END,
				ds.sets_pct DESC,
				ds.points_ratio DESC
		) o
	GROUP BY o.heading_group,
		o.heading_sorting_group
	ORDER BY o.heading_sorting_group
`;
