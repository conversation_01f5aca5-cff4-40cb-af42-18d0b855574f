import { RawSqlVarHelper, RawSqlValue } from '@/shared/utils/sql';

export type PaginatedEventsQueryParams = {
	offset?: number;
	limit?: number;
	startBefore?: string;
	startAfter?: string;
	endBefore?: string;
	endAfter?: string;
	search?: string;
	years?: string[];
	asc?: boolean;
};

export const getPaginatedEventsQuery = ({
	offset,
	limit,
	startBefore,
	startAfter,
	endBefore,
	endAfter,
	search,
	years,
	asc = true,
}: PaginatedEventsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	let query = `
    SELECT (
      CASE
        WHEN e.schedule_published IS TRUE THEN e.esw_id
        ELSE e.event_id::VARCHAR
      END
    ) "event_id",
    e.name,
    e.long_name,
    e.date_start,
    e.date_end,
    e.timezone,
    e.city,
    e.state,
    e.address,
    e.has_coed_teams,
    e.has_female_teams,
    e.has_male_teams,
    e.email,
    e.website,
    concat(em.file_path, '.', em.file_ext) as small_logo,
    TRUE has_athletes,
    TRUE has_staff,
    (
      e.date_start <= (NOW() AT TIME ZONE e.timezone)
      AND (NOW() AT TIME ZONE e.timezone) <= e.date_end
      AND e.schedule_published IS TRUE
      AND e.is_test IS NOT TRUE
      AND e.deleted IS NULL
    ) "current",
    e.schedule_published,
    COUNT(*) OVER() AS "item_count"
    FROM "event" e
    LEFT JOIN event_media em on em.event_id = e.event_id AND em.file_type = 'small-logo'
    WHERE e.live_to_public IS TRUE
    AND e.allow_teams_registration IS TRUE
    AND e.esw_id IS NOT NULL
    AND e.registration_method <> 'doubles'
  `;

	if (years && years.length > 0) {
		const yearValues = years.map((year) => sqlVars.useValue(year));
		query += `AND TO_CHAR(e.date_start, 'YYYY') IN (${yearValues.join(', ')}) `;
	}

	if (startBefore) {
		query += `AND (e.date_start AT TIME ZONE e.timezone) < ${sqlVars.useValue(startBefore)}::timestamptz `;
	}

	if (startAfter) {
		query += `AND (e.date_start AT TIME ZONE e.timezone) > ${sqlVars.useValue(startAfter)}::timestamptz `;
	}

	if (endBefore) {
		query += `AND (e.date_end AT TIME ZONE e.timezone) < ${sqlVars.useValue(endBefore)}::timestamptz `;
	}

	if (endAfter) {
		query += `AND (e.date_end AT TIME ZONE e.timezone) > ${sqlVars.useValue(endAfter)}::timestamptz `;
	}

	search = search?.trim();
	if (search) {
		const sVal = sqlVars.useValue(search);
		const wcVal = sqlVars.useValue(`%${search}%`); // wildcard
		query += `AND (
			(CASE
				WHEN LENGTH(${sVal}) IN (1, 2) AND ${sVal} ~ '^[0-9]+$' THEN
					TO_CHAR(e.date_start, 'FMDD') = ${sVal}
				WHEN LENGTH(${sVal}) = 2 AND ${sVal} !~ '^[0-9]+$' THEN
					e.state ILIKE ${wcVal}
				ELSE
					e.long_name ILIKE ${wcVal}
					OR e.city ILIKE ${wcVal}
					OR e.address ILIKE ${wcVal}
					OR TO_CHAR(e.date_start, 'Mon') ILIKE ${wcVal}
					OR TO_CHAR(e.date_start, 'Month') ILIKE ${wcVal}
					OR TO_CHAR(e.date_start, 'DD') ILIKE ${wcVal}
					OR TO_CHAR(e.date_start, 'YYYY') ILIKE ${wcVal}
					OR (TO_CHAR(e.date_start, 'Mon') || ' ' || TO_CHAR(e.date_start, 'DD') || ' ' || TO_CHAR(e.date_start, 'YYYY')) ILIKE ${wcVal}
					OR (TO_CHAR(e.date_start, 'Month') || ' ' || TO_CHAR(e.date_start, 'DD') || ' ' || TO_CHAR(e.date_start, 'YYYY')) ILIKE ${wcVal}
					OR (TO_CHAR(e.date_start, 'Month') || ' ' || TO_CHAR(e.date_start, 'YYYY')) ILIKE ${wcVal}
					OR (TO_CHAR(e.date_start, 'Mon') || ' ' || TO_CHAR(e.date_start, 'YYYY')) ILIKE ${wcVal}
			END)
		) `;
	}

	query += `ORDER BY (e.date_start AT TIME ZONE e.timezone) ${asc ? 'ASC' : 'DESC'}, e.event_id ${asc ? 'ASC' : 'DESC'} `;

	if (offset) {
		query += `OFFSET ${sqlVars.useValue(offset)} `;
	}

	if (limit) {
		query += `LIMIT ${sqlVars.useValue(limit)} `;
	}

	return [query, sqlVars.getValues()];
};
