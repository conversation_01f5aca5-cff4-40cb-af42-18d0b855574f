import { ArgsType, Field, ID } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';

import { IsEventKeyValue } from '@/shared/graphql/dto/validators/is-event-key-value.validator';
import { IsNumericId } from '@/shared/graphql/dto/validators/is-numeric-id.validator';

@ArgsType()
export class PoolByTeamInputDto {
	@Field(() => ID)
	@IsEventKeyValue()
	id: string;

	@Field(() => ID)
	@IsNumericId()
	teamId: number;

	@Field(() => ID, { nullable: true })
	@IsOptional()
	poolId?: string;
}
