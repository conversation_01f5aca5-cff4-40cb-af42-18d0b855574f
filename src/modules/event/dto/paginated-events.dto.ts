import { ArgsType, Field } from '@nestjs/graphql';
import { IsOptional, IsString, Matches, ArrayMaxSize, IsISO8601 } from 'class-validator';

import { PaginationInputDto } from '@/shared/graphql/dto/pagination.dto';
import { IsSearchField } from '@/shared/graphql/dto/validators/is-search-field.validator';

import { MIN_SEARCH_LENGTH } from '../constants';

const YearRegExp = /^\d{4}$/;

@ArgsType()
export class PaginatedEventsInputDto extends PaginationInputDto {
	@Field(() => String, { nullable: true })
	@IsOptional()
	@IsSearchField(MIN_SEARCH_LENGTH)
	search?: string;

	@Field(() => String, { nullable: true })
	@IsOptional()
	@IsISO8601({}, { message: '"startBefore" must be a valid ISO 8601 timestamp' })
	startBefore?: string;

	@Field(() => String, { nullable: true })
	@IsOptional()
	@IsISO8601({}, { message: '"startAfter" must be a valid ISO 8601 timestamp' })
	startAfter?: string;

	@Field(() => String, { nullable: true })
	@IsOptional()
	@IsISO8601({}, { message: '"endBefore" must be a valid ISO 8601 timestamp' })
	endBefore?: string;

	@Field(() => String, { nullable: true })
	@IsOptional()
	@IsISO8601({}, { message: '"endAfter" must be a valid ISO 8601 timestamp' })
	endAfter?: string;

	@Field(() => [String], { nullable: true })
	@IsOptional()
	@IsString({ each: true })
	@ArrayMaxSize(20, { message: 'number of years must be less than 20' })
	@Matches(YearRegExp, { each: true, message: 'each year must be a valid 4-digit string' })
	years?: string[];

	@Field(() => Boolean, { nullable: true })
	@IsOptional()
	asc?: boolean;
}
