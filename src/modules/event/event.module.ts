import { Modu<PERSON> } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { EVENTS_INDEX_SCHEMA } from './constants';
import { EventSearchRepository } from './event-search.repository';
import { EventSearchService } from './event-search.service';
import { EventRepository } from './event.repository';
import { EventResolver } from './event.resolver';
import { EventService } from './event.service';
import { BracketModule } from '../bracket/bracket.module';
import { CourtGridModule } from '../courtGrid/courtGrid.module';
import { SearchModule } from '../search/search.module';
import { SearchEntityName } from '../search/types';

@Module({
	imports: [
		PrismaModule,
		CacheModule,
		CourtGridModule,
		BracketModule,
		SearchModule.forFeature(SearchEntityName.Events, EVENTS_INDEX_SCHEMA),
	],
	providers: [EventResolver, EventService, EventRepository, EventSearchRepository, EventSearchService],
})
export class EventModule {}
