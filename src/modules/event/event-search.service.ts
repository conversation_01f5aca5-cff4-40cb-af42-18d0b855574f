import { Injectable, OnModuleInit } from '@nestjs/common';

import configuration from '@/infra/configuration';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { MIN_SEARCH_LENGTH } from './constants';
import { EventSearchRepository } from './event-search.repository';
import { EventSearchIndexEntry } from './types';
import { InjectSearchManager } from '../search/decorators';
import { SearchEntityName } from '../search/types';
import { RedisSearchManager, Search } from '../search/utils/redis-search-manager';

type DateRange = {
	startBefore?: string;
	startAfter?: string;
	endBefore?: string;
	endAfter?: string;
};
export type FindPaginatedEventsIdsParams = {
	search?: string;
	years?: string[];
	asc?: boolean;
} & PageParams &
	DateRange;

const SEARCH_SERVICE_DISABLED = !configuration.USE_SEARCH_SERVICES;
const EVENTS_ALIAS = 'events';

@Injectable()
export class EventSearchService implements OnModuleInit {
	constructor(
		@InjectSearchManager(SearchEntityName.Events) private readonly searchManager: RedisSearchManager<EventSearchIndexEntry>,
		private eventSearchRepository: EventSearchRepository,
	) {}

	async onModuleInit() {
		if (SEARCH_SERVICE_DISABLED) return;
		return this.searchManager.createOrUpdateIndex();
	}

	canSearchWith<T extends { search?: string; years?: string[] }>(params: T): boolean {
		if (SEARCH_SERVICE_DISABLED) return false;
		return params.search?.trim().length >= MIN_SEARCH_LENGTH || params.years?.length > 0;
	}

	async findPaginatedEventsIds(params: FindPaginatedEventsIdsParams): Promise<PageResults<string>> {
		await this.actualizeIndexEntries();

		const { search, years, asc } = params;
		let queryBuilder = this.searchManager.find();
		queryBuilder = this._withTextSearch(queryBuilder, search);
		queryBuilder = this._withDateRangeSearch(queryBuilder, params);
		queryBuilder = this._withYearsSearch(queryBuilder, years);

		const { offset, limit } = toOffsetLimit(params);
		const [items, itemCount] = await Promise.all([
			queryBuilder.sortBy('sort_order', asc ? 'ASC' : 'DESC').return.page(offset, limit),
			queryBuilder.count(), // TODO Get the count from the search result
		]);
		return { items: items.map((item) => item.event_id), itemCount };
	}

	actualizeIndexEntries(): Promise<void> {
		return this.searchManager.actualizeIndexEntries(EVENTS_ALIAS, async () => {
			const entries = await this.eventSearchRepository.fetchEventsSearchIndexEntries();
			return [entries, [EVENTS_ALIAS]];
		});
	}

	clearIndexEntries(): Promise<void> {
		return this.searchManager.clearIndexEntries(EVENTS_ALIAS);
	}

	private _withTextSearch(queryBuilder: Search<EventSearchIndexEntry>, search = ''): Search<EventSearchIndexEntry> {
		const searchContent = this.searchManager.sanitizeSearchText(search);
		if (searchContent.length < MIN_SEARCH_LENGTH) {
			return queryBuilder;
		}

		if (searchContent.length <= 2 && /^\d+$/.test(searchContent)) {
			return queryBuilder.where('day').eq(parseInt(searchContent));
		} else if (searchContent.length === 2) {
			return queryBuilder.where('state').eq(searchContent);
		} else {
			return queryBuilder.and((builder) => {
				return builder.or('search_content').matches(searchContent).or('search_content').matches(`*${searchContent}*`);
			});
		}
	}

	private _withDateRangeSearch(
		queryBuilder: Search<EventSearchIndexEntry>,
		{ startBefore, startAfter, endBefore, endAfter }: DateRange,
	): Search<EventSearchIndexEntry> {
		if (startBefore) {
			queryBuilder = queryBuilder.and('date_start').lt(new Date(startBefore).getTime());
		}
		if (startAfter) {
			queryBuilder = queryBuilder.and('date_start').gt(new Date(startAfter).getTime());
		}
		if (endBefore) {
			queryBuilder = queryBuilder.and('date_end').lt(new Date(endBefore).getTime());
		}
		if (endAfter) {
			queryBuilder = queryBuilder.and('date_end').gt(new Date(endAfter).getTime());
		}

		return queryBuilder;
	}

	private _withYearsSearch(queryBuilder: Search<EventSearchIndexEntry>, years?: string[]): Search<EventSearchIndexEntry> {
		if (!years?.length) return queryBuilder;
		return queryBuilder.and((search) => {
			return years.reduce((search, year) => {
				search.or('year').eq(year);
				return search;
			}, search);
		});
	}
}
