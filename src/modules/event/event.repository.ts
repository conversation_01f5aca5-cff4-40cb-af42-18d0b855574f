import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { Club } from '@/modules/club/entities';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import {
	Event,
	EventDivision,
	EventAthlete,
	TeamList,
	TeamSingle,
	Standing,
	NextPrevPool,
	EventPool,
	PoolId,
	DivisionPool,
	FeatureMatches,
	EventQualifiedTeam,
	UpcomingMatches,
} from './entities';
import { getAthletesForEventQuery } from './queries/athletes.query';
import { getClubsForEventQuery } from './queries/clubs.query';
import { getPoolsForEventDivisionQuery } from './queries/division-pools.query';
import { getDivisionsForEventQuery } from './queries/divisions.query';
import { getEventQuery } from './queries/event.query';
import { EventsQueryParams, getEventsQuery } from './queries/events.query';
import { getFeatureMatchesForEventQuery } from './queries/feature-matches.query';
import { getPaginatedEventsQuery, PaginatedEventsQueryParams } from './queries/paginated-events.query';
import { getPoolForQuery, getFirstPoolIdByTeamId, getPoolSiblingsQuery } from './queries/pool.query';
import { getQualifiedTeamsForEventQuery } from './queries/qualifiedTeams.query';
import { getDivisionDetailsForEventQuery } from './queries/standing.query';
import { getSingleTeamForEventQuery, getTeamsForEventQuery } from './queries/teams.query';
import { getUpcomingMatchesForEventQuery } from './queries/upcomingMatches.query';

export type FetchEventsParams = EventsQueryParams;
export type FetchPaginatedEventsParams = Omit<PaginatedEventsQueryParams, 'offset' | 'limit'> & PageParams;

@Injectable()
export class EventRepository {
	constructor(private prisma: PrismaService) {}

	fetchEvents(params: FetchEventsParams): Promise<Event[]> {
		const [query, values] = getEventsQuery(params);
		return this.prisma.$queryRawUnsafe<Event[]>(query, ...values);
	}

	async fetchPaginatedEvents(params: FetchPaginatedEventsParams): Promise<PageResults<Event>> {
		const [query, values] = getPaginatedEventsQuery({
			...params,
			...toOffsetLimit(params),
		});

		const items = await this.prisma.$queryRawUnsafe<Event[]>(query, ...values);
		if (!items.length) {
			return { items, itemCount: 0 };
		}
		// Get the item count from the first row as it is the same for all rows
		return {
			items,
			itemCount: Number((items[0] as unknown as Record<string, string>).item_count),
		};
	}

	async fetchEvent(eventID: string | number, isESWID: boolean) {
		const response = await this.prisma.$queryRawUnsafe<Event[]>(getEventQuery(isESWID), eventID);
		return response[0];
	}
	async fetchDivisionsForEvent(eventID: string) {
		const response = await this.prisma.$queryRawUnsafe<EventDivision[]>(getDivisionsForEventQuery(), eventID);
		return response;
	}
	async fetchClubsForEvent(eventID: string) {
		const response = await this.prisma.$queryRawUnsafe<Club[]>(getClubsForEventQuery(), eventID);
		return response;
	}
	async fetchAthletesForEvent(eventID: string) {
		const response = await this.prisma.$queryRawUnsafe<EventAthlete[]>(getAthletesForEventQuery(), eventID);
		return response;
	}
	async fetchTeamsForEvent(eventID: string) {
		const response = await this.prisma.$queryRawUnsafe<TeamList[]>(getTeamsForEventQuery(), eventID);
		return response;
	}
	async fetchDivisionDetails(eventID: string, divisionIds: string[]) {
		const response = await this.prisma.$queryRawUnsafe<Standing[]>(getDivisionDetailsForEventQuery(), eventID, divisionIds);
		return response;
	}

	async fetchPoolsForEventDivision(eventID: string, divisionId: string) {
		const response = await this.prisma.$queryRawUnsafe<DivisionPool[]>(getPoolsForEventDivisionQuery(), eventID, divisionId);
		return response;
	}
	async fetchPoolForEvent(eventID: string, poolId: string) {
		const response = await this.prisma.$queryRawUnsafe<EventPool[]>(getPoolForQuery(), poolId, eventID);
		return response[0];
	}
	async fetchSiblingsOfPoolForEvent(pool: EventPool) {
		const response = await this.prisma.$queryRawUnsafe<NextPrevPool[]>(getPoolSiblingsQuery(), pool.division_short_name, pool.event_id);
		return response;
	}
	async fetchSingleTeamForEvent(eventID: string, teamId: string) {
		const response = await this.prisma.$queryRawUnsafe<TeamSingle>(getSingleTeamForEventQuery(), eventID, teamId);
		return response[0];
	}
	async fetchFeatureMatchesForEvent(eventID: string) {
		const response = await this.prisma.$queryRawUnsafe<FeatureMatches[]>(getFeatureMatchesForEventQuery(), eventID);
		return response;
	}
	async fetchFirstPoolIdByTeamId(teamId: number) {
		const response = await this.prisma.$queryRawUnsafe<PoolId[]>(getFirstPoolIdByTeamId(), teamId);
		return response[0];
	}
	async fetchQualifiedTeamsForEvent(eventID: string) {
		const response = await this.prisma.$queryRawUnsafe<EventQualifiedTeam[]>(getQualifiedTeamsForEventQuery(), eventID);
		return response;
	}
	async fetchUpcomingMatchesForEvent(eventID: string) {
		const response = await this.prisma.$queryRawUnsafe<UpcomingMatches[]>(getUpcomingMatchesForEventQuery(), eventID);

		return response;
	}
}
