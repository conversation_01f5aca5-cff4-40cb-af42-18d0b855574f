import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class FeatureMatches {
	@Field({ nullable: true })
	match_id: string;

	@Field({ nullable: true })
	division_id: string;

	@Field({ nullable: true })
	team1_id: string;

	@Field({ nullable: true })
	team1_name: string;

	@Field({ nullable: true })
	team2_id: string;

	@Field({ nullable: true })
	team2_name: string;

	@Field({ nullable: true })
	court_name: string;

	@Field({ nullable: true })
	court_short_name: string;

	@Field({ nullable: true })
	match_start: string;
}
