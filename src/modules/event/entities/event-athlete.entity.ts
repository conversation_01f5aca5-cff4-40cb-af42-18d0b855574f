import { Field, Int, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class EventAthlete {
	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	roster_team_id: number;

	@Field({ nullable: true })
	first: string;

	@Field({ nullable: true })
	last: string;

	@Field({ nullable: true })
	team_name: string;

	@Field({ nullable: true })
	team_organization_code: string;

	@Field({ nullable: true })
	jersey?: number;

	@Field()
	club_name: string;

	@Field({ nullable: true })
	gender: 'male' | 'female';

	@Field(() => Int, { nullable: true })
	age: number;
}
