import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class TeamList {
	@Field({ nullable: true })
	division_name?: string;

	@Field({ nullable: true })
	division_short_name?: string;

	@Field({ nullable: true })
	club_name?: string;

	@Field({ nullable: true })
	state?: string;

	@Field({ nullable: true })
	team_name?: string;

	@Field({ nullable: true })
	club_state?: string;

	@Field({ nullable: true })
	organization_code?: string;

	@Field({ nullable: true })
	roster_team_id: number;

	@Field({ nullable: true })
	roster_club_id: number;

	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	gender: 'male' | 'female';
}
