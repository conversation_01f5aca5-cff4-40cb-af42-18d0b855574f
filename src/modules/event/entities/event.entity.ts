import { ObjectType, Field, ID } from '@nestjs/graphql';

import { Club } from '@/modules/club/entities';

import { EventAthlete } from './event-athlete.entity';
import { EventDivision } from './event-division.entity';
import { TeamList } from './team-list.entity';

@ObjectType()
class EventLocation {
	@Field({ nullable: true })
	location_name: string;

	@Field({ nullable: true })
	address: string;

	@Field({ nullable: true })
	city: string;

	@Field({ nullable: true })
	state: string;

	@Field({ nullable: true })
	zip: string;

	@Field({ nullable: true })
	number: number;
}

@ObjectType()
class EventTeamsSettings {
	@Field({ nullable: true })
	sort_by: string;

	@Field({ nullable: true })
	hide_standings: boolean;

	@Field({ nullable: true })
	manual_club_names: boolean;

	@Field({ nullable: true })
	manual_teams_addition: boolean;

	@Field({ nullable: true })
	baller_tv_available: boolean;
}

@ObjectType()
export class Event {
	@Field({ nullable: true })
	hide_seeds: boolean;

	@Field({ nullable: true })
	has_rosters: boolean;

	@Field(() => ID, { nullable: true })
	event_id: string;

	@Field(() => Date, { nullable: true })
	modified?: Date;

	@Field({ nullable: true })
	id: number;

	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	long_name: string;

	@Field({ nullable: true })
	event_notes: string;

	@Field({ nullable: true })
	address: string;

	@Field({ nullable: true })
	small_logo: string;

	@Field({ nullable: true })
	city?: string;

	@Field({ nullable: true })
	is_with_prev_qual?: boolean;

	@Field({ nullable: true })
	date_start?: string;

	@Field({ nullable: true })
	state?: string;

	@Field({ nullable: true })
	schedule_published?: boolean;

	@Field({ nullable: true })
	tickets_published?: boolean;

	@Field({ nullable: true })
	is_require_recipient_name_for_each_ticket?: boolean;

	@Field({ nullable: true })
	tickets_code?: string;

	@Field(() => Boolean, { nullable: true })
	has_officials?: boolean | null;

	@Field(() => [EventDivision])
	divisions?: EventDivision[];

	@Field(() => [Club])
	clubs?: Club[];

	@Field(() => [TeamList])
	teams?: TeamList[];

	@Field(() => [EventAthlete])
	athletes?: EventAthlete[];

	@Field(() => [String], { nullable: true })
	days: string[];

	@Field(() => [EventLocation], { nullable: true })
	locations: EventLocation[];

	@Field(() => EventTeamsSettings, { nullable: true })
	teams_settings: EventTeamsSettings;

	@Field({ nullable: true })
	sport_sanctioning: string;
}
