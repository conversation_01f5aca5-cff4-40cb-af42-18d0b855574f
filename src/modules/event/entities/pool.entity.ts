import { ObjectType, Field } from '@nestjs/graphql';

import { PoolOrBracketSettings } from '@/modules/poolBracket/entities';
import { TeamAdvancement } from '@/shared/graphql/entities/team-advancement.entity';

@ObjectType({
	description: 'Generate a link to the next pool',
})
export class NextPrevPool {
	@Field({ nullable: true })
	is_pool: number;

	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	uuid: string;
}

@ObjectType()
export class PoolResultsResultsTeam {
	@Field({ nullable: true })
	heading: string;

	@Field({ nullable: true })
	heading_sort: number;

	@Field({ nullable: true })
	matches_lost: number;

	@Field({ nullable: true })
	matches_pct: number;

	@Field({ nullable: true })
	matches_won: number;

	@Field({ nullable: true })
	overallSeed: number;

	@Field({ nullable: true })
	points_lost: number;

	@Field({ nullable: true })
	points_ratio: number;

	@Field({ nullable: true })
	points_won: number;

	@Field({ nullable: true })
	roster_team_id: number;

	@Field({ nullable: true })
	scores: string;

	@Field({ nullable: true })
	sets_lost: number;

	@Field({ nullable: true })
	sets_pct: number;

	@Field({ nullable: true })
	sets_won: number;

	@Field({ nullable: true })
	title: string;
}

@ObjectType()
export class PoolResultsResults {
	@Field({ nullable: true })
	set1: string;

	@Field({ nullable: true })
	set2: string;

	@Field({ nullable: true })
	set3: string;

	@Field({ nullable: true })
	set4: string;

	@Field({ nullable: true })
	set5: string;

	@Field(() => PoolResultsResultsTeam, { nullable: true })
	team1: PoolResultsResultsTeam;

	@Field(() => PoolResultsResultsTeam, { nullable: true })
	team2: PoolResultsResultsTeam;
}

@ObjectType()
export class PoolResults {
	@Field({ nullable: true })
	court_name: string;

	@Field({ nullable: true })
	date_start?: string;

	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	match_id: string;

	@Field({ nullable: true })
	ref_roster_id: string;

	@Field({ nullable: true })
	ref_team_code: string;

	@Field({ nullable: true })
	ref_team_name: string;

	@Field({ nullable: true })
	team1_code: string;

	@Field({ nullable: true })
	team1_name: string;

	@Field({ nullable: true })
	team1_roster_id: number;

	@Field({ nullable: true })
	team1_master_id: number;

	@Field({ nullable: true })
	team2_code: string;

	@Field({ nullable: true })
	team2_name: string;

	@Field({ nullable: true })
	team2_roster_id: number;

	@Field({ nullable: true })
	team2_master_id: number;

	@Field(() => PoolResultsResults)
	results: PoolResultsResults;
}

@ObjectType()
export class PoolStandingsStats {
	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	rank: string;

	@Field({ nullable: true })
	team_id: string;

	@Field({ nullable: true })
	sets_pct: number;

	@Field({ nullable: true })
	sets_won: number;

	@Field({ nullable: true })
	sets_lost: number;

	@Field({ nullable: true })
	points_won: number;

	@Field({ nullable: true })
	matches_pct: number;

	@Field({ nullable: true })
	matches_won: number;

	@Field({ nullable: true })
	points_lost: number;

	@Field({ nullable: true })
	matches_lost: number;

	@Field({ nullable: true })
	points_ratio: number;
}

@ObjectType()
export class PoolStandings {
	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	division_name: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field(() => [PoolStandingsStats])
	pb_stats: PoolStandingsStats[];
}

@ObjectType()
export class PoolFuture {
	@Field(() => [String])
	team_ids: string[];

	@Field(() => [TeamAdvancement])
	teams: TeamAdvancement[];
}

@ObjectType()
export class PoolUpcomingMatches {
	@Field({ nullable: true })
	court_name: string;

	@Field({ nullable: true })
	date_start: number;

	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	footnote_play: string;

	@Field({ nullable: true })
	footnote_team1: string;

	@Field({ nullable: true })
	footnote_team2: string;

	@Field({ nullable: true })
	match_id: string;

	@Field({ nullable: true })
	ref_roster_id: string;

	@Field({ nullable: true })
	ref_team_code: string;

	@Field({ nullable: true })
	ref_team_name: string;

	@Field({ nullable: true })
	results: string;

	@Field({ nullable: true })
	team1_code: string;

	@Field({ nullable: true })
	team1_name: string;

	@Field({ nullable: true })
	team1_roster_id: string;

	@Field({ nullable: true })
	team1_master_id: string;

	@Field({ nullable: true })
	team2_code: string;

	@Field({ nullable: true })
	team2_name: string;

	@Field({ nullable: true })
	team2_roster_id: string;

	@Field({ nullable: true })
	team2_master_id: string;
}

@ObjectType()
export class EventPool {
	@Field({ nullable: true })
	event_id: number;

	@Field({ nullable: true })
	uuid: string;

	@Field({ nullable: true })
	r_uuid: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	division_id: string;

	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	display_name_short: string;

	@Field({ nullable: true })
	team_count: number;

	@Field({ nullable: true })
	is_pool: boolean;

	@Field(() => NextPrevPool, { nullable: true })
	next: NextPrevPool;

	@Field(() => NextPrevPool, { nullable: true })
	prev: NextPrevPool;

	@Field(() => [PoolResults], { nullable: true })
	results: PoolResults[];

	@Field(() => [PoolStandings], { nullable: true })
	standings: PoolStandings[];

	@Field(() => PoolFuture, { nullable: true })
	pb_finishes: PoolFuture;

	@Field(() => [PoolUpcomingMatches], { nullable: true })
	upcoming_matches: PoolUpcomingMatches[];

	@Field(() => PoolOrBracketSettings, { nullable: true })
	settings?: PoolOrBracketSettings;
}

@ObjectType()
export class PoolId {
	@Field({ nullable: true, name: 'pool_id' })
	pool_bracket_id: string;
}
