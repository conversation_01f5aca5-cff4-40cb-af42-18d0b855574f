import { ObjectType, Field } from '@nestjs/graphql';

import { PoolOrBracketSettings } from '@/modules/poolBracket/entities';

@ObjectType()
export class DivisionPoolTeamInfo {
	@Field({ nullable: true })
	title: string;

	@Field({ nullable: true })
	heading: string;

	@Field({ nullable: true })
	seed_current: number;

	@Field({ nullable: true })
	seed_original: number;

	@Field({ nullable: true })
	heading_sort_priority: number;
}

@ObjectType()
export class DivisionPoolTeam {
	@Field({ nullable: true })
	opponent_team_name: string;

	@Field({ nullable: true })
	opponent_organization_code: string;

	@Field({ nullable: true })
	opponent_team_id: number;

	@Field({ nullable: true })
	rank: number;

	@Field({ nullable: true })
	is_empty_team_pb_stats: boolean;

	@Field({ nullable: true })
	matches_won: string;

	@Field({ nullable: true })
	matches_lost: string;

	@Field({ nullable: true })
	sets_won: string;

	@Field({ nullable: true })
	sets_lost: string;

	@Field({ nullable: true })
	points_ratio: number;

	@Field({ nullable: true })
	sets_pct: number;

	@Field(() => DivisionPoolTeamInfo)
	info: DivisionPoolTeamInfo;
}

@ObjectType()
export class DivisionPool {
	@Field({ nullable: true })
	r_uuid: string;

	@Field({ nullable: true })
	r_sort_priority: number;

	@Field({ nullable: true })
	sort_priority: number;

	@Field({ nullable: true })
	rr_sort_priority: number;

	@Field({ nullable: true })
	r_name: string;

	@Field({ nullable: true })
	r_short_name: string;

	@Field({ nullable: true })
	is_pool: number;

	@Field({ nullable: true })
	pb_name: string;

	@Field({ nullable: true })
	pb_short_name: string;

	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	team_count: number;

	@Field({ nullable: true })
	uuid: string;

	@Field({ nullable: true })
	rr_name: string;

	@Field({ nullable: true })
	rr_short_name: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	is_empty_pb_stats: boolean;

	@Field({ nullable: true })
	date_start: number;

	@Field({ nullable: true })
	court_start: string;

	@Field({ nullable: true })
	settings: PoolOrBracketSettings;

	@Field(() => [DivisionPoolTeam], { nullable: true })
	teams: DivisionPoolTeam[];
}
