import { ObjectType, Field } from '@nestjs/graphql';

import { MatchFinishes } from '@/modules/match/entities';
import { PoolOrBracketSettings } from '@/modules/poolBracket/entities';

import { PoolFuture, PoolStandingsStats } from './pool.entity';

@ObjectType()
class EventUpcoming {
	@Field({ nullable: true })
	match_type: string;

	@Field({ nullable: true })
	match_id: string;

	@Field({ nullable: true })
	unix_finished: string;

	@Field({ nullable: true })
	results: string;

	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	date_start_formatted: string;

	@Field({ nullable: true })
	date_start: string;

	@Field({ nullable: true })
	division_id: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	court_name: string;

	@Field({ nullable: true })
	pool_name: string;

	@Field({ nullable: true })
	pool_bracket_id: string;

	@Field({ nullable: true })
	is_pool: number;

	@Field({ nullable: true })
	opponent_team_name: string;

	@Field({ nullable: true })
	opponent_organization_code: string;

	@Field({ nullable: true })
	pb_name: string;

	@Field({ nullable: true })
	round_name: string;

	@Field({ nullable: true })
	footnote_play: string;

	@Field({ nullable: true })
	footnote_team1: string;

	@Field({ nullable: true })
	footnote_team2: string;

	@Field(() => PoolOrBracketSettings, { nullable: true })
	settings?: PoolOrBracketSettings;
}

@ObjectType()
export class TeamSingleStaff {
	@Field({ nullable: true })
	first: string;

	@Field({ nullable: true })
	last: string;

	@Field({ nullable: true })
	sort_order: number;

	@Field({ nullable: true })
	role_name: string;
}

@ObjectType()
export class TeamSingleAthlete {
	@Field({ nullable: true })
	first: string;

	@Field({ nullable: true })
	last: string;

	@Field({ nullable: true })
	uniform: number;

	@Field({ nullable: true })
	short_position: string;

	@Field({ nullable: true })
	gradyear: number;

	@Field({ nullable: true })
	height: string;
}

@ObjectType()
export class TeamSingleMatchResultsTeam {
	@Field({ nullable: true })
	heading: string;

	@Field({ nullable: true })
	heading_sort: number;

	@Field({ nullable: true })
	matches_lost: number;

	@Field({ nullable: true })
	matches_pct: number;

	@Field({ nullable: true })
	matches_won: number;

	@Field({ nullable: true })
	overallSeed: number;

	@Field({ nullable: true })
	points_lost: number;

	@Field({ nullable: true })
	points_ratio: number;

	@Field({ nullable: true })
	points_won: number;

	@Field({ nullable: true })
	roster_team_id: number;

	@Field({ nullable: true })
	scores: string;

	@Field({ nullable: true })
	sets_lost: number;

	@Field({ nullable: true })
	sets_pct: number;

	@Field({ nullable: true })
	sets_won: number;

	@Field({ nullable: true })
	title: string;
}

@ObjectType()
export class TeamSingleMatchResults {
	@Field({ nullable: true })
	set1: string;

	@Field({ nullable: true })
	set2: string;

	@Field({ nullable: true })
	set3: string;

	@Field({ nullable: true })
	set4: string;

	@Field({ nullable: true })
	set5: string;

	@Field({ nullable: true })
	winner: string;

	@Field(() => TeamSingleMatchResultsTeam)
	team1: TeamSingleMatchResultsTeam;

	@Field(() => TeamSingleMatchResultsTeam)
	team2: TeamSingleMatchResultsTeam;
}

@ObjectType()
export class TeamSingleMatch {
	@Field({ nullable: true })
	date_start: number;

	@Field({ nullable: true })
	date_start_formatted: string;

	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	match_id: string;

	@Field({ nullable: true })
	match_type: string;

	@Field({ nullable: true })
	opponent_organization_code: string;

	@Field({ nullable: true })
	opponent_team_id: number;

	@Field({ nullable: true })
	opponent_team_name: string;

	@Field({ nullable: true })
	pool_bracket_id: string;

	@Field({ nullable: true })
	unix_finished: number;

	@Field(() => TeamSingleMatchResults, { nullable: true })
	results: TeamSingleMatchResults;
}

@ObjectType()
export class TeamSingleResults {
	@Field({ nullable: true })
	uuid: string;

	@Field({ nullable: true })
	is_pool: number;

	@Field({ nullable: true })
	pb_name: string;

	@Field({ nullable: true })
	round_name: string;

	@Field({ nullable: true })
	sort_priority: number;

	@Field(() => [PoolStandingsStats], { nullable: true })
	pb_stats: PoolStandingsStats[];

	@Field(() => [TeamSingleMatch])
	matches: TeamSingleMatch[];
}

@ObjectType()
export class TeamSinglePbInfo {
	@Field({ nullable: true })
	uuid: string;

	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	is_pool: number;

	@Field(() => PoolFuture, { nullable: true })
	pb_finishes: PoolFuture;
}

@ObjectType()
export class TeamSingle {
	@Field({ nullable: true })
	roster_team_id: number;

	@Field({ nullable: true })
	master_team_id: number;

	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	team_name: string;

	@Field({ nullable: true })
	matches_won: number;

	@Field({ nullable: true })
	matches_lost: number;

	@Field({ nullable: true })
	manual_club_name: string;

	@Field({ nullable: true })
	sets_won: number;

	@Field({ nullable: true })
	sets_lost: number;

	@Field({ nullable: true })
	organization_code: string;

	@Field({ nullable: true })
	points_won: number;

	@Field({ nullable: true })
	points_lost: number;

	@Field({ nullable: true })
	roster_club_id: number;

	@Field({ nullable: true })
	club_name: string;

	@Field({ nullable: true })
	state: string;

	@Field(() => [TeamSingleAthlete], { nullable: true })
	athletes: TeamSingleAthlete[];

	@Field(() => [TeamSingleStaff], { nullable: true })
	staff: TeamSingleStaff[];

	@Field(() => [TeamSingleResults], { nullable: true })
	results: TeamSingleResults[];

	@Field(() => [EventUpcoming], { nullable: true })
	upcoming: EventUpcoming[];

	@Field(() => [TeamSinglePbInfo], { nullable: true })
	pb_info: TeamSinglePbInfo[];

	@Field(() => MatchFinishes, { nullable: true })
	bracket_finishes: MatchFinishes;
}
