import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class UpcomingMatches {
	@Field({ nullable: true })
	division_id: number;
	@Field({ nullable: true })
	division_name: string;
	@Field({ nullable: true })
	club_id: number;
	@Field({ nullable: true })
	club_name: string;
	@Field({ nullable: true })
	club_state: string;
	@Field({ nullable: true })
	club_code: string;
	@Field({ nullable: true })
	team_code: string;
	@Field({ nullable: true })
	team_id: number;
	@Field({ nullable: true })
	team_name: string;
	@Field({ nullable: true })
	opponent_id: number;
	@Field({ nullable: true })
	opponent_name: string;
	@Field({ nullable: true })
	secs_start: number;
	@Field({ nullable: true })
	secs_finished: number;
	@Field({ nullable: true })
	court_name: string;
	@Field({ nullable: true })
	matches_won: number;
	@Field({ nullable: true })
	matches_lost: number;
	@Field({ nullable: true })
	sets_won: number;
	@Field({ nullable: true })
	sets_lost: number;
	@Field({ nullable: true })
	sets_pct: number;
	@Field({ nullable: true })
	seed: number;
}
