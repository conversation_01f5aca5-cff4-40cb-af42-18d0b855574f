import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class StandingInfo {
	@Field({ nullable: true })
	points: number;
}

@ObjectType()
class TeamDetails {
	@Field({ nullable: true })
	roster_team_id: number;

	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	seed_current: number;

	@Field({ nullable: true })
	team_name: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	organization_code: string;

	@Field({ nullable: true })
	points_ratio: number;

	@Field({ nullable: true })
	matches_won: number;

	@Field({ nullable: true })
	matches_lost: number;

	@Field({ nullable: true })
	sets_won: number;

	@Field({ nullable: true })
	sets_lost: number;

	@Field({ nullable: true })
	sets_pct: number;

	@Field({ nullable: true })
	rank: number;

	@Field(() => StandingInfo, { nullable: true })
	info: StandingInfo;
}

@ObjectType()
export class Standing {
	@Field(() => [TeamDetails])
	teams: TeamDetails[];

	@Field()
	heading_group: string;
}

@ObjectType()
export class EventDivisionDetails {
	@Field(() => [Standing])
	standing: Standing[];
}
