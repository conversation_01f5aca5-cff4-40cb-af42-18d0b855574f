import { Field, ID, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class EventDivision {
	@Field(() => ID, { nullable: true })
	event_id: string;

	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	short_name: string;

	@Field({ nullable: true })
	teams_count: number;

	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	gender: 'male' | 'female';
}
