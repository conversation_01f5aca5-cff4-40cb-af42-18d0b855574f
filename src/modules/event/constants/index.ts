import { SchemaDefinition } from 'redis-om';

import { EventSearchIndexEntry } from '../types';

export const MIN_SEARCH_LENGTH = 1;
export const EVENTS_CACHE_TTL = 60 * 60 * 1000; // 1 hour

export const EVENTS_INDEX_SCHEMA: SchemaDefinition<EventSearchIndexEntry> = {
	event_id: { type: 'string', indexed: false },
	date_start: { type: 'number', sortable: true },
	date_end: { type: 'number', sortable: true },
	state: { type: 'string' },
	day: { type: 'string' },
	year: { type: 'string' },
	search_content: { type: 'text' },
	sort_order: { type: 'number', sortable: true },
};
