import { BadRequestException, Injectable } from '@nestjs/common';

import { CacheService } from '@/infra/cache/cache.service';
import { ExtendedCacheService, ItemCacheKeyConfig, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheCategories, CacheCommonKeys, CacheScopes } from '@/shared/constants/cache';
import { TeamAdvancement } from '@/shared/graphql/entities/team-advancement.entity';
import { keyBy } from '@/shared/utils/collection';
import { convertDateToTimeStamp } from '@/shared/utils/format';
import { parseWithFallback } from '@/shared/utils/json';
import { completeObject } from '@/shared/utils/object';
import { createPageInfo } from '@/shared/utils/pagination';

import { EVENTS_CACHE_TTL } from './constants';
import { Event, PaginatedEvents, NextPrevPool, EventPool, TeamSingle } from './entities';
import { EventSearchService, FindPaginatedEventsIdsParams } from './event-search.service';
import { EventRepository, FetchEventsParams, FetchPaginatedEventsParams as PaginatedEventsParams } from './event.repository';

export type FetchPaginatedEventsParams = PaginatedEventsParams;

type MatchSourceEntry = {
	name?: string;
	seed?: number;
	type?: number;
};

@Injectable()
export class EventService {
	constructor(
		private eventRepository: EventRepository,
		private cacheService: CacheService,
		private extendedCacheService: ExtendedCacheService,
		private eventSearchService: EventSearchService,
	) {}

	private addSiblingsToPool(pool: EventPool, siblings: NextPrevPool[]) {
		const createSibling = (sibling: NextPrevPool) => {
			const { uuid, name, is_pool } = sibling;
			return { uuid, name, is_pool };
		};

		const processSiblings = (pool: EventPool, siblings: NextPrevPool[]) => {
			const updatedPool = { ...pool };
			siblings.forEach((sibling, index, array) => {
				if (sibling.uuid === updatedPool.uuid) {
					if (index > 0) {
						updatedPool['prev'] = createSibling(array[index - 1]);
					}
					if (index < array.length - 1) {
						updatedPool['next'] = createSibling(array[index + 1]);
					}
				}
			});
			return updatedPool;
		};

		const parseStandings = (pool: EventPool) => {
			const updatedPool = { ...pool };
			updatedPool.standings = updatedPool.standings.map((data) => {
				const pb_stats = data.pb_stats?.toString();
				return {
					...data,
					pb_stats: pb_stats ? Object.values(JSON.parse(pb_stats)) : [],
				};
			});
			return updatedPool;
		};

		const formatTeamAdvancement = (teamAdvancement?: TeamAdvancement) => {
			if (!teamAdvancement) return;
			const { next_match, next_ref } = teamAdvancement;
			teamAdvancement.next_match = next_match && Object.keys(next_match).length ? next_match : undefined;
			teamAdvancement.next_ref = next_ref && Object.keys(next_ref).length ? next_ref : undefined;
			return teamAdvancement;
		};

		const parseFinishData = (pool: EventPool) => {
			const updatedPool = { ...pool };
			const finishes = updatedPool.pb_finishes && Object.values(JSON.parse(updatedPool.pb_finishes.toString()));
			const team_ids = (finishes && (finishes.pop() as string)?.split(' ')) || [];
			updatedPool.pb_finishes = {
				team_ids,
				teams: (finishes || []).map(formatTeamAdvancement),
			};
			return updatedPool;
		};

		const parseUpcomingMatches = (pool: EventPool) => {
			const updatedPool = { ...pool };
			const { pb_seeds: pbSeedsJson } = pool as unknown as Record<string, string>;
			const pbSeeds = parseWithFallback<Record<string, string>>(pbSeedsJson, {});

			updatedPool.upcoming_matches?.forEach((match) => {
				const { team1_name, team2_name, ref_team_name } = match;
				if (team1_name && team2_name && ref_team_name) return;
				const { source: sourceJson } = match as unknown as Record<string, string>;
				if (!sourceJson) return;
				// Trying to resolve the display names from the source and pb_seeds
				const source = parseWithFallback<Record<string, MatchSourceEntry>>(sourceJson, {});

				// Convert the pb_seeds object into a map of seed => seed_data (JSON string)
				const pbSeedData = new Map<number, string>();
				Object.entries(pbSeeds).forEach(([key, value]) => pbSeedData.set(parseInt(key, 10), value));

				if (!team1_name) {
					const { type, seed, name } = source.team1 || {};
					const pbName = type === 5 && seed ? parseWithFallback<Record<string, string>>(pbSeedData.get(seed), {}).name : '';
					match.team1_name = pbName || name || '';
				}
				if (!team2_name) {
					const { type, seed, name } = source.team2 || {};
					const pbName = type === 5 && seed ? parseWithFallback<Record<string, string>>(pbSeedData.get(seed), {}).name : '';
					match.team2_name = pbName || name || '';
				}
				if (!ref_team_name) {
					const { type, seed, name } = source.ref || {};
					const pbName = type === 5 && seed ? parseWithFallback<Record<string, string>>(pbSeedData.get(seed), {}).name : '';
					match.ref_team_name = pbName || name || '';
				}
			});
			return updatedPool;
		};

		const getUpdatedPool = (pool: EventPool, siblings: NextPrevPool[]) => {
			pool = processSiblings(pool, siblings);
			pool = parseStandings(pool);
			pool = parseFinishData(pool);
			pool = parseUpcomingMatches(pool);

			return pool;
		};
		return getUpdatedPool(pool, siblings);
	}

	private parseTeamResults(team: TeamSingle) {
		const formatTeamAdvancement = (teamAdvancement?: TeamAdvancement) => {
			if (!teamAdvancement) return;
			const { next_match, next_ref } = teamAdvancement;
			teamAdvancement.next_match = next_match && Object.keys(next_match).length ? next_match : undefined;
			teamAdvancement.next_ref = next_ref && Object.keys(next_ref).length ? next_ref : undefined;
			return teamAdvancement;
		};

		const formatBracketFinishes = (team: TeamSingle) => {
			const { bracket_finishes } = team;
			if (!bracket_finishes) return;
			const { Winner, Loser } = parseWithFallback<Record<string, TeamAdvancement>>(bracket_finishes as unknown as string, {});
			if (!Winner && !Loser) return;
			team.bracket_finishes = {
				winner: formatTeamAdvancement(Winner),
				looser: formatTeamAdvancement(Loser),
			};
		};

		const updatedTeam = { ...team };
		updatedTeam.results = updatedTeam.results?.map((data) => {
			const pbStatsString = data.pb_stats?.toString();
			return {
				...data,
				pb_stats: pbStatsString ? Object.values(JSON.parse(pbStatsString)) : null,
				matches: JSON.parse(data.matches.toString()),
			};
		});
		updatedTeam.pb_info = updatedTeam.pb_info?.map((pool) => {
			const updatedPool = { ...pool };
			const finishes = updatedPool.pb_finishes && Object.values(JSON.parse(updatedPool.pb_finishes.toString()));
			const team_ids = (finishes && (finishes.pop() as string)?.split(' ')) || [];
			updatedPool.pb_finishes = {
				team_ids,
				teams: (finishes || []).map(formatTeamAdvancement),
			};
			return updatedPool;
		});

		formatBracketFinishes(updatedTeam);
		return updatedTeam;
	}

	private getPaginatedEventsCacheKeyConfig(params: PaginatedEventsParams): ItemCacheKeyConfig | null {
		if (params.search?.trim() || params.years?.length) return null; // Do not cache search results

		const { page, pageSize, startBefore, startAfter, endBefore, endAfter, asc } = params;
		const keyParts: (string | number)[] = [page, pageSize, asc ? 'a' : 'd'];
		if (startBefore) keyParts.push(`sb-${startBefore}`);
		if (startAfter) keyParts.push(`sa-${startAfter}`);
		if (endBefore) keyParts.push(`eb-${endBefore}`);
		if (endAfter) keyParts.push(`ea-${endAfter}`);

		return {
			scope: CacheScopes.Event,
			scopeKey: CacheCommonKeys.All,
			category: CacheCategories.Page,
			categoryKey: keyParts.join('-'),
			ttl: EVENTS_CACHE_TTL,
		};
	}

	private getEventsCacheKeyConfig(): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: CacheCommonKeys.All,
			category: CacheCategories.Event,
			ttl: EVENTS_CACHE_TTL,
		};
	}

	private async getPaginatedEventsData(params: PaginatedEventsParams): Promise<PaginatedEvents> {
		const { items, itemCount } = await this.eventRepository.fetchPaginatedEvents(params);
		return {
			items: convertDateToTimeStamp(items, ['date_start']),
			page_info: createPageInfo(params, itemCount),
		};
	}

	private async getEventsData(params: FetchEventsParams): Promise<Record<string, Event | null>> {
		const events = await this.eventRepository.fetchEvents(params);
		const eventsMap = keyBy(events, 'id') as Record<string, Event>;
		return completeObject(params.eventsIds, eventsMap, null);
	}

	private async findPaginatedEvents(params: FindPaginatedEventsIdsParams): Promise<PaginatedEvents> {
		const { items: eventsIds, itemCount } = await this.eventSearchService.findPaginatedEventsIds(params);
		const events = await this.extendedCacheService.getOrFetchMany(this.getEventsCacheKeyConfig(), eventsIds, (missingIds) =>
			this.getEventsData({ eventsIds: missingIds }),
		);
		return {
			items: eventsIds.map((id) => events[id]).filter(Boolean),
			page_info: createPageInfo(params, itemCount),
		};
	}

	async fetchPaginatedEvents(params: FetchPaginatedEventsParams): Promise<PaginatedEvents> {
		if (this.eventSearchService.canSearchWith(params)) {
			return this.findPaginatedEvents(params);
		} else {
			return this.extendedCacheService.getOrFetch<PaginatedEvents>(this.getPaginatedEventsCacheKeyConfig(params), () =>
				this.getPaginatedEventsData(params),
			);
		}
	}

	async fetchEvent(eventId: string | number, isESWID: boolean) {
		const cacheResponse = await this.cacheService.getCache('fetchEvent', { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}

		const response = await this.eventRepository.fetchEvent(eventId, isESWID);
		if (response) {
			this.cacheService.setCache(response, 'fetchEvent', { eventId });
		}
		return response;
	}
	async fetchDivisionsForEvent(eventId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchDivisionsForEvent', { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchDivisionsForEvent(eventId);
		this.cacheService.setCache(response, 'fetchDivisionsForEvent', { eventId });
		return response;
	}
	async fetchClubsForEvent(eventId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchClubsForEvent', { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchClubsForEvent(eventId);
		this.cacheService.setCache(response, 'fetchClubsForEvent', { eventId });
		return response;
	}
	async fetchAthletesForEvent(eventId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchAthletesForEvent', { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchAthletesForEvent(eventId);
		this.cacheService.setCache(response, 'fetchAthletesForEvent', { eventId });
		return response;
	}
	async fetchTeamsForEvent(eventId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchTeamsForEvent', { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchTeamsForEvent(eventId);
		this.cacheService.setCache(response, 'fetchTeamsForEvent', { eventId });
		return response;
	}
	async fetchDivisionDetails(eventId: string, divisionIds: string[]) {
		const cacheResponse = await this.cacheService.getCache('fetchDivisionDetails', { eventId, divisionIds });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchDivisionDetails(eventId, divisionIds);
		this.cacheService.setCache(response, 'fetchDivisionDetails', { eventId, divisionIds });
		return response;
	}
	async fetchPoolsForEventDivision(eventId: string, divisionId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchPoolsForEventDivision', { eventId, divisionId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchPoolsForEventDivision(eventId, divisionId);
		this.cacheService.setCache(response, 'fetchPoolsForEventDivision', { eventId, divisionId });
		return response;
	}
	async fetchPoolForEvent(eventId: string, poolId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchPoolForEvent', { eventId, poolId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const pool = await this.eventRepository.fetchPoolForEvent(eventId, poolId);
		const siblings = await this.eventRepository.fetchSiblingsOfPoolForEvent(pool);

		const response = this.addSiblingsToPool(pool, siblings);
		await this.cacheService.setCache(response, 'fetchPoolForEvent', { eventId, poolId });
		return response;
	}
	async fetchSingleTeamForEvent(eventId: string, teamId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchSingleTeamForEvent', { eventId, teamId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const team = await this.eventRepository.fetchSingleTeamForEvent(eventId, teamId);
		const response = this.parseTeamResults(team);
		await this.cacheService.setCache(response, 'fetchSingleTeamForEvent', { eventId, teamId });
		return response;
	}
	async fetchFeatureMatchesForEvent(eventId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchFeatureMatchesForEvent', { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchFeatureMatchesForEvent(eventId);
		this.cacheService.setCache(response, 'fetchFeatureMatchesForEvent', { eventId });
		return response;
	}

	async fetchPoolIdByTeamId(id: string, teamId: number, poolId?: string) {
		try {
			if (poolId) {
				return await this.fetchPoolForEvent(id, poolId);
			}

			const cacheResponse = await this.cacheService.getCache('fetchFirstPoolByTeamId', { eventId: id, teamId });
			if (cacheResponse) {
				return cacheResponse;
			}

			const firstPool = await this.eventRepository.fetchFirstPoolIdByTeamId(teamId);
			if (firstPool) {
				const response = await this.fetchPoolForEvent(id, firstPool.pool_bracket_id);
				await this.cacheService.setCache(response, 'fetchFirstPoolByTeamId', { eventId: id, teamId });
				return response;
			}
		} catch (error) {
			throw new BadRequestException(error.message);
		}
		return null;
	}

	async fetchQualifiedTeamsForEvent(eventId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchQualifiedTeamsForEvent', { eventId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const response = await this.eventRepository.fetchQualifiedTeamsForEvent(eventId);
		this.cacheService.setCache(response, 'fetchQualifiedTeamsForEvent', { eventId });
		return response;
	}

	async fetchUpcomingMatchesForEvent(eventId: string) {
		const cacheResponse = await this.cacheService.getCache('fetchUpcomingMatchesForEvent', { eventId });
		if (cacheResponse && typeof cacheResponse === 'string') {
			return JSON.parse(cacheResponse);
		}
		const data = await this.eventRepository.fetchUpcomingMatchesForEvent(eventId);
		const response = convertDateToTimeStamp(data, ['secs_start', 'secs_finished']);

		this.cacheService.setCache(JSON.stringify(response), 'fetchUpcomingMatchesForEvent', { eventId });
		return response;
	}
}
