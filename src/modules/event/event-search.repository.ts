import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToNumber, castFieldsToString, NumberCastKeys, StringCastKeys } from '@/shared/utils/format';

import { getEventsSearchIndexEntriesQuery } from './queries/events-search-index-entries.query';
import { EventSearchIndexEntry } from './types';

const EVENT_SEARCH_NUMBER_CAST_FIELDS: NumberCastKeys<EventSearchIndexEntry>[] = ['sort_order', 'date_start', 'date_end'];
const EVENT_SEARCH_STRING_CAST_FIELDS: StringCastKeys<EventSearchIndexEntry>[] = ['event_id'];

@Injectable()
export class EventSearchRepository {
	constructor(private prisma: PrismaService) {}

	async fetchEventsSearchIndexEntries(): Promise<EventSearchIndexEntry[]> {
		const [query, values] = getEventsSearchIndexEntriesQuery();
		const items = await this.prisma.$queryRawUnsafe<EventSearchIndexEntry[]>(query, ...values);
		castFieldsToNumber(items, EVENT_SEARCH_NUMBER_CAST_FIELDS);
		castFieldsToString(items, EVENT_SEARCH_STRING_CAST_FIELDS);
		return items;
	}
}
