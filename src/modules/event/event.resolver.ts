import { Query, Resolver, <PERSON>rg<PERSON>, ResolveField, Parent, Context } from '@nestjs/graphql';
import { ID } from '@nestjs/graphql';

import { EventId } from '@/infra/decorators/eventId.decorator';
import { BracketService } from '@/modules/bracket/bracket.service';
import { EventBracket } from '@/modules/bracket/entities';
import { Club } from '@/modules/club/entities';
import { CourtGridService } from '@/modules/courtGrid/courtGrid.service';
import { CourtGrid } from '@/modules/courtGrid/entities';

import {
	PaginatedEventsInputDto,
	EventByIdInputDto,
	DivisionPoolsInputDto,
	PoolByIdInputDto,
	TeamSingleInputDto,
	PoolByTeamInputDto,
	BracketByIdInputDto,
} from './dto';
import {
	Event,
	PaginatedEvents,
	EventDivision,
	EventAthlete,
	TeamList,
	TeamSingle,
	EventPool,
	EventDivisionDetails,
	DivisionPool,
	FeatureMatches,
	UpcomingMatches,
} from './entities';
import { EventService } from './event.service';

@Resolver(() => Event)
export class EventResolver {
	constructor(
		private readonly eventService: EventService,
		private readonly courtGridService: CourtGridService,
		private readonly bracketService: BracketService,
	) {}

	@Query(() => PaginatedEvents)
	async paginatedEvents(@Args() args: PaginatedEventsInputDto) {
		return this.eventService.fetchPaginatedEvents(args);
	}

	@Query(() => Event, { nullable: true })
	event(@Args() { id }: EventByIdInputDto, @EventId() { eventID, isESWID }: { eventID: string | number; isESWID: boolean }) {
		void id;
		return this.eventService.fetchEvent(eventID, isESWID);
	}

	@ResolveField(() => [EventDivision], { nullable: true })
	divisions(@Parent() event: Event, @Context() context) {
		const eventId = context.req.body.variables.id;
		return this.eventService.fetchDivisionsForEvent(eventId);
	}

	@ResolveField(() => [Club], { nullable: true })
	clubs(@Parent() event: Event, @Context() context) {
		const eventId = context.req.body.variables.id;
		return this.eventService.fetchClubsForEvent(eventId);
	}
	@ResolveField(() => [EventAthlete], { nullable: true })
	athletes(@Parent() event: Event, @Context() context) {
		const eventId = context.req.body.variables.id;
		return this.eventService.fetchAthletesForEvent(eventId);
	}
	@ResolveField(() => [TeamList], { nullable: true })
	teams(@Parent() event: Event, @Context() context) {
		const eventId = context.req.body.variables.id;
		return this.eventService.fetchTeamsForEvent(eventId);
	}

	@Query(() => EventDivisionDetails, { nullable: true })
	divisionDetails(@Args('id', { type: () => ID }) id: string, @Args('divisionIds', { type: () => [ID] }) divisionIds: string[]) {
		return { standing: this.eventService.fetchDivisionDetails(id, divisionIds) };
	}

	@Query(() => [DivisionPool])
	pools(@Args() { id, divisionId }: DivisionPoolsInputDto) {
		return this.eventService.fetchPoolsForEventDivision(id, divisionId);
	}

	@Query(() => EventPool)
	pool(@Args() { id, poolId }: PoolByIdInputDto) {
		return this.eventService.fetchPoolForEvent(id, poolId);
	}

	@Query(() => TeamSingle)
	teamSingle(@Args() { id, teamId }: TeamSingleInputDto) {
		return this.eventService.fetchSingleTeamForEvent(id, teamId);
	}

	@Query(() => CourtGrid)
	courtMatches(
		@Args('id', { type: () => ID }) id: string,
		@Args('day', { type: () => String }) day: string,
		@Args('hour', { type: () => String }) hour: string,
		@Args('hours', { type: () => String }) hours: string,
		@Args('division', { type: () => String }) division: string,
	) {
		return this.courtGridService.fetchCourtGrid({ eventId: id, day, hour, hours, division });
	}

	@Query(() => [FeatureMatches])
	featureMatches(@Args('id', { type: () => ID }) id: string) {
		return this.eventService.fetchFeatureMatchesForEvent(id);
	}

	@Query(() => EventPool, { nullable: true })
	poolIdByTeamId(@Args() { id, teamId, poolId }: PoolByTeamInputDto) {
		return this.eventService.fetchPoolIdByTeamId(id, teamId, poolId);
	}

	@Query(() => [UpcomingMatches], { nullable: true })
	upcomingMatches(@Args('id', { type: () => ID }) id: string) {
		return this.eventService.fetchUpcomingMatchesForEvent(id);
	}
	//*Bracket
	@Query(() => EventBracket, { nullable: true })
	bracket(@Args() { id, poolId }: BracketByIdInputDto) {
		void id;
		return this.bracketService.fetchBracket({ poolId });
	}
}
