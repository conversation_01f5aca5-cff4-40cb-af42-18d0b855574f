import { Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { EventMediaRepository } from './event-media.repository';
import { EventMediaService } from './event-media.service';

@Module({
	imports: [PrismaModule, CacheModule],
	providers: [EventMediaService, EventMediaRepository],
	exports: [EventMediaService],
})
export class EventMediaModule {}
