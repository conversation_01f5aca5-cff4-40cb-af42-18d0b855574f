import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type DivisionsEventMediaQueryParams = {
	eventId: string;
	divisionsIds: string[];
};

export const getDivisionsEventMediaQuery = ({ eventId, divisionsIds }: DivisionsEventMediaQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const divisionsIdsValues = divisionsIds.map((divisionId) => sqlVars.useValue(Number(divisionId)));

	const query = `
		SELECT 
			em.event_id,
			em.event_media_id as media_id,
			em.division_id,
			em.file_type,
			concat(em.file_path, '.', em.file_ext) as path
		FROM 
			event_media em
		WHERE
			em.event_id = ${sqlVars.useValue(Number(eventId))} 
			AND em.division_id IN (${divisionsIdsValues.join(',')})
	`;

	return [query, sqlVars.getValues()];
};
