import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { EventMedia } from '@/modules/eventMedia/entities';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';

import { getDivisionsEventMediaQuery, DivisionsEventMediaQueryParams } from './queries/divisions-event-media.query';

export type FetchDivisionsEventMediaParams = DivisionsEventMediaQueryParams;

const EVENT_MEDIA_STRING_CAST_FIELDS: StringCastKeys<EventMedia>[] = ['event_id', 'media_id', 'division_id'];

@Injectable()
export class EventMediaRepository {
	constructor(private prisma: PrismaService) {}

	async fetchDivisionsEventMedia(params: FetchDivisionsEventMediaParams): Promise<EventMedia[]> {
		const [query, values] = getDivisionsEventMediaQuery(params);
		const items = await this.prisma.$queryRawUnsafe<EventMedia[]>(query, ...values);
		return this._formatEventMediaFields(items);
	}

	private _formatEventMediaFields(items: EventMedia[]): EventMedia[] {
		return castFieldsToString(items, EVENT_MEDIA_STRING_CAST_FIELDS);
	}
}
