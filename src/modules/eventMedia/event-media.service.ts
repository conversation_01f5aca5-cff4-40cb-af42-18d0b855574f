import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { EventMedia } from '@/modules/eventMedia/entities';
import { CacheCategories, CacheScopes } from '@/shared/constants/cache';
import { groupBy } from '@/shared/utils/collection';
import { completeObject } from '@/shared/utils/object';

import { EventMediaRepository, FetchDivisionsEventMediaParams } from './event-media.repository';

@Injectable()
export class EventMediaService {
	constructor(private eventMediaRepository: EventMediaRepository, private cacheService: ExtendedCacheService) {}

	fetchDivisionsEventMediaMap(params: FetchDivisionsEventMediaParams): Promise<Record<string, EventMedia[]>> {
		const cacheKeyConfig = this._getDivisionsEventMediaCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<EventMedia[]>(cacheKeyConfig, params.divisionsIds, (missingIds) =>
			this._getDivisionsEventMediaData({ ...params, divisionsIds: missingIds }),
		);
	}

	private async _getDivisionsEventMediaData(params: FetchDivisionsEventMediaParams): Promise<Record<string, EventMedia[]>> {
		const divisionsEventMedia = await this.eventMediaRepository.fetchDivisionsEventMedia(params);
		const eventMediaMap = groupBy(divisionsEventMedia, 'division_id');
		return completeObject(params.divisionsIds, eventMediaMap, []);
	}

	private _getDivisionsEventMediaCacheKeyConfig(params: FetchDivisionsEventMediaParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.DivisionMedia,
		};
	}
}
