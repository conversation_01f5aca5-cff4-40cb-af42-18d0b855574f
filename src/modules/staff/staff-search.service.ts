import { Injectable, OnModuleInit } from '@nestjs/common';

import configuration from '@/infra/configuration';
import { EventHelperService } from '@/infra/eventHelper/eventHelper.service';
import { EventKey } from '@/shared/utils/event';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { MIN_SEARCH_LENGTH } from './constants';
import { StaffSearchRepository } from './staff-search.repository';
import { StaffSearchIndexEntry } from './types';
import { InjectSearchManager } from '../search/decorators';
import { SearchEntityName } from '../search/types';
import { RedisSearchManager, Search } from '../search/utils/redis-search-manager';

export type FindPaginatedStaffIdsParams = {
	eventKey: EventKey;
	search: string;
} & PageParams;

const SEARCH_SERVICE_DISABLED = !configuration.USE_SEARCH_SERVICES;

@Injectable()
export class StaffSearchService implements OnModuleInit {
	constructor(
		@InjectSearchManager(SearchEntityName.Staff) private readonly searchManager: RedisSearchManager<StaffSearchIndexEntry>,
		private staffSearchRepository: StaffSearchRepository,
		private eventHelperService: EventHelperService,
	) {}

	async onModuleInit() {
		if (SEARCH_SERVICE_DISABLED) return;
		return this.searchManager.createOrUpdateIndex();
	}

	canSearchWith<T extends { search?: string }>(params: T): params is T & { search: string } {
		if (SEARCH_SERVICE_DISABLED) return false;
		return params.search?.trim().length >= MIN_SEARCH_LENGTH;
	}

	async findPaginatedStaffIds(params: FindPaginatedStaffIdsParams): Promise<PageResults<string>> {
		const { eventKey } = params;
		await this.actualizeIndexEntries(eventKey);

		const searchContent = this.searchManager.sanitizeSearchText(params.search);
		if (searchContent.length < MIN_SEARCH_LENGTH) {
			return { items: [], itemCount: 0 };
		}

		let queryBuilder = this.searchManager.find();
		queryBuilder = this._withEventKey(queryBuilder, eventKey);
		queryBuilder = this._withTextSearch(queryBuilder, searchContent);

		const { offset, limit } = toOffsetLimit(params);
		const [items, itemCount] = await Promise.all([
			queryBuilder.sortBy('sort_order').return.page(offset, limit),
			queryBuilder.count(), // TODO Get the count from the search result
		]);
		return { items: items.map((item) => item.staff_id), itemCount };
	}

	actualizeIndexEntries(eventKey: EventKey): Promise<void> {
		return this.searchManager.actualizeIndexEntries(eventKey.value, async () => {
			const [entries, { event_id, esw_id }] = await Promise.all([
				this.staffSearchRepository.fetchStaffSearchIndexEntries(eventKey),
				this.eventHelperService.getEventIdentifiers(eventKey),
			]);
			return [entries, [event_id, esw_id]];
		});
	}

	clearIndexEntries(eventKey: EventKey): Promise<void> {
		return this.searchManager.clearIndexEntries(eventKey.value);
	}

	private _withEventKey(queryBuilder: Search<StaffSearchIndexEntry>, eventKey: EventKey): Search<StaffSearchIndexEntry> {
		return queryBuilder.where(eventKey.isESWID ? 'esw_id' : 'event_id').eq(eventKey.value);
	}

	private _withTextSearch(queryBuilder: Search<StaffSearchIndexEntry>, searchContent: string): Search<StaffSearchIndexEntry> {
		return searchContent.length === 2
			? // If the search query is a state code, we should search by the state field
			  queryBuilder.and('state').eq(searchContent)
			: queryBuilder.and('search_content').matches(`*${searchContent}*`);
	}
}
