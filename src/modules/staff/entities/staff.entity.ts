import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class Staff {
	@Field(() => ID)
	staff_id: string;

	@Field()
	first: string;

	@Field()
	last: string;

	@Field()
	club_name: string;

	@Field(() => ID)
	team_id: string;

	@Field()
	team_name: string;

	@Field({ nullable: true })
	role_name: string;

	@Field({ nullable: true })
	organization_code: string;

	@Field({ nullable: true })
	state: string;
}
