import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { Staff } from './entities';
import { getPaginatedStaffQuery, PaginatedStaffQueryParams } from './queries/paginated-staff.query';
import { getStaffQuery, StaffQueryParams } from './queries/staff.query';

export type FetchStaffParams = StaffQueryParams;
export type FetchPaginatedStaffParams = Omit<PaginatedStaffQueryParams, 'offset' | 'limit'> & PageParams;

const STAFF_STRING_CAST_FIELDS: StringCastKeys<Staff>[] = ['staff_id', 'team_id'];

@Injectable()
export class StaffRepository {
	constructor(private prisma: PrismaService) {}

	async fetchPaginatedStaff(params: FetchPaginatedStaffParams): Promise<PageResults<Staff>> {
		const [query, values] = getPaginatedStaffQuery({ ...params, ...toOffsetLimit(params) });

		const items = await this.prisma.$queryRawUnsafe<Staff[]>(query, ...values);
		if (!items.length) {
			return { items, itemCount: 0 };
		}
		// Get the item count from the first row as it is the same for all rows
		return {
			items: this._formatStaffFields(items),
			itemCount: Number((items[0] as unknown as Record<string, string>).item_count),
		};
	}

	async fetchStaff(params: FetchStaffParams): Promise<Staff[]> {
		const [query, values] = getStaffQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Staff[]>(query, ...values);
		return this._formatStaffFields(items);
	}

	private _formatStaffFields(staff: Staff[]): Staff[] {
		return castFieldsToString(staff, STAFF_STRING_CAST_FIELDS);
	}
}
