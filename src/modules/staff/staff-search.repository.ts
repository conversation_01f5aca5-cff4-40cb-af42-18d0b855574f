import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { EventKey } from '@/shared/utils/event';
import { castFieldsToNumber, castFieldsToString, NumberCastKeys, StringCastKeys } from '@/shared/utils/format';

import { getStaffSearchIndexEntriesQuery } from './queries/staff-search-index-entries.query';
import { StaffSearchIndexEntry } from './types';

const STAFF_SEARCH_NUMBER_CAST_FIELDS: NumberCastKeys<StaffSearchIndexEntry>[] = ['sort_order'];
const STAFF_SEARCH_STRING_CAST_FIELDS: StringCastKeys<StaffSearchIndexEntry>[] = ['event_id', 'staff_id'];

@Injectable()
export class StaffSearchRepository {
	constructor(private prisma: PrismaService) {}

	async fetchStaffSearchIndexEntries(eventKey: EventKey): Promise<StaffSearchIndexEntry[]> {
		const [query, values] = getStaffSearchIndexEntriesQuery({ eventKey });
		const items = await this.prisma.$queryRawUnsafe<StaffSearchIndexEntry[]>(query, ...values);
		castFieldsToNumber(items, STAFF_SEARCH_NUMBER_CAST_FIELDS);
		castFieldsToString(items, STAFF_SEARCH_STRING_CAST_FIELDS);
		return items;
	}
}
