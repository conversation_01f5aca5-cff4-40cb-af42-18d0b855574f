import { SchemaDefinition } from 'redis-om';

import { StaffSearchIndexEntry } from '../types';

export const MIN_SEARCH_LENGTH = 2;

export const STAFF_INDEX_SCHEMA: SchemaDefinition<StaffSearchIndexEntry> = {
	esw_id: { type: 'string' },
	event_id: { type: 'string' },
	staff_id: { type: 'string', indexed: false },
	state: { type: 'string' },
	search_content: { type: 'text' },
	sort_order: { type: 'number', sortable: true },
};
