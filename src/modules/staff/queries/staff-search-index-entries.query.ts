import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

type StaffSearchIndexEntriesQueryParams = {
	eventKey: EventKey;
};

export const getStaffSearchIndexEntriesQuery = ({ eventKey }: StaffSearchIndexEntriesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const query = `
		SELECT 
			e.event_id,
			e.esw_id,
			rsr.roster_staff_role_id as staff_id,
			COALESCE(rc.state, '') AS state,
			LOWER(
				ms.first || '|' || ms.last || '|' || 
				rc.club_name || '|' || rt.team_name || '|' || COALESCE(rt.organization_code, '')
			) as search_content,
			ROW_NUMBER() OVER (PARTITION BY e.event_id ORDER BY 
				COALESCE(r.sort_order, 2147483647),
				ms.last,
				ms.first,
				rt.team_name,
				rsr.roster_staff_role_id
			) AS sort_order
		FROM event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted is NULL
		JOIN roster_team rt 
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		JOIN roster_staff_role rsr
			ON rsr.roster_team_id = rt.roster_team_id
			AND rsr.deleted IS NULL
			AND rsr.deleted_by_user IS NULL
		JOIN master_staff ms
			ON ms.master_staff_id = rsr.master_staff_id
			AND ms.deleted is NULL
		LEFT JOIN master_staff_role msr
			ON msr.master_staff_id = rsr.master_staff_id
			AND msr.master_team_id = rsr.master_team_id
		LEFT JOIN role r
			ON r.role_id = COALESCE(NULLIF(rsr.role_id, '0'), msr.role_id)
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
	`;

	return [query, sqlVars.getValues()];
};
