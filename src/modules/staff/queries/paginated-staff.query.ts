import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type PaginatedStaffQueryParams = {
	eventKey: EventKey;
	offset?: number;
	limit?: number;
	search?: string;
};

export const getPaginatedStaffQuery = ({ eventKey, offset, limit, search }: PaginatedStaffQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	let query = `
		SELECT 
			rsr.roster_staff_role_id as staff_id,
			ms.first,
			ms.last,
			rc.club_name,
			rc.state,
			rt.roster_team_id AS team_id,
			rt.team_name,
			rt.organization_code,
			r.name AS role_name,
			COUNT(*) OVER() AS item_count
		FROM event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted is NULL
		JOIN roster_team rt 
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		JOIN roster_staff_role rsr
			ON rsr.roster_team_id = rt.roster_team_id
			AND rsr.deleted IS NULL
			AND rsr.deleted_by_user IS NULL
		JOIN master_staff ms
			ON ms.master_staff_id = rsr.master_staff_id
			AND ms.deleted is NULL
		LEFT JOIN master_staff_role msr 
			ON msr.master_staff_id = rsr.master_staff_id
			AND msr.master_team_id = rsr.master_team_id
		LEFT JOIN role r
			ON r.role_id = COALESCE(NULLIF(rsr.role_id, '0'), msr.role_id)
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted IS NULL
	`;

	search = search && search.trim();
	if (search) {
		const sValue = sqlVars.useValue(search);
		const wcVal = sqlVars.useValue(`%${search}%`);
		query += `AND (
			CASE
				WHEN LENGTH(${sValue}) = 2 THEN rc.state ILIKE ${wcVal}
				ELSE (
					ms.first ILIKE ${wcVal} OR
					ms.last ILIKE ${wcVal} OR
					(ms.first || ' ' || ms.last) ILIKE ${wcVal} OR
					(ms.last || ' ' || ms.first) ILIKE ${wcVal} OR
					rc.club_name ILIKE ${wcVal} OR
					rt.team_name ILIKE ${wcVal} OR
					rt.organization_code ILIKE ${wcVal}
				)
			END
		) `;
	}

	query += `ORDER BY 
		COALESCE(r.sort_order, 2147483647),
		ms.last,
		ms.first,
		rt.team_name,
		rsr.roster_staff_role_id
	`;

	if (offset) {
		query += `OFFSET ${sqlVars.useValue(offset)} `;
	}

	if (limit) {
		query += `LIMIT ${sqlVars.useValue(limit)} `;
	}

	return [query, sqlVars.getValues()];
};
