import { Args, Query, Resolver } from '@nestjs/graphql';

import { Staff, PaginatedStaff } from '@/modules/staff/entities';
import { EventKey } from '@/shared/utils/event';

import { PaginatedStaffInputDto } from './dto/paginated-staff.dto';
import { StaffService } from './staff.service';

@Resolver(() => Staff)
export class StaffResolver {
	constructor(private readonly staffService: StaffService) {}

	@Query(() => PaginatedStaff)
	paginatedStaff(@Args() args: PaginatedStaffInputDto): Promise<PaginatedStaff> {
		return this.staffService.fetchPaginatedStaff({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}
}
