import { Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { STAFF_INDEX_SCHEMA } from './constants';
import { StaffSearchRepository } from './staff-search.repository';
import { StaffSearchService } from './staff-search.service';
import { StaffRepository } from './staff.repository';
import { StaffResolver } from './staff.resolver';
import { StaffService } from './staff.service';
import { SearchModule } from '../search/search.module';
import { SearchEntityName } from '../search/types';

@Module({
	imports: [PrismaModule, CacheModule, EventHelperModule, SearchModule.forFeature(SearchEntityName.Staff, STAFF_INDEX_SCHEMA)],
	providers: [StaffResolver, StaffService, StaffRepository, StaffSearchRepository, StaffSearchService],
	exports: [StaffService],
})
export class StaffModule {}
