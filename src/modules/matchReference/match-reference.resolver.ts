import { Context, Parent, Resol<PERSON><PERSON>ield, Resolver } from '@nestjs/graphql';

import { MatchReference } from '@/shared/graphql/entities/match-reference.entity';

import { MatchReferenceExternal } from './entities/match-reference-external.entity';
import { MatchReferenceExternalLoader } from './loaders/match-reference-external.loader';

type MatchReferenceResolverContext = {
	matchReferenceExternalLoader: ReturnType<MatchReferenceExternalLoader['create']>;
};

@Resolver(() => MatchReference)
export class MatchReferenceResolver {
	constructor(private matchReferenceExternalLoader: MatchReferenceExternalLoader) {}

	@ResolveField(() => MatchReferenceExternal, { nullable: true })
	async external(
		@Parent() matchReference: MatchReference,
		@Context() context: MatchReferenceResolverContext,
	): Promise<MatchReferenceExternal | null> {
		if (!matchReference.match_id) {
			return null;
		}

		if (!context.matchReferenceExternalLoader) {
			context.matchReferenceExternalLoader = this.matchReferenceExternalLoader.create();
		}
		return context.matchReferenceExternalLoader.load(matchReference);
	}
}
