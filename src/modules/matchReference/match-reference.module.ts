import { Module } from '@nestjs/common';

import { PoolBracketModule } from '@/modules/poolBracket/pool-bracket.module';

import { MatchReferenceExternalLoader } from './loaders/match-reference-external.loader';
import { MatchReferenceResolver } from './match-reference.resolver';

@Module({
	imports: [PoolBracketModule],
	providers: [MatchReferenceResolver, MatchReferenceExternalLoader],
})
export class MatchReferenceModule {}
