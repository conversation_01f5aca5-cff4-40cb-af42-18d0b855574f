import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { PoolBracketService } from '@/modules/poolBracket/pool-bracket.service';
import { MatchReference } from '@/shared/graphql/entities/match-reference.entity';

import { MatchReferenceExternal } from '../entities/match-reference-external.entity';

@Injectable()
export class MatchReferenceExternalLoader {
	constructor(private poolBracketService: PoolBracketService) {}

	create(): DataLoader<MatchReference, MatchReferenceExternal> {
		return new DataLoader<MatchReference, MatchReferenceExternal>(
			async (matchReferences: MatchReference[]) => {
				const matchesIds = matchReferences.map((matchReference) => matchReference.match_id);
				const matchesPoolBracketsShortInfoMap = await this.poolBracketService.fetchMatchesPoolBracketsShortInfoMap({ matchesIds });
				return matchesIds.map((matchId: string) => ({
					pool_bracket_info: matchesPoolBracketsShortInfoMap[matchId],
				}));
			},
			{ cache: false, name: 'MatchReferenceExternalLoader' },
		);
	}
}
