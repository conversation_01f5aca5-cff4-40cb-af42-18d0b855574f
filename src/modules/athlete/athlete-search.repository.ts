import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { EventKey } from '@/shared/utils/event';
import { castFieldsToNumber, castFieldsToString, NumberCastKeys, StringCastKeys } from '@/shared/utils/format';

import { getAthletesSearchIndexEntriesQuery } from './queries/athletes-search-index-entries.query';
import { AthleteSearchIndexEntry } from './types';

const ATHLETE_SEARCH_NUMBER_CAST_FIELDS: NumberCastKeys<AthleteSearchIndexEntry>[] = ['sort_order'];
const ATHLETE_SEARCH_STRING_CAST_FIELDS: StringCastKeys<AthleteSearchIndexEntry>[] = ['event_id', 'athlete_id'];

@Injectable()
export class AthleteSearchRepository {
	constructor(private prisma: PrismaService) {}

	async fetchAthletesSearchIndexEntries(eventKey: EventKey): Promise<AthleteSearchIndexEntry[]> {
		const [query, values] = getAthletesSearchIndexEntriesQuery({ eventKey });
		const items = await this.prisma.$queryRawUnsafe<AthleteSearchIndexEntry[]>(query, ...values);
		castFieldsToNumber(items, ATHLETE_SEARCH_NUMBER_CAST_FIELDS);
		castFieldsToString(items, ATHLETE_SEARCH_STRING_CAST_FIELDS);
		return items;
	}
}
