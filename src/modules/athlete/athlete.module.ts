import { Modu<PERSON> } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { AthleteSearchRepository } from './athlete-search.repository';
import { AthleteSearchService } from './athlete-search.service';
import { AthleteRepository } from './athlete.repository';
import { AthleteResolver } from './athlete.resolver';
import { AthleteService } from './athlete.service';
import { ATHLETES_INDEX_SCHEMA } from './constants';
import { SearchModule } from '../search/search.module';
import { SearchEntityName } from '../search/types';

@Module({
	imports: [PrismaModule, CacheModule, EventHelperModule, SearchModule.forFeature(SearchEntityName.Athletes, ATHLETES_INDEX_SCHEMA)],
	providers: [AthleteResolver, AthleteService, AthleteRepository, AthleteSearchService, AthleteSearchRepository],
	exports: [AthleteService],
})
export class AthleteModule {}
