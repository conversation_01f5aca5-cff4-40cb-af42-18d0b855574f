import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

type AthletesSearchIndexEntriesQueryParams = {
	eventKey: EventKey;
};

export const getAthletesSearchIndexEntriesQuery = ({ eventKey }: AthletesSearchIndexEntriesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const query = `
		SELECT 
			e.event_id,
			e.esw_id,
			ra.roster_athlete_id AS athlete_id,
			COALESCE(rc.state, '') AS state,
			LOWER(
				ma.first || '|' || ma.last || '|' || 
				rc.club_name || '|' || rt.team_name || '|' || COALESCE(rt.organization_code, '')
			) as search_content,
			ROW_NUMBER() OVER (PARTITION BY e.event_id ORDER BY 
				CASE 
				    WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey, 0)
				    ELSE COALESCE(ra.jersey, ma.jersey, 0)
				END,
				ma.last,
				ma.first,
				rt.team_name, 
				ra.roster_athlete_id
			) AS sort_order
		FROM event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted is NULL
		JOIN roster_team rt 
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		JOIN roster_athlete ra
			ON ra.roster_team_id = rt.roster_team_id
			AND ra.event_id = e.event_id
			AND ra.deleted IS NULL
			AND ra.deleted_by_user IS NULL
			AND (ra.as_staff = 0 OR ra.as_staff IS NULL)
		JOIN master_athlete ma 
			ON ma.master_athlete_id = ra.master_athlete_id
			AND ma.deleted is NULL
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
	`;

	return [query, sqlVars.getValues()];
};
