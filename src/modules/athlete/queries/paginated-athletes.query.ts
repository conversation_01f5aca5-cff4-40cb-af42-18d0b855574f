import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type PaginatedAthletesQueryParams = {
	eventKey: EventKey;
	offset?: number;
	limit?: number;
	search?: string;
};

export const getPaginatedAthletesQuery = ({ eventKey, offset, limit, search }: PaginatedAthletesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	let query = `
		SELECT 
			ra.roster_athlete_id AS athlete_id,
			ma.first,
			ma.last,
			rc.club_name,
			rc.state,
			rt.roster_team_id AS team_id,
			rt.team_name,
			rt.organization_code,
			sp.short_name AS short_position,
			(
				CASE
					WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey)
					ELSE COALESCE(ra.jersey, ma.jersey)
				END
			) as uniform,
			COUNT(*) OVER() AS item_count
		FROM event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted is NULL
		JOIN roster_team rt 
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		JOIN roster_athlete ra
			ON ra.roster_team_id = rt.roster_team_id
			AND ra.event_id = e.event_id
			AND ra.deleted IS NULL
			AND ra.deleted_by_user IS NULL
			AND (ra.as_staff = 0 OR ra.as_staff IS NULL)
		JOIN master_athlete ma 
			ON ma.master_athlete_id = ra.master_athlete_id
			AND ma.deleted is NULL
		LEFT JOIN sport_position sp
			ON sp.sport_position_id = COALESCE(ra.sport_position_id, ma.sport_position_id)
		WHERE 
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted IS NULL
	`;

	search = search && search.trim();
	if (search) {
		const sValue = sqlVars.useValue(search);
		const wcVal = sqlVars.useValue(`%${search}%`);
		query += `AND (
			CASE
				WHEN LENGTH(${sValue}) = 2 THEN rc.state ILIKE ${wcVal}
				ELSE (
					ma.first ILIKE ${wcVal} OR
					ma.last ILIKE ${wcVal} OR
					(ma.first || ' ' || ma.last) ILIKE ${wcVal} OR
					(ma.last || ' ' || ma.first) ILIKE ${wcVal} OR
					rc.club_name ILIKE ${wcVal} OR
					rt.team_name ILIKE ${wcVal} OR
					rt.organization_code ILIKE ${wcVal}
				)
			END
		) `;
	}

	query += `
		ORDER BY 
			CASE 
				WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey, 0)
				ELSE COALESCE(ra.jersey, ma.jersey, 0)
			END,
			ma.last,
			ma.first,
			rt.team_name, 
			ra.roster_athlete_id
	`;

	if (offset) {
		query += `OFFSET ${sqlVars.useValue(offset)} `;
	}

	if (limit) {
		query += `LIMIT ${sqlVars.useValue(limit)} `;
	}

	return [query, sqlVars.getValues()];
};
