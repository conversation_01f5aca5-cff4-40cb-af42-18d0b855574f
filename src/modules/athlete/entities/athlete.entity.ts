import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class Athlete {
	@Field(() => ID)
	athlete_id: string;

	@Field()
	first: string;

	@Field()
	last: string;

	@Field()
	club_name: string;

	@Field(() => ID)
	team_id: string;

	@Field()
	team_name: string;

	@Field({ nullable: true })
	organization_code: string;

	@Field({ nullable: true })
	state: string;

	@Field({ nullable: true })
	short_position: string;

	@Field({ nullable: true })
	uniform: string;
}
