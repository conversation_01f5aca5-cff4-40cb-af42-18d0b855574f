import { SchemaDefinition } from 'redis-om';

import { AthleteSearchIndexEntry } from '../types';

export const MIN_SEARCH_LENGTH = 2;

export const ATHLETES_INDEX_SCHEMA: SchemaDefinition<AthleteSearchIndexEntry> = {
	esw_id: { type: 'string' },
	event_id: { type: 'string' },
	athlete_id: { type: 'string', indexed: false },
	state: { type: 'string' },
	search_content: { type: 'text' },
	sort_order: { type: 'number', sortable: true },
};
