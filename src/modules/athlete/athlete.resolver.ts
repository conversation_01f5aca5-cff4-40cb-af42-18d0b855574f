import { Args, Query, Resolver } from '@nestjs/graphql';

import { EventKey } from '@/shared/utils/event';

import { AthleteService } from './athlete.service';
import { PaginatedAthletesInputDto } from './dto/paginated-athletes.dto';
import { Athlete, PaginatedAthletes } from './entities';

@Resolver(() => Athlete)
export class AthleteResolver {
	constructor(private readonly athleteService: AthleteService) {}

	@Query(() => PaginatedAthletes)
	paginatedAthletes(@Args() args: PaginatedAthletesInputDto): Promise<PaginatedAthletes> {
		return this.athleteService.fetchPaginatedAthletes({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}
}
