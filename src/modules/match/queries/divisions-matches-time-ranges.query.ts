import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type DivisionsMatchesTimeRangesQueryParams = {
	eventId: string;
	divisionsIds: string[];
};

export const getDivisionsMatchesTimeRangesQuery = ({
	eventId,
	divisionsIds,
}: DivisionsMatchesTimeRangesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const divisionsIdsValues = divisionsIds.map((id) => sqlVars.useValue(Number(id)));

	const query = `
		SELECT
			division_id,
			TO_CHAR(DATE(m.secs_start), 'YYYY-MM-DD') AS day,
			TO_CHAR(MIN(m.secs_start), 'HH24:MI') AS start_time,
			TO_CHAR(
				MAX(COALESCE(m.secs_end, m.secs_start)),
				'HH24:MI'
			) AS end_time
		FROM
			matches m
		WHERE
			m.event_id = ${sqlVars.useValue(Number(eventId))}
			AND m.division_id IN (${divisionsIdsValues.join(',')})
			AND m.secs_start IS NOT NULL
			AND m.day > 0
		GROUP BY
			m.division_id, DATE(m.secs_start)
		ORDER BY
    	m.division_id, DATE(m.secs_start)
	`;

	return [query, sqlVars.getValues()];
};
