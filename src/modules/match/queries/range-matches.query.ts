import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type RangeMatchesQueryParams = {
	eventKey: EventKey;
	after: string;
	before: string;
};

export const getRangeMatchesQuery = ({ eventKey, after, before }: RangeMatchesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	// Selecting the team_id and opponent_id from the matches table due to utilizing the left join on the roster_team table.
	// This is done to ensure that the team_id and opponent_id are always present in the result set,
	// as we can have matches without assigned teams in a scope of a division
	const query = `
		SELECT
			e.event_id,
			m.team1_roster_id AS team_id,
			m.team2_roster_id AS opponent_id,
			m.ref_roster_id AS ref_team_id,
			m.match_id,
			m.pool_bracket_id,
			m.court_id,
			m.display_name AS match_name,
			m.match_number,
			m.division_id,
			m.division_short_name AS division_name,
			extract(epoch FROM m.secs_start)::BIGINT * 1000 AS secs_start,
			extract(epoch FROM m.secs_end)::BIGINT * 1000 AS secs_end,
			extract(epoch FROM m.secs_finished)::BIGINT * 1000 AS secs_finished,
			m.results,
			m.source,
			m.finishes,
			m.is_tie_breaker as is_tb,
			pb.pb_seeds,
			pb.pb_finishes,
			pb.is_pool,
			FALSE as team12_swap,
			CASE
				WHEN rt.roster_team_id IS NULL THEN NULL
				ELSE jsonb_build_object(
				  'team_id', rt.roster_team_id,
				  'master_team_id', rt.master_team_id,
				  'team_name', rt.team_name
				)
			END AS team_info,
			CASE
				WHEN opp_rt.roster_team_id IS NULL THEN NULL
				ELSE jsonb_build_object(
				  'team_id', opp_rt.roster_team_id,
				  'master_team_id', opp_rt.master_team_id,
				  'team_name', opp_rt.team_name
				)
			END AS opponent_info,
			CASE
				WHEN ref_rt.roster_team_id IS NULL THEN NULL
				ELSE jsonb_build_object(
				  'team_id', ref_rt.roster_team_id,
				  'master_team_id', ref_rt.master_team_id,
				  'team_name', ref_rt.team_name
				)
			END AS ref_team_info,
			jsonb_build_object(
			  'uuid', c.uuid,
			  'short_name', c.short_name
			) AS court_info
		FROM
			event e
		JOIN matches m
			ON m.event_id = e.event_id
		JOIN poolbrackets pb
			ON pb.uuid = m.pool_bracket_id
		LEFT JOIN roster_team rt
			ON rt.event_id = e.event_id
			AND rt.division_id = m.division_id
			AND rt.roster_team_id = m.team1_roster_id
		LEFT JOIN roster_team opp_rt
			ON opp_rt.event_id = e.event_id
			AND opp_rt.division_id = m.division_id
			AND opp_rt.roster_team_id = m.team2_roster_id
		LEFT JOIN roster_team ref_rt
			ON ref_rt.event_id = e.event_id
			AND ref_rt.division_id = m.division_id
			AND ref_rt.roster_team_id = m.ref_roster_id
		JOIN courts c
			ON m.court_id = c.uuid
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted IS NULL
			AND m.secs_start >= ${sqlVars.useValue(after)}::timestamp AND m.secs_start <= ${sqlVars.useValue(before)}::timestamp
		ORDER BY
			m.secs_start
	`;

	return [query, sqlVars.getValues()];
};
