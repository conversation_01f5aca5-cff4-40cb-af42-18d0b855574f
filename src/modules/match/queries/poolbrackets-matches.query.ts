import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type PoolBracketsMatchesQueryParams = {
	eventId: string;
	poolBracketsIds: string[];
};

export const getPoolBracketsMatchesQuery = ({ eventId, poolBracketsIds }: PoolBracketsMatchesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const poolBracketsIdsValues = poolBracketsIds.map((id) => sqlVars.useValue(id));

	const query = `
		SELECT
			m.event_id,
			m.team1_roster_id AS team_id,
			m.team2_roster_id AS opponent_id,
			m.ref_roster_id AS ref_team_id,
			m.match_id,
			m.pool_bracket_id,
			m.court_id,
			m.display_name AS match_name,
			m.match_number,
			m.division_id,
			m.division_short_name AS division_name,
			extract(epoch FROM m.secs_start)::BIGINT * 1000 AS secs_start,
			extract(epoch FROM m.secs_end)::BIGINT * 1000 AS secs_end,
			extract(epoch FROM m.secs_finished)::BIGINT * 1000 AS secs_finished,
			m.results,
			m.source,
			m.finishes,
			m.is_tie_breaker as is_tb,
			pb.pb_seeds,
			pb.pb_finishes,
			pb.is_pool,
			FALSE as team12_swap,
			CASE
				WHEN rt.roster_team_id IS NULL THEN NULL
				ELSE jsonb_build_object(
				  'team_id', rt.roster_team_id,
				  'master_team_id', rt.master_team_id,
				  'team_name', rt.team_name
				)
			END AS team_info,
			CASE
				WHEN opp_rt.roster_team_id IS NULL THEN NULL
				ELSE jsonb_build_object(
				  'team_id', opp_rt.roster_team_id,
				  'master_team_id', opp_rt.master_team_id,
				  'team_name', opp_rt.team_name
				)
			END AS opponent_info,
			CASE
				WHEN ref_rt.roster_team_id IS NULL THEN NULL
				ELSE jsonb_build_object(
				  'team_id', ref_rt.roster_team_id,
				  'master_team_id', ref_rt.master_team_id,
				  'team_name', ref_rt.team_name
				)
			END AS ref_team_info,
			jsonb_build_object(
			  'uuid', c.uuid,
			  'short_name', c.short_name
			) AS court_info
		FROM
			matches m
		JOIN poolbrackets pb
			ON pb.uuid = m.pool_bracket_id
		LEFT JOIN roster_team rt
			ON rt.event_id = m.event_id
			AND rt.division_id = m.division_id
			AND rt.roster_team_id = m.team1_roster_id
		LEFT JOIN roster_team opp_rt
			ON opp_rt.event_id = m.event_id
			AND opp_rt.division_id = m.division_id
			AND opp_rt.roster_team_id = m.team2_roster_id
		LEFT JOIN roster_team ref_rt
			ON ref_rt.event_id = m.event_id
			AND ref_rt.division_id = m.division_id
			AND ref_rt.roster_team_id = m.ref_roster_id
		JOIN courts c
			ON m.court_id = c.uuid
		WHERE
			m.event_id = ${sqlVars.useValue(Number(eventId))}
			AND m.pool_bracket_id IN (${poolBracketsIdsValues.map((id) => `${id}::uuid`).join(',')})
		ORDER BY
			m.match_number
	`;

	return [query, sqlVars.getValues()];
};
