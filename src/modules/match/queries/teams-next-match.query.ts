import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type TeamsNextMatchQueryParams = {
	eventId: string;
	teamsIds: string[];
};

export const getTeamsNextMatchQuery = ({ eventId, teamsIds }: TeamsNextMatchQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const teamsIdsValues = teamsIds.map((id) => sqlVars.useValue(Number(id)));

	const query = `
    SELECT DISTINCT ON (rt.roster_team_id)
			rt.event_id,
			rt.roster_team_id AS team_id,
			CASE
				WHEN rt.roster_team_id = m.team1_roster_id THEN m.team2_roster_id
				ELSE m.team1_roster_id
			END AS opponent_id,
			m.ref_roster_id AS ref_team_id,
			m.match_id,
			m.pool_bracket_id,
			m.court_id,
			m.display_name AS match_name,
			m.match_number,
			m.division_id,
			m.division_short_name AS division_name,
			extract(epoch FROM m.secs_start)::BIGINT * 1000 AS secs_start,
			extract(epoch FROM m.secs_end)::BIGINT * 1000 AS secs_end,
			extract(epoch FROM m.secs_finished)::BIGINT * 1000 AS secs_finished,
			m.results,
			m.source,
			m.finishes,
			m.is_tie_breaker as is_tb,
			pb.pb_seeds,
			pb.pb_finishes,
			pb.is_pool,
			CASE
				WHEN rt.roster_team_id = m.team1_roster_id THEN FALSE
				ELSE TRUE
			END AS team12_swap,
			jsonb_build_object(
			  'team_id', rt.roster_team_id,
			  'master_team_id', rt.master_team_id,
			  'team_name', rt.team_name
			) AS team_info,
			CASE
				WHEN opp_rt.roster_team_id IS NULL THEN NULL
				ELSE jsonb_build_object(
				  'team_id', opp_rt.roster_team_id,
				  'master_team_id', opp_rt.master_team_id,
				  'team_name', opp_rt.team_name
				)
			END AS opponent_info,
			NULL as ref_team_info,
			jsonb_build_object(
			  'uuid', c.uuid,
			  'short_name', c.short_name
			) AS court_info
		FROM
			roster_team rt
		JOIN matches m
			ON m.event_id = rt.event_id
			AND m.division_id = rt.division_id
			AND rt.roster_team_id IN (m.team1_roster_id, m.team2_roster_id)
		JOIN poolbrackets pb
			ON pb.uuid = m.pool_bracket_id
		LEFT JOIN roster_team opp_rt 
			ON opp_rt.event_id = rt.event_id
			AND opp_rt.division_id = rt.division_id
			AND opp_rt.roster_team_id = (
				CASE
					WHEN rt.roster_team_id = m.team1_roster_id THEN m.team2_roster_id
					ELSE m.team1_roster_id
				END
			)
		JOIN courts c 
			ON m.court_id = c.uuid
		JOIN event e
			ON e.event_id = rt.event_id
		WHERE
			rt.event_id = ${sqlVars.useValue(Number(eventId))}
			AND rt.roster_team_id IN (${teamsIdsValues.join(',')})
			AND (
        (
					m.secs_start <= (NOW() AT TIME ZONE e.timezone) 
					AND (m.secs_finished IS NULL OR m.secs_finished > (NOW() AT TIME ZONE e.timezone))
				)
        OR 
        	m.secs_start > (NOW() AT TIME ZONE e.timezone)
      )
			
		ORDER BY 
			rt.roster_team_id,
			m.secs_start ASC
	`;

	return [query, sqlVars.getValues()];
};
