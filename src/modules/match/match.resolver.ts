import { Args, Query, Resolver } from '@nestjs/graphql';

import { ProvidesData, DataProvidersParam } from '@/infra/dataSharing/decorators';
import { DataTypes, DataProviders } from '@/infra/dataSharing/types';
import { FilteredMatches, Match } from '@/modules/match/entities';
import { EventKey } from '@/shared/utils/event';

import { DayRangeMatchesInputDto } from './dto/day-range-matches.dto';
import { MatchService } from './match.service';

@Resolver(() => Match)
export class MatchResolver {
	constructor(private readonly matchService: MatchService) {}

	@Query(() => FilteredMatches)
	@ProvidesData(DataTypes.Matches)
	async dayRangeMatches(
		@Args() args: DayRangeMatchesInputDto,
		@DataProvidersParam() [matchesProvider]: DataProviders<[DataTypes.Matches]>,
	): Promise<FilteredMatches> {
		const filteredMatches = await this.matchService.fetchDayRangeMatches({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
		matchesProvider(filteredMatches.items);
		return filteredMatches;
	}
}
