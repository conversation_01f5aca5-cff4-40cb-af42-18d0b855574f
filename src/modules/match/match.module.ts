import { Modu<PERSON> } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { DataSharingModule } from '@/infra/dataSharing/dataSharing.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { MatchRepository } from './match.repository';
import { MatchResolver } from './match.resolver';
import { MatchService } from './match.service';

@Module({
	imports: [PrismaModule, CacheModule, DataSharingModule],
	providers: [MatchResolver, MatchService, MatchRepository],
	exports: [MatchService],
})
export class MatchModule {}
