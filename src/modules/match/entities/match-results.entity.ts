import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
class MatchTeamResults {
	@Field({ nullable: true })
	scores?: string;

	@Field({ nullable: true })
	roster_team_id?: string;
}

@ObjectType()
export class MatchResults {
	@Field(() => [String])
	sets: string[];

	@Field(() => MatchTeamResults, { nullable: true })
	team1?: MatchTeamResults;

	@Field(() => MatchTeamResults, { nullable: true })
	team2?: MatchTeamResults;

	@Field({ nullable: true })
	winner?: string;

	@Field(() => MatchTeamResults, { nullable: true })
	winner_team?: MatchTeamResults;
}
