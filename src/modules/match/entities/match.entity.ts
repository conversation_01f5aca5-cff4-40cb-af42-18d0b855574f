import { Field, ObjectType, ID } from '@nestjs/graphql';

import { MatchExternal } from './match-external.entity';
import { MatchFinishes } from './match-finishes.entity';
import { MatchResults } from './match-results.entity';
import { MatchSource } from './match-source.entity';

@ObjectType()
export class Match {
	event_id: string;

	@Field(() => ID, { nullable: true })
	team_id: string;

	@Field(() => ID, { nullable: true })
	opponent_id: string;

	@Field(() => ID, { nullable: true })
	ref_team_id: string;

	@Field(() => ID)
	match_id: string;

	@Field(() => ID)
	pool_bracket_id: string;

	@Field(() => ID)
	court_id: string;

	@Field({ nullable: true })
	match_name?: string;

	@Field({ nullable: true })
	match_number: number;

	@Field(() => ID)
	division_id: string;

	@Field({ nullable: true })
	division_name?: string;

	@Field({ nullable: true })
	secs_start?: number;

	@Field({ nullable: true })
	secs_end?: number;

	@Field({ nullable: true })
	secs_finished?: number;

	@Field({ nullable: true })
	is_tb?: number;

	@Field(() => MatchSource, { nullable: true })
	source?: MatchSource;

	@Field(() => MatchResults, { nullable: true })
	results?: MatchResults;

	@Field(() => MatchFinishes, { nullable: true })
	finishes?: MatchFinishes;

	@Field(() => MatchExternal)
	external: MatchExternal;
}
