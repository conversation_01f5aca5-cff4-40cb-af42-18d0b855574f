import { Field, ObjectType } from '@nestjs/graphql';

import { CourtShortInfo } from '@/modules/court/entities';
import { PoolOrBracketSeedItem, PoolOrBracketShortInfo } from '@/modules/poolBracket/entities';
import { TeamShortInfo } from '@/modules/team/entities';

@ObjectType()
export class MatchExternal {
	@Field(() => PoolOrBracketShortInfo)
	pool_bracket_info: PoolOrBracketShortInfo;

	@Field(() => PoolOrBracketSeedItem, { nullable: true })
	team_pb_seed?: PoolOrBracketSeedItem;

	@Field(() => PoolOrBracketSeedItem, { nullable: true })
	opponent_pb_seed?: PoolOrBracketSeedItem;

	@Field(() => PoolOrBracketSeedItem, { nullable: true })
	ref_team_pb_seed?: PoolOrBracketSeedItem;

	@Field(() => CourtShortInfo)
	court_info: CourtShortInfo;

	@Field(() => TeamShortInfo, { nullable: true })
	team_info?: TeamShortInfo;

	@Field(() => TeamShortInfo, { nullable: true })
	opponent_info?: TeamShortInfo;

	@Field(() => TeamShortInfo, { nullable: true })
	ref_team_info?: TeamShortInfo;

	@Field()
	team_display_name: string;

	@Field()
	opponent_display_name: string;

	@Field()
	ref_team_display_name: string;

	@Field()
	has_finishes: boolean;
}
