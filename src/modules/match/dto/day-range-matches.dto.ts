import { ArgsType, Field, ID } from '@nestjs/graphql';
import { IsISO8601, IsOptional } from 'class-validator';

import { IsEventKeyValue } from '@/shared/graphql/dto/validators/is-event-key-value.validator';
import { IsNumericId } from '@/shared/graphql/dto/validators/is-numeric-id.validator';

@ArgsType()
export class DayRangeMatchesInputDto {
	@Field(() => ID)
	@IsEventKeyValue()
	eventKey: string;

	@Field(() => ID, { nullable: true })
	@IsOptional()
	@IsNumericId()
	divisionId?: string;

	@Field(() => String)
	@IsISO8601({}, { message: '"after" must be a valid ISO 8601 timestamp' })
	after: string;

	@Field(() => String)
	@IsISO8601({}, { message: '"before" must be a valid ISO 8601 timestamp' })
	before: string;
}
