import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { CourtShortInfo } from '@/modules/court/entities';
import { PoolOrBracketSeedItem } from '@/modules/poolBracket/entities';
import { TeamShortInfo } from '@/modules/team/entities';
import { TeamAdvancement } from '@/shared/graphql/entities/team-advancement.entity';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';
import { parseWithFallback } from '@/shared/utils/json';
import { stripKeysInPlace } from '@/shared/utils/object';

import { Match, MatchesTimeRange, MatchSourceItem } from './entities';
import {
	getMatchesQuery,
	getRangeMatchesQuery,
	getTeamsMatchesQuery,
	getTeamsNextMatchQuery,
	getPoolBracketsMatchesQuery,
	getDivisionsMatchesTimeRangesQuery,
	MatchesQueryParams,
	RangeMatchesQueryParams,
	TeamsMatchesQueryParams,
	TeamsNextMatchQueryParams,
	PoolBracketsMatchesQueryParams,
	DivisionsMatchesTimeRangesQueryParams,
} from './queries';

export type FetchMatchesParams = MatchesQueryParams;
export type FetchRangeMatchesParams = RangeMatchesQueryParams;
export type FetchTeamsMatchesParams = TeamsMatchesQueryParams;
export type FetchTeamsNextMatchParams = TeamsNextMatchQueryParams;
export type FetchPoolBracketsMatchesParams = PoolBracketsMatchesQueryParams;
export type FetchDivisionsMatchesTimeRangesParams = DivisionsMatchesTimeRangesQueryParams;

const MATCH_STRING_CAST_FIELDS: StringCastKeys<Match>[] = [
	'event_id',
	'match_id',
	'team_id',
	'opponent_id',
	'ref_team_id',
	'division_id',
	'secs_start',
	'secs_end',
	'secs_finished',
];
const MATCHES_TIME_RANGE_STRING_CAST_FIELDS: StringCastKeys<MatchesTimeRange>[] = ['division_id'];
const MATCH_INTERMEDIATE_FIELDS: string[] = [
	'pb_seeds',
	'pb_finishes',
	'is_pool',
	'team12_swap', // TODO: The swap logic must be removed as soon as we remove the support for team_next_match
	'team_info',
	'opponent_info',
	'ref_team_info',
	'court_info',
];

@Injectable()
export class MatchRepository {
	constructor(private prisma: PrismaService) {}

	async fetchMatches(params: FetchMatchesParams): Promise<Match[]> {
		const [query, values] = getMatchesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Match[]>(query, ...values);
		return this._formatMatchesFields(items);
	}

	async fetchTeamsMatches(params: FetchTeamsMatchesParams): Promise<Match[]> {
		const [query, values] = getTeamsMatchesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Match[]>(query, ...values);
		return this._formatMatchesFields(items);
	}

	async fetchTeamsNextMatch(params: FetchTeamsNextMatchParams): Promise<Match[]> {
		const [query, values] = getTeamsNextMatchQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Match[]>(query, ...values);
		return this._formatMatchesFields(items);
	}

	async fetchMatchesInRange(params: FetchRangeMatchesParams): Promise<Match[]> {
		const [query, values] = getRangeMatchesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Match[]>(query, ...values);
		return this._formatMatchesFields(items);
	}

	async fetchPoolBracketsMatches(params: FetchPoolBracketsMatchesParams): Promise<Match[]> {
		const [query, values] = getPoolBracketsMatchesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Match[]>(query, ...values);
		return this._formatMatchesFields(items);
	}

	async fetchDivisionsMatchesTimeRanges(params: FetchDivisionsMatchesTimeRangesParams): Promise<MatchesTimeRange[]> {
		const [query, values] = getDivisionsMatchesTimeRangesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<MatchesTimeRange[]>(query, ...values);
		return this._formatMatchesTimeRangesFields(items);
	}

	private _formatMatchesTimeRangesFields(items: MatchesTimeRange[]): MatchesTimeRange[] {
		return castFieldsToString(items, MATCHES_TIME_RANGE_STRING_CAST_FIELDS);
	}

	private _formatMatchesFields(items: Match[]): Match[] {
		castFieldsToString(items, MATCH_STRING_CAST_FIELDS);
		items.forEach((item) => {
			this._formatMatchSource(item);
			this._formatMatchResults(item);
			this._formatMatchExternal(item);
			this._formatMatchFinishes(item);
			this._cleanupMatchIntermediateFields(item);
		});
		return items;
	}

	private _formatMatchSource(match: Match): void {
		const { source } = match;
		if (!source) return;
		const { team12_swap } = match as unknown as Record<string, boolean>;
		const { team1, team2, ref } = parseWithFallback<Record<string, MatchSourceItem>>(source as unknown as string, {});

		match.source = {
			team: team12_swap ? team2 : team1,
			opponent: team12_swap ? team1 : team2,
			ref,
		};
	}

	private _formatMatchResults(match: Match): void {
		const { results } = match;
		if (!results) return;
		// Convert the results object into an array of sets
		const sets = [];
		Object.entries(results).forEach(([key, value]) => {
			if (key.startsWith('set')) {
				sets[key.slice(3)] = value;
			}
		});
		sets.shift(); // Remove the first empty element
		results.sets = sets;
		const { team1, team2, winner } = results;
		results.winner_team = winner === '1' ? team1 : winner === '2' ? team2 : null;
	}

	private _formatMatchExternal(match: Match): void {
		const { team_info, opponent_info, ref_team_info } = match as unknown as Record<string, TeamShortInfo>;
		const { court_info } = match as unknown as Record<string, CourtShortInfo>;
		const { source } = match;

		const pbSeedData = this._getMatchPbSeedData(match);
		const [ref_team_pb_seed, ref_team_pb_name] = this._processMatchPbSeedData(pbSeedData, source?.ref);
		const [team_pb_seed, team_pb_name] = this._processMatchPbSeedData(pbSeedData, source?.team);
		const [opponent_pb_seed, opponent_pb_name] = this._processMatchPbSeedData(pbSeedData, source?.opponent);

		const { is_pool, pb_finishes } = match as unknown as Record<string, unknown>;
		match.external = {
			court_info,
			team_info,
			opponent_info,
			ref_team_info,
			// Presetting the display names from the *_info and fallback to the pb_name if the info is not available
			team_display_name: team_info?.team_name || team_pb_name,
			opponent_display_name: opponent_info?.team_name || opponent_pb_name,
			ref_team_display_name: ref_team_info?.team_name || ref_team_pb_name,
			team_pb_seed,
			opponent_pb_seed,
			ref_team_pb_seed,
			pool_bracket_info: {
				uuid: match.pool_bracket_id,
				is_pool: is_pool as number,
			},
			// If the match is a pool match, we use pb_finishes, otherwise we use match.finishes for the bracket match
			has_finishes: !!(is_pool ? pb_finishes : match.finishes),
		};
	}

	private _formatMatchFinishes(match: Match): void {
		const { finishes } = match;
		if (!finishes) return;
		const { Winner, Loser } = parseWithFallback<Record<string, TeamAdvancement | undefined>>(finishes as unknown as string, {});
		if (!Winner && !Loser) return;

		match.finishes = {
			winner: Winner && this._formatTeamAdvancement(Winner),
			looser: Loser && this._formatTeamAdvancement(Loser),
		};
	}

	private _formatTeamAdvancement(teamAdvancement: TeamAdvancement): TeamAdvancement {
		// TODO Make this logic model-related, it should be a part of the model during the future refactor
		const { next_match, next_ref } = teamAdvancement;
		teamAdvancement.next_match = next_match && Object.keys(next_match).length ? next_match : undefined;
		teamAdvancement.next_ref = next_ref && Object.keys(next_ref).length ? next_ref : undefined;
		return teamAdvancement;
	}

	private _getMatchPbSeedData(match: Match): Map<number, PoolOrBracketSeedItem> {
		// TODO Make this logic model-related, it should be a part of the model during the future refactor
		const { pb_seeds: pbSeedsJson } = match as unknown as Record<string, string>;
		const pbSeeds = parseWithFallback<Record<string, string>>(pbSeedsJson, {});

		const pbSeedData = new Map<number, PoolOrBracketSeedItem>();
		Object.entries(pbSeeds).forEach(([key, value]) =>
			pbSeedData.set(parseInt(key, 10), parseWithFallback(value, {} as PoolOrBracketSeedItem)),
		);
		return pbSeedData;
	}

	private _processMatchPbSeedData(
		pbSeedData: Map<number, PoolOrBracketSeedItem>,
		sourceItem?: MatchSourceItem,
	): [PoolOrBracketSeedItem | undefined, string] {
		if (!sourceItem) return [undefined, ''];
		const { type, seed, name: sourceName, id: sourceId } = sourceItem;
		// TODO: Figure out about the magic number 5
		if (type === 5 && seed) {
			const pbSeedItem = pbSeedData.get(seed);
			const pbName = pbSeedItem?.name || sourceName || '';
			return [pbSeedItem, pbName];
		}
		// TODO: Figure out about the magic number 6
		if (type === 6) {
			for (const pbSeedItem of pbSeedData.values()) {
				if (pbSeedItem.name === sourceName || (pbSeedItem.id && pbSeedItem.id === sourceId)) {
					return [pbSeedItem, sourceName];
				}
			}
		}
		return [undefined, sourceName || ''];
	}

	private _cleanupMatchIntermediateFields(match: Match): void {
		stripKeysInPlace(match, MATCH_INTERMEDIATE_FIELDS);
	}
}
