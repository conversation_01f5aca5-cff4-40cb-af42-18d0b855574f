import { Injectable } from '@nestjs/common';
import * as moment from 'moment';

import { ExtendedCacheService, ItemsCacheKeyConfig, ItemCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { FilteredMatches, Match, MatchesTimeRange } from '@/modules/match/entities';
import { CacheCategories, CacheScopes } from '@/shared/constants/cache';
import { groupBy, keyBy } from '@/shared/utils/collection';
import { createFilterInfo } from '@/shared/utils/filter';
import { completeObject } from '@/shared/utils/object';

import {
	FetchMatchesParams,
	FetchTeamsMatchesParams,
	FetchTeamsNextMatchParams,
	FetchRangeMatchesParams,
	FetchPoolBracketsMatchesParams,
	FetchDivisionsMatchesTimeRangesParams,
	MatchRepository,
} from './match.repository';

export type FetchDayMatchesParams = Pick<FetchRangeMatchesParams, 'eventKey'> & {
	day: string;
};

export type FetchDayRangeMatchesParams = Pick<FetchRangeMatchesParams, 'eventKey'> & {
	divisionId?: string;
	after: string;
	before: string;
};

export type FetchTeamsMatchesServiceParams = Pick<FetchTeamsMatchesParams, 'eventId' | 'teamsIds'>;

@Injectable()
export class MatchService {
	constructor(private matchRepository: MatchRepository, private cacheService: ExtendedCacheService) {}

	fetchMatchesMap(params: FetchMatchesParams): Promise<Record<string, Match | null>> {
		const cacheKeyConfig = this._getMatchesCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Match | null>(cacheKeyConfig, params.matchesIds, (missingIds) =>
			this._getMatchesData({ ...params, matchesIds: missingIds }),
		);
	}

	fetchTeamsMatchesMap(params: FetchTeamsMatchesServiceParams): Promise<Record<string, Match[]>> {
		const cacheKeyConfig = this._getTeamsMatchesCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Match[]>(cacheKeyConfig, params.teamsIds, (missingIds) =>
			this._getTeamsMatchesData({ ...params, teamsIds: missingIds }),
		);
	}

	fetchTeamsUpcomingMatchesMap(params: FetchTeamsMatchesServiceParams): Promise<Record<string, Match[]>> {
		const fetchParams = { ...params, upcoming: true };
		const cacheKeyConfig = this._getTeamsMatchesCacheKeyConfig(fetchParams);
		return this.cacheService.getOrFetchMany<Match[]>(cacheKeyConfig, params.teamsIds, (missingIds) =>
			this._getTeamsMatchesData({ ...fetchParams, teamsIds: missingIds }),
		);
	}

	fetchTeamsFinishedMatchesMap(params: FetchTeamsMatchesServiceParams): Promise<Record<string, Match[]>> {
		const fetchParams = { ...params, finished: true };
		const cacheKeyConfig = this._getTeamsMatchesCacheKeyConfig(fetchParams);
		return this.cacheService.getOrFetchMany<Match[]>(cacheKeyConfig, params.teamsIds, (missingIds) =>
			this._getTeamsMatchesData({ ...fetchParams, teamsIds: missingIds }),
		);
	}

	fetchTeamsNextMatchMap(params: FetchTeamsNextMatchParams): Promise<Record<string, Match | null>> {
		const cacheKeyConfig = this._getTeamsNextMatchCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Match | null>(cacheKeyConfig, params.teamsIds, async (missingIds) =>
			this._getTeamsNextMatchData({ ...params, teamsIds: missingIds }),
		);
	}

	fetchPoolBracketsMatchesMap(params: FetchPoolBracketsMatchesParams): Promise<Record<string, Match[]>> {
		const cacheKeyConfig = this._getPoolBracketsMatchesCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Match[]>(cacheKeyConfig, params.poolBracketsIds, (missingIds) =>
			this._getPoolBracketsMatchesData({ ...params, poolBracketsIds: missingIds }),
		);
	}

	async fetchDivisionsMatchesTimeRangesMap(params: FetchDivisionsMatchesTimeRangesParams): Promise<Record<string, MatchesTimeRange[]>> {
		const cacheKeyConfig = this._getDivisionsMatchesTimeRangesCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<MatchesTimeRange[]>(cacheKeyConfig, params.divisionsIds, (missingIds) =>
			this._getDivisionsMatchesTimeRangesData({ ...params, divisionsIds: missingIds }),
		);
	}

	async fetchDayMatches(params: FetchDayMatchesParams): Promise<Match[]> {
		const cacheKeyConfig = this._getDayMatchesCacheKeyConfig(params);
		return this.cacheService.getOrFetch<Match[]>(cacheKeyConfig, () => this._getDayMatchesData(params));
	}

	async fetchDayRangeMatches({ eventKey, after, before, divisionId }: FetchDayRangeMatchesParams): Promise<FilteredMatches> {
		const dayMatches = await this.fetchDayMatches({
			eventKey: eventKey,
			day: after, // It is safe to use `after` as `day` because fetchDayMatches will get the day automatically
		});
		// If there are no matches within the day, return
		if (!dayMatches.length) {
			return { items: [], filter_info: createFilterInfo(0, 0) };
		}
		const afterTimestamp = moment.utc(after).valueOf();
		const beforeTimestamp = moment.utc(before).valueOf();
		const totalMatches = dayMatches.length;
		if (divisionId) {
			const items = dayMatches.filter((match) => {
				const { secs_start } = match;
				return match.division_id === divisionId && secs_start >= afterTimestamp && secs_start < beforeTimestamp;
			});
			return { items, filter_info: createFilterInfo(totalMatches, items.length) };
		} else {
			const items = dayMatches.filter((match) => {
				const { secs_start } = match;
				return secs_start >= afterTimestamp && secs_start < beforeTimestamp;
			});
			return { items, filter_info: createFilterInfo(totalMatches, items.length) };
		}
	}

	// TODO Move to the Match domain shared lib in the future
	filterMatchesByPoolBracket(matches: Match[], poolBracketId: string): Match[] {
		return matches.filter((match) => match.pool_bracket_id === poolBracketId);
	}

	// TODO Move to the Match domain shared lib in the future
	filterMatchesWhereTeamPlays(matches: Match[], teamId: string): Match[] {
		return matches.filter((match) => match.team_id === teamId || match.opponent_id === teamId);
	}

	// TODO Move to the Match domain shared lib in the future
	filterMatchesWhereTeamRefs(matches: Match[], teamId: string): Match[] {
		return matches.filter((match) => match.ref_team_id === teamId);
	}

	private async _getMatchesData(params: FetchMatchesParams): Promise<Record<string, Match | null>> {
		const matches = await this.matchRepository.fetchMatches(params);
		const matchesMap = keyBy(matches, 'match_id');
		return completeObject(params.matchesIds, matchesMap, null);
	}

	private async _getTeamsMatchesData(params: FetchTeamsMatchesParams): Promise<Record<string, Match[]>> {
		const teamsMatches = await this.matchRepository.fetchTeamsMatches(params);
		const { teamsIds } = params;
		const teamsIdsSet = new Set(teamsIds);
		const teamsMatchesMap = Object.fromEntries(teamsIds.map((id) => [id, []]));
		teamsMatches.forEach((match) => {
			const matchTeamsIds = [match.team_id, match.opponent_id, match.ref_team_id];
			matchTeamsIds.forEach((teamId) => {
				if (teamsIdsSet.has(teamId)) {
					teamsMatchesMap[teamId].push(match);
				}
			});
		});
		return teamsMatchesMap;
	}

	private async _getTeamsNextMatchData(params: FetchTeamsNextMatchParams): Promise<Record<string, Match | null>> {
		const teamsNextMatch = await this.matchRepository.fetchTeamsNextMatch(params);
		const teamsNextMatchMap = keyBy(teamsNextMatch, 'team_id');
		return completeObject(params.teamsIds, teamsNextMatchMap, null);
	}

	private async _getPoolBracketsMatchesData(params: FetchPoolBracketsMatchesParams): Promise<Record<string, Match[]>> {
		const poolBracketsMatches = await this.matchRepository.fetchPoolBracketsMatches(params);
		const poolBracketsMatchesMap = groupBy(poolBracketsMatches, 'pool_bracket_id');
		return completeObject(params.poolBracketsIds, poolBracketsMatchesMap, []);
	}

	private async _getDivisionsMatchesTimeRangesData(
		params: FetchDivisionsMatchesTimeRangesParams,
	): Promise<Record<string, MatchesTimeRange[]>> {
		const divisionsMatchesTimeRanges = await this.matchRepository.fetchDivisionsMatchesTimeRanges(params);
		const divisionsMatchesTimeRangesMap = groupBy(divisionsMatchesTimeRanges, 'division_id');
		return completeObject(params.divisionsIds, divisionsMatchesTimeRangesMap, []);
	}

	private async _getDayMatchesData(params: FetchDayMatchesParams): Promise<Match[]> {
		const fromDayDate = moment.utc(params.day).startOf('day');
		const tillDayDate = fromDayDate.clone().endOf('day');
		return this.matchRepository.fetchMatchesInRange({
			eventKey: params.eventKey,
			after: fromDayDate.toISOString(),
			before: tillDayDate.toISOString(),
		});
	}

	private _getMatchesCacheKeyConfig(params: FetchMatchesParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.Match,
		};
	}

	private _getTeamsMatchesCacheKeyConfig(params: FetchTeamsMatchesParams): ItemsCacheKeyConfig {
		let category: CacheCategories;
		if (params.upcoming) {
			category = CacheCategories.TeamsUpcomingMatches;
		} else if (params.finished) {
			category = CacheCategories.TeamsFinishedMatches;
		} else {
			category = CacheCategories.TeamsMatches;
		}

		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category,
		};
	}

	private _getTeamsNextMatchCacheKeyConfig(params: FetchTeamsNextMatchParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.TeamsNextMatch,
		};
	}

	private _getPoolBracketsMatchesCacheKeyConfig(params: FetchPoolBracketsMatchesParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.PoolBracketMatches,
		};
	}

	private _getDivisionsMatchesTimeRangesCacheKeyConfig(params: FetchDivisionsMatchesTimeRangesParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.DivisionMatchTimeRange,
		};
	}

	private _getDayMatchesCacheKeyConfig(params: FetchDayMatchesParams): ItemCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.DayMatch,
			categoryKey: params.day.slice(0, 10),
		};
	}
}
