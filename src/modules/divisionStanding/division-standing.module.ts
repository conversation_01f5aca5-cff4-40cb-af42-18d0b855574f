import { Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { DivisionStandingRepository } from './division-standing.repository';
import { DivisionStandingService } from './division-standing.service';

@Module({
	imports: [PrismaModule, CacheModule],
	providers: [DivisionStandingService, DivisionStandingRepository],
	exports: [DivisionStandingService],
})
export class DivisionStandingModule {}
