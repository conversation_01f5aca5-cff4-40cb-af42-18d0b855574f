import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';

import { DivisionStanding } from './entities';
import { getTeamsDivisionStandingQuery, TeamsDivisionStandingQueryParams } from './queries/teams-division-standing.query';

export type FetchTeamsDivisionStandingParams = TeamsDivisionStandingQueryParams;

const DIVISION_STANDING_STRING_CAST_FIELDS: StringCastKeys<DivisionStanding>[] = ['event_id', 'team_id', 'division_id'];

@Injectable()
export class DivisionStandingRepository {
	constructor(private prisma: PrismaService) {}

	async fetchTeamsDivisionStanding(params: FetchTeamsDivisionStandingParams): Promise<DivisionStanding[]> {
		const [query, values] = getTeamsDivisionStandingQuery(params);
		const items = await this.prisma.$queryRawUnsafe<DivisionStanding[]>(query, ...values);
		return this._formatDivisionStandingFields(items);
	}

	private _formatDivisionStandingFields(items: DivisionStanding[]): DivisionStanding[] {
		return castFieldsToString(items, DIVISION_STANDING_STRING_CAST_FIELDS);
	}
}
