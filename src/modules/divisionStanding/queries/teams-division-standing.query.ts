import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type TeamsDivisionStandingQueryParams = {
	eventId: string;
	divisionTeamPairs: { division_id: string; team_id: string }[];
};

export const getTeamsDivisionStandingQuery = ({
	eventId,
	divisionTeamPairs,
}: TeamsDivisionStandingQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const divisionTeamPairsValues = divisionTeamPairs.map(
		({ division_id, team_id }) => `(${sqlVars.useValue(Number(division_id))}, ${sqlVars.useValue(Number(team_id))})`,
	);

	const query = `
		SELECT 
			ds.event_id,
			ds.uuid as division_standing_id,
			ds.division_id,
			ds.team_id,
			ds.matches_won,
			ds.matches_lost,
			ds.sets_won,
			ds.sets_lost,
			ds.sets_pct,
			ds.points_ratio,
			ds.rank,
			CAST(ds.info::JSON->>'seed_current' AS INTEGER) AS seed,
			CAST(ds.info::JSON->>'seed_original' AS INTEGER) AS seed_original,
			CAST(ds.info::JSON->>'points' AS INTEGER) AS points,
			COALESCE(NULLIF(ds.info->>'heading', ''), 'None') AS heading,
			COALESCE(CAST(ds.info->>'heading_sort_priority' AS INTEGER), 0) AS heading_priority
		FROM 
			division_standing ds
		WHERE 
			(ds.division_id, ds.team_id) IN (${divisionTeamPairsValues.join(',')})
			AND ds.event_id = ${sqlVars.useValue(Number(eventId))}
	`;

	return [query, sqlVars.getValues()];
};
