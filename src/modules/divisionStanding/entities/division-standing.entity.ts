import { Field, ObjectType, ID } from '@nestjs/graphql';

@ObjectType()
export class DivisionStanding {
	event_id: string;

	@Field(() => ID)
	division_standing_id: string;

	@Field(() => ID)
	team_id: string;

	@Field(() => ID)
	division_id: string;

	@Field()
	matches_won: number;

	@Field()
	matches_lost: number;

	@Field()
	sets_won: number;

	@Field()
	sets_lost: number;

	@Field()
	sets_pct: number;

	@Field()
	points_ratio: number;

	@Field()
	rank: number;

	@Field({ nullable: true })
	seed: number;

	@Field({ nullable: true })
	seed_original: number;

	@Field({ nullable: true })
	points: number;

	@Field()
	heading: string;

	@Field()
	heading_priority: number;
}
