import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheScopes, CacheCategories } from '@/shared/constants/cache';

import { DivisionStandingRepository, FetchTeamsDivisionStandingParams } from './division-standing.repository';
import { DivisionStanding } from './entities';

@Injectable()
export class DivisionStandingService {
	constructor(private divisionStandingRepository: DivisionStandingRepository, private cacheService: ExtendedCacheService) {}

	async fetchTeamsDivisionStanding(params: FetchTeamsDivisionStandingParams): Promise<DivisionStanding[]> {
		const cacheKeyConfig = this._getTeamsDivisionStandingCacheKeyConfig(params);
		const pairCacheKeys = params.divisionTeamPairs.map(this._toDivisionTeamPairCacheKey);
		const teamsDivisionStanding = await this.cacheService.getOrFetchMany<DivisionStanding | null>(
			cacheKeyConfig,
			pairCacheKeys,
			(missingPairCacheKeys) => this._getTeamsDivisionStandingDataByPairCacheKeys(params, missingPairCacheKeys),
		);
		return Object.values(teamsDivisionStanding).filter(Boolean);
	}

	private async _getTeamsDivisionStandingDataByPairCacheKeys(
		params: Omit<FetchTeamsDivisionStandingParams, 'divisionTeamPairs'>,
		pairCacheKeys: string[],
	): Promise<Record<string, DivisionStanding | null>> {
		const divisionTeamPairs = pairCacheKeys.map(this._toDivisionTeamPair);
		const divisionStandings = await this.divisionStandingRepository.fetchTeamsDivisionStanding({ ...params, divisionTeamPairs });
		return divisionTeamPairs.reduce<Record<string, DivisionStanding | null>>((acc, teamPair) => {
			const { team_id, division_id } = teamPair;
			const teamPairCacheKey = this._toDivisionTeamPairCacheKey(teamPair);
			acc[teamPairCacheKey] = divisionStandings.find((ds) => ds.team_id === team_id && ds.division_id === division_id) || null;
			return acc;
		}, {});
	}

	private _getTeamsDivisionStandingCacheKeyConfig(params: FetchTeamsDivisionStandingParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.TeamsStandings,
		};
	}

	private _toDivisionTeamPairCacheKey({ team_id, division_id }: { team_id: string; division_id: string }): string {
		// Combine team_id and division_id to create a unique cache key for each team-division pair
		return `${team_id}_${division_id}`;
	}

	private _toDivisionTeamPair(teamPairCacheKey: string): { team_id: string; division_id: string } {
		// Split the team_id and division_id from the cache key
		const [team_id, division_id] = teamPairCacheKey.split('_');
		return { team_id, division_id };
	}
}
