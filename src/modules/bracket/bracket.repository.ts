import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';

import {
	getBracketMatchDataForEventQuery,
	getPoolBracketDataForEventQuery,
	getPoolBracketSiblingsForEventQuery,
} from './queries/bracket.query';

@Injectable()
export class BracketRepository {
	constructor(private prisma: PrismaService) {}

	async getPoolBracketDataForEvent(poolId: string) {
		const response = await this.prisma.$queryRawUnsafe(getPoolBracketDataForEventQuery(), poolId);
		return response[0];
	}
	async getPoolSiblings(division_short_name: string, event_id: number) {
		const response = await this.prisma.$queryRawUnsafe(getPoolBracketSiblingsForEventQuery(), division_short_name, event_id);
		return response;
	}
	async getBracketMatchData(poolId: string) {
		const response = await this.prisma.$queryRawUnsafe(getBracketMatchDataForEventQuery(), poolId);
		return response;
	}
}
