const subqueryRosterTeam = (colName: string, idName: string) =>
	`(SELECT rt.${colName} FROM roster_team rt WHERE rt.roster_team_id = ${idName} LIMIT 1)`;
export const getBracketMatchDataForEventQuery = () => {
	return `
		SELECT m.match_number,
					m.match_id,
					m.source,
					m.finishes,
					rt1.extra->>'show_previously_accepted_bid' show_previously_accepted_bid_team1,
					rt2.extra->>'show_previously_accepted_bid' show_previously_accepted_bid_team2,
					m.team1_roster_id,
					rt1.team_name team1_name,
					rt1.organization_code team1_code,
					m.team2_roster_id,
					rt2.team_name team2_name,
					rt2.organization_code team2_code,
					m.ref_roster_id,
					rt3.team_name ref_name,
					rt3.organization_code ref_code,
					(
								SELECT rank
								FROM division_standing ds
								WHERE ds.team_id = m.team1_roster_id
											AND ds.event_id = m.event_id
											AND ds.division_id = m.division_id
								LIMIT 1
					) team1_rank, (
								SELECT rank
								FROM division_standing ds
								WHERE ds.team_id = m.team2_roster_id
											AND ds.event_id = m.event_id
											AND ds.division_id = m.division_id
								LIMIT 1
					) team2_rank, bm.team1_roster_id team1_temp_roster_id, bm.team2_roster_id team2_temp_roster_id, ${subqueryRosterTeam(
						'team_name',
						'bm.team1_roster_id',
					)} team1_temp_name, ${subqueryRosterTeam(
		'team_name',
		'bm.team2_roster_id',
	)} team2_temp_name, bm.winning_roster_id, ${subqueryRosterTeam(
		'team_name',
		'bm.winning_roster_id',
	)} winning_temp_name, bm.score temp_score, m.display_name, extract(
								epoch
								from m.secs_start
					)::BIGINT * 1000 date_start,
					c.name court_name,
					c.uuid court_id,
					m.results,
					m.footnote_play,
					m.footnote_team1,
					m.footnote_team2
		FROM matches m
					LEFT JOIN bracketmatch bm ON bm.uuid = m.pool_bracket_id
					AND bm.match_number = m.match_number
					LEFT JOIN courts c ON c.uuid = m.court_id
					LEFT JOIN LATERAL (
								SELECT rt.team_name,
											rt.extra,
											rt.organization_code
								FROM roster_team rt
								WHERE rt.roster_team_id = m.team1_roster_id
								LIMIT 1
					) rt1 ON TRUE
					LEFT JOIN LATERAL (
								SELECT rt.team_name,
											rt.extra,
											rt.organization_code
								FROM roster_team rt
								WHERE rt.roster_team_id = m.team2_roster_id
								LIMIT 1
					) rt2 ON TRUE
					LEFT JOIN LATERAL (
								SELECT rt.team_name,
											rt.organization_code
								FROM roster_team rt
								WHERE rt.roster_team_id = m.ref_roster_id
								LIMIT 1
					) rt3 ON TRUE
		WHERE m.pool_bracket_id = $1::UUID
		ORDER BY m.match_number
	`;
};
export const getPoolBracketSiblingsForEventQuery = () => `
	SELECT pb.uuid,
		pb.name,
		pb.is_pool
	FROM poolbrackets pb
		INNER JOIN rounds r ON r.uuid = pb.round_id
	WHERE pb.division_short_name = $1
		AND pb.event_id = $2
	ORDER BY r.sort_priority,
		pb.sort_priority
`;
export const getPoolBracketDataForEventQuery = () => `
	SELECT pb.event_id,
		pb.pb_seeds,
		pb.pb_finishes,
		concat_ws(' ', r.name, rr.name, pb.name) AS display_name,
		pb.name full_name,
		pb.display_name display_name_short,
		d.name division_name,
		bt.m1t1,
		bt.m1t2,
		bt.m1ref,
		bt.m2t1,
		bt.m2t2,
		bt.m2ref,
		bt.m3t1,
		bt.m3t2,
		bt.m3ref,
		bt.m4t1,
		bt.m4t2,
		bt.m4ref,
		bt.m5t1,
		bt.m5t2,
		bt.m5ref,
		bt.m6t1,
		bt.m6t2,
		bt.m6ref,
		bt.m7t1,
		bt.m7t2,
		bt.m7ref,
		bt.m8t1,
		bt.m8t2,
		bt.m8ref,
		bt.m9t1,
		bt.m9t2,
		bt.m9ref,
		bt.m10t1,
		bt.m10t2,
		bt.m10ref,
		bt.m11t1,
		bt.m11t2,
		bt.m11ref,
		bt.m12t1,
		bt.m12t2,
		bt.m12ref,
		bt.m13t1,
		bt.m13t2,
		bt.m13ref,
		bt.m14t1,
		bt.m14t2,
		bt.m14ref,
		bt.m15t1,
		bt.m15t2,
		bt.m15ref,
		bt.m16t1,
		bt.m16t2,
		bt.m16ref,
		bt.m17t1,
		bt.m17t2,
		bt.m17ref,
		bt.m18t1,
		bt.m18t2,
		bt.m18ref,
		bt.m19t1,
		bt.m19t2,
		bt.m19ref,
		bt.m20t1,
		bt.m20t2,
		bt.m20ref,
		bt.m21t1,
		bt.m21t2,
		bt.m21ref,
		bt.m22t1,
		bt.m22t2,
		bt.m22ref,
		bt.uuid,
		bt.win,
		bt."3rd",
		pb.flow_chart,
		pb.team_count,
		pb.sort_priority,
		pb.round_id,
		pb.consolation
	FROM poolbrackets pb
		LEFT JOIN division d ON d.division_id = pb.division_id
		LEFT JOIN bracketteams bt ON bt.uuid = pb.uuid
		LEFT JOIN rounds r ON pb.round_id = r.uuid
		LEFT JOIN rounds rr ON pb.group_id = rr.uuid
	WHERE pb.uuid = $1::UUID
`;
