import { Injectable } from '@nestjs/common';
import { each } from 'lodash';

import { CacheService } from '@/infra/cache/cache.service';

import { BracketRepository } from './bracket.repository';
import { IBracketMatch, IBracketPool, IBracketSourceMatch } from './types/bracket.types';

@Injectable()
export class BracketService {
	constructor(private bracketRepository: BracketRepository, private cacheService: CacheService) {}
	async fetchBracket({ poolId }: { poolId: string }) {
		const cacheResponse = await this.cacheService.getCache('fetchBracket', { poolId });
		if (cacheResponse) {
			return cacheResponse;
		}
		const poolData = await this.bracketRepository.getPoolBracketDataForEvent(poolId);
		if (!poolData) {
			return null;
		}

		const bracket_name = poolData.display_name;
		const division_name = poolData.division_name;

		if (!poolData.flow_chart && poolData.team_count) {
			if (poolData.consolation == 0) {
				poolData.flow_chart = poolData.team_count + 'tnc.html';
			} else if (poolData.consolation == 1) {
				poolData.flow_chart = poolData.team_count + 'tw3.html';
			} else if (poolData.consolation == 2) {
				poolData.flow_chart = poolData.team_count + 'twc.html';
			}
		}

		const seeds = [];
		if (poolData.pb_seeds) {
			try {
				const seedsObj = JSON.parse(poolData.pb_seeds);
				each(seedsObj, function (seed, key) {
					seeds[parseInt(key, 10)] = JSON.parse(seed);
				});
			} catch (e) {
				console.log(e.message);
			}
		}
		const pools = (await this.bracketRepository.getPoolSiblings(poolData.division_short_name, poolData.event_id)) as IBracketPool[];
		pools.forEach(function (p, i, arr) {
			if (p.uuid == poolData.uuid) {
				if (i > 0) {
					poolData.prev = {
						uuid: arr[i - 1].uuid,
						name: arr[i - 1].name,
						is_pool: arr[i - 1].is_pool,
					};
				}
				if (i < arr.length - 1) {
					poolData.next = {
						uuid: arr[i + 1].uuid,
						name: arr[i + 1].name,
						is_pool: arr[i + 1].is_pool,
					};
				}
			}
		});

		const matches = (await this.bracketRepository.getBracketMatchData(poolId)) as IBracketMatch[];
		each(matches, function (match, key) {
			let source = {} as IBracketSourceMatch;
			try {
				source = JSON.parse(match.source as unknown as string);
				match.source = source;
			} catch (e) {
				console.log(e.message);
			}

			const { results } = match;
			let winner = 0;
			let winning_team_name = '';
			let winning_team_id = 0;
			let scores = '';

			if (results && results.winner) {
				winner = results.winner;
				winning_team_name = match['team' + winner + '_name'];
				winning_team_id = match['team' + winner + '_roster_id'];
				if (results['team' + winner] && results['team' + winner].scores) scores = results['team' + winner].scores;
			}
			matches[key].winning_team_name = winning_team_name;
			matches[key].winning_team_id = winning_team_id;
			matches[key].winner = winner;
			matches[key].scores = scores;

			if (matches[key].winning_roster_id) {
				delete matches[key].winning_roster_id;
				delete matches[key].winning_temp_name;
				matches[key].winning_team_name = matches[key].winning_temp_name;
				matches[key].winning_team_id = matches[key].winning_roster_id;
			}

			if (matches[key].temp_score) {
				delete matches[key].temp_score;
				matches[key].scores = matches[key].temp_score;
			}

			let seed, newPool;
			seed = newPool = 0;
			if (source && source['team1'] && source['team1']['type'] && source['team1']['type'] == 5 && source['team1']['seed']) {
				seed = source['team1']['seed'];
				newPool = seeds[seed];
				if (newPool) {
					matches[key].team1_pool_id = newPool.id;
					matches[key].team1_pool_name = newPool.name;
				}
			}

			if (source && source['team2'] && source['team2']['type'] && source['team2']['type'] == 5 && source['team2']['seed']) {
				seed = source['team2']['seed'];
				newPool = seeds[seed];
				if (newPool) {
					matches[key].team2_pool_id = newPool.id;
					matches[key].team2_pool_name = newPool.name;
				}
			}

			if (source && source['ref'] && source['ref']['type'] && source['ref']['type'] == 5 && source['ref']['seed']) {
				seed = source['ref']['seed'];
				newPool = seeds[seed];
				if (newPool) {
					matches[key].team2_pool_id = newPool.id;
					matches[key].team2_pool_name = newPool.name;
				}
			}

			match.team1_name = match.team1_name ? match.team1_name : match.source.team1.name;
			match.team2_name = match.team2_name ? match.team2_name : match.source.team2.name;
		});

		const response = {
			display_name: bracket_name,
			pool: poolData,
			division_name,
			matches: matches.map((match) => ({ ...match, date_start: Number(match.date_start) })),
		};

		this.cacheService.setCache(response, 'fetchBracket', { poolId });
		return response;
	}
}
