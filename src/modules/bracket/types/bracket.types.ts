export interface IBracketPool {
	uuid: string;
	name: string;
	is_pool: boolean;
}
export interface IBracketMatch {
	source: IBracketSourceMatch;
	winning_team_name: string;
	winning_team_id: number;
	winner: number;
	scores: string;
	winning_roster_id: number;
	winning_temp_name: string;
	temp_score: string;
	team1_pool_id: string;
	team1_pool_name: string;
	team2_pool_id: string;
	team2_pool_name: string;
	team1_name: string;
	team2_name: string;
	date_start: number;
	results: {
		set1: string;
		set2: string;
		set3: string;
		set4?: string;
		set5?: string;
		winner: number;
	};
}
export interface IBracketSourceMatch {
	team1: {
		id: string;
		name: string;
		overallSeed: number;
		reseedRank: number;
		type: number;
	};
	team2: {
		id: string;
		name: string;
		overallSeed: number;
		reseedRank: number;
		type: number;
	};
	ref: {
		id: string;
		name: string;
		overallSeed: number;
		reseedRank: number;
		type: number;
	};
}
