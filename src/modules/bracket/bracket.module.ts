import { Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { BracketRepository } from './bracket.repository';
import { BracketService } from './bracket.service';

@Module({
	imports: [PrismaModule, CacheModule],
	providers: [BracketService, BracketRepository],
	exports: [BracketService],
})
export class BracketModule {}
