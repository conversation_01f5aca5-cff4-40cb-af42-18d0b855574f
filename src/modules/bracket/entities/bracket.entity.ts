import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class BracketPool {
	@Field({ nullable: true })
	flow_chart: string;
}

@ObjectType()
export class BracketMatchResultsTeam {
	@Field({ nullable: true })
	sets_won: number;

	@Field({ nullable: true })
	sets_lost: number;

	@Field({ nullable: true })
	points_won: number;

	@Field({ nullable: true })
	points_lost: number;

	@Field({ nullable: true })
	sets_pct: number;

	@Field({ nullable: true })
	matches_lost: number;

	@Field({ nullable: true })
	matches_won: number;

	@Field({ nullable: true })
	matches_pct: number;

	@Field({ nullable: true })
	scores: string;

	@Field({ nullable: true })
	roster_team_id: number;
}

@ObjectType()
export class BracketMatchSourceTeamItem {
	@Field({ nullable: true })
	id: string;

	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	seed: number;

	@Field({ nullable: true })
	type: number;

	@Field({ nullable: true })
	reseedRank: number;

	@Field({ nullable: true })
	overallSeed: number;
}

@ObjectType()
export class BracketMatchSourceTeam {
	@Field(() => BracketMatchSourceTeamItem, { nullable: true })
	ref: BracketMatchSourceTeamItem;

	@Field(() => BracketMatchSourceTeamItem, { nullable: true })
	team1: BracketMatchSourceTeamItem;

	@Field(() => BracketMatchSourceTeamItem, { nullable: true })
	team2: BracketMatchSourceTeamItem;
}

@ObjectType()
export class BracketMatchResults {
	@Field({ nullable: true })
	set1: string;

	@Field({ nullable: true })
	set2: string;

	@Field({ nullable: true })
	set3: string;

	@Field({ nullable: true })
	set4: string;

	@Field({ nullable: true })
	set5: string;

	@Field({ nullable: true })
	winner: string;

	@Field(() => BracketMatchResultsTeam, { nullable: true })
	team1: BracketMatchResultsTeam;

	@Field(() => BracketMatchResultsTeam, { nullable: true })
	team2: BracketMatchResultsTeam;
}

@ObjectType()
export class BracketMatch {
	@Field({ nullable: true })
	show_previously_accepted_bid_team1: string;

	@Field({ nullable: true })
	show_previously_accepted_bid_team2: string;

	@Field({ nullable: true })
	court_id: string;

	@Field({ nullable: true })
	court_name: string;

	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	date_start: number;

	@Field({ nullable: true })
	match_id: string;

	@Field({ nullable: true })
	match_number: number;

	@Field({ nullable: true })
	ref_code: string;

	@Field({ nullable: true })
	ref_name: string;

	@Field({ nullable: true })
	ref_roster_id: number;

	@Field({ nullable: true })
	scores: string;

	@Field({ nullable: true })
	team1_code: string;

	@Field({ nullable: true })
	team1_name: string;

	@Field({ nullable: true })
	team1_rank: number;

	@Field({ nullable: true })
	team1_roster_id: number;

	@Field({ nullable: true })
	team1_temp_name: string;

	@Field({ nullable: true })
	team1_temp_roster_id: string;

	@Field({ nullable: true })
	team2_name: string;

	@Field({ nullable: true })
	team2_rank: number;

	@Field({ nullable: true })
	team2_roster_id: number;

	@Field({ nullable: true })
	team2_temp_name: string;

	@Field({ nullable: true })
	team2_temp_roster_id: number;

	@Field({ nullable: true })
	temp_score: string;

	@Field({ nullable: true })
	winner: string;

	@Field({ nullable: true })
	winning_roster_id: number;

	@Field({ nullable: true })
	winning_team_id: number;

	@Field({ nullable: true })
	winning_team_name: string;

	@Field({ nullable: true })
	winning_temp_name: string;

	@Field(() => BracketMatchResults, { nullable: true })
	results: BracketMatchResults;

	@Field(() => BracketMatchSourceTeam, { nullable: true })
	source: BracketMatchSourceTeam;
}

@ObjectType()
export class EventBracket {
	@Field({ nullable: true })
	display_name: string;

	@Field({ nullable: true })
	division_name: string;

	@Field(() => BracketPool, { nullable: true })
	pool: BracketPool;

	@Field(() => [BracketMatch], { nullable: true })
	matches: BracketMatch[];
}
