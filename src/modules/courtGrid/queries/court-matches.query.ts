export const getDefaultHourQuery = () => `
	SELECT EXTRACT(
		HOUR
		FROM NOW() AT TIME ZONE e.timezone
	) "hour"
	FROM "event" e
	WHERE e.esw_id = $1
	AND to_char((now() AT TIME ZONE e.timezone), 'YYYY-MM-DD') = $2
`;
export const getHoursQuery = () => `
	SELECT DISTINCT to_char(m.secs_start, 'HH24')::INT as time,
		to_char(m.secs_start, 'FMHH12 AM') as time12
	FROM matches m
		INNER JOIN event e ON m.event_id = e.event_id
	WHERE e.esw_id = $1
		AND to_char(m.secs_start, 'YYYY-MM-DD') = $2
	ORDER BY to_char(m.secs_start, 'HH24')::INT
`;
export const getDivisionsQuery = () => `
	SELECT DISTINCT d.division_id,
		d.name division_name,
		d.gender,
		d.sort_order,
		d.max_age,
		d.level_sort_order,
		d.level
	FROM matches m
		INNER JOIN event e ON m.event_id = e.event_id
		LEFT JOIN division d ON d.division_id = m.division_id
	WHERE e.esw_id = $1
	ORDER BY d.sort_order,
		d.gender,
		d.max_age DESC,
		d.level_sort_order,
		d.level
`;
export const getCourtsQuery = (filterDataByDivision: boolean) => `
	SELECT mt.event_id,
		mt.court_id,
		c.name court_name,
		c.short_name,
		(
			SELECT array_to_json(array_agg(row_to_json(ctmatches)))
			FROM (
					SELECT m.match_id,
						m.display_name match_name,
						m.source,
						m.results,
						pb.pb_seeds,
						d.division_id,
						d.name division_name,
						d.short_name division_short_name,
						extract(
							epoch
							from m.secs_start
						)::BIGINT * 1000 date_start,
						extract(
							epoch
							from m.secs_end
						)::BIGINT * 1000 date_end,
						extract(
							epoch
							from m.secs_finished
						)::BIGINT * 1000 secs_finished,
						m.team1_roster_id,
						m.team2_roster_id,
						rt1.team_name team_1_name,
						rt2.team_name team_2_name,
						rtr.team_name team_ref_name,
						d.color
					FROM matches m
						LEFT JOIN poolbrackets pb ON pb.uuid = m.pool_bracket_id
						LEFT JOIN division d ON d.division_id = m.division_id
						LEFT JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id
						LEFT JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id
						LEFT JOIN roster_team rtr ON rtr.roster_team_id = m.ref_roster_id
					WHERE m.event_id = mt.event_id
						AND m.court_id = mt.court_id ${filterDataByDivision ? 'AND m.division_id = $5' : ''}
						AND m.secs_start >= $2::TIMESTAMP + ($3::INTEGER || ' hour')::INTERVAL
						AND m.secs_start < $2::TIMESTAMP + (($3::INTEGER + $4::INTEGER) || ' hour')::INTERVAL
					ORDER BY m.secs_start
				) AS ctmatches
		) AS matches
	FROM matches mt
		INNER JOIN "event" e ON e.event_id = mt.event_id
		LEFT JOIN courts c ON mt.court_id = c.uuid
	WHERE e.esw_id = $1
		AND mt.secs_start >= $2::TIMESTAMP + ($3::INTEGER || ' hour')::INTERVAL
		AND mt.secs_start < $2::TIMESTAMP + (($3::INTEGER + $4::INTEGER) || ' hour')::INTERVAL
	GROUP BY mt.event_id,
		mt.court_id,
		c.name,
		c.sort_priority,
		c.short_name
	ORDER BY c.sort_priority
`;
