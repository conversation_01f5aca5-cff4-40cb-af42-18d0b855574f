import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class CourtMatchesCourtMatchResultsTeam {
	@Field({ nullable: true })
	scores: string;
}

@ObjectType()
export class CourtMatchesCourtMatchResults {
	@Field({ nullable: true })
	set1: string;

	@Field({ nullable: true })
	set2: string;

	@Field(() => CourtMatchesCourtMatchResultsTeam, { nullable: true })
	team1: CourtMatchesCourtMatchResultsTeam;

	@Field(() => CourtMatchesCourtMatchResultsTeam, { nullable: true })
	team2: CourtMatchesCourtMatchResultsTeam;

	@Field({ nullable: true })
	winner: string;
}

@ObjectType()
export class CourtMatchesCourtMatch {
	@Field({ nullable: true })
	match_id: string;

	@Field({ nullable: true })
	match_name: string;

	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	division_name: string;

	@Field({ nullable: true })
	division_short_name: string;

	@Field({ nullable: true })
	date_start: number;

	@Field({ nullable: true })
	date_end: number;

	@Field({ nullable: true })
	secs_finished: number;

	@Field({ nullable: true })
	team1_roster_id: number;

	@Field({ nullable: true })
	team2_roster_id: number;

	@Field({ nullable: true })
	team_1_name: string;

	@Field({ nullable: true })
	team_2_name: string;

	@Field({ nullable: true })
	team_ref_name: string;

	@Field({ nullable: true })
	color: string;

	@Field(() => CourtMatchesCourtMatchResults, { nullable: true })
	results: CourtMatchesCourtMatchResults;
}

@ObjectType()
export class CourtMatchesCourt {
	@Field({ nullable: true })
	court_id: string;

	@Field({ nullable: true })
	court_name: string;

	@Field({ nullable: true })
	event_id: number;

	@Field({ nullable: true })
	short_name: string;

	@Field(() => [CourtMatchesCourtMatch], { nullable: true })
	matches: CourtMatchesCourtMatch[];
}

@ObjectType()
export class CourtMatchesTime {
	@Field({ nullable: true })
	time?: number;

	@Field({ nullable: true })
	time12?: string;

	@Field({ nullable: true })
	default?: string;
}

@ObjectType()
export class CourtMatchesDivision {
	@Field({ nullable: true })
	division_id: number;

	@Field({ nullable: true })
	division_name: string;

	@Field({ nullable: true })
	gender: string;

	@Field({ nullable: true })
	sort_order: number;

	@Field({ nullable: true })
	max_age: number;

	@Field({ nullable: true })
	level_sort_order: number;

	@Field({ nullable: true })
	level: string;
}

@ObjectType()
export class CourtGrid {
	@Field(() => [CourtMatchesCourt], { nullable: true })
	courts: CourtMatchesCourt[];

	@Field(() => [CourtMatchesTime], { nullable: true })
	hours: CourtMatchesTime[];

	@Field(() => [CourtMatchesDivision], { nullable: true })
	divisions: CourtMatchesDivision[];
}
