import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';

import { getCourtsQuery, getDefaultHourQuery, getDivisionsQuery, getHoursQuery } from './queries/court-matches.query';
import { IDefaultHour, IDivision, IGetCourtsRepository, IHours } from './types';

@Injectable()
export class CourtGridRepository {
	constructor(private prisma: PrismaService) {}

	async getDefaultHour(eventId: string, day: string) {
		const response = await this.prisma.$queryRawUnsafe<IDefaultHour[]>(getDefaultHourQuery(), eventId, day);
		return response[0];
	}
	async getHours(eventId: string, day: string) {
		const response = await this.prisma.$queryRawUnsafe<IHours[]>(getHoursQuery(), eventId, day);
		return response;
	}
	async getDivisions(eventId: string) {
		const response = await this.prisma.$queryRawUnsafe<IDivision[]>(getDivisionsQuery(), eventId);
		return response;
	}
	async getCourts({ eventId, day, hour, hoursCount, division_id = 0, filterDataByDivision }: IGetCourtsRepository) {
		const response = await this.prisma.$queryRawUnsafe(getCourtsQuery(filterDataByDivision), eventId, day, hour, hoursCount, division_id);
		return response;
	}
}
