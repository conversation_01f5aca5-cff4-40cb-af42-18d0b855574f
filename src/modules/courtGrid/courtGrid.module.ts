import { Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { CourtGridRepository } from './courtGrid.repository';
import { CourtGridService } from './courtGrid.service';

@Module({
	imports: [PrismaModule, CacheModule],
	providers: [CourtGridService, CourtGridRepository],
	exports: [CourtGridService],
})
export class CourtGridModule {}
