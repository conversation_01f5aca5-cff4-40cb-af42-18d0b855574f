import { BadRequestException, Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import * as moment from 'moment';

import { CacheService } from '@/infra/cache/cache.service';

import { CourtGridRepository } from './courtGrid.repository';
import {
	CourtsResult,
	IDivision,
	IFetchCourtGrid,
	IGetCourtsParams,
	IGetCourtsRepository,
	IGetDefaultHour,
	IGetHours,
	IHours,
} from './types';

@Injectable()
export class CourtGridService {
	constructor(private courtGridRepository: CourtGridRepository, private cacheService: CacheService) {}

	private async getDefaultHour({ eventId, day }: IGetDefaultHour) {
		return this.courtGridRepository.getDefaultHour(eventId, day);
	}

	private async getHours({ defaultHour, eventId, day, hour }: IGetHours) {
		const result = await this.courtGridRepository.getHours(eventId, day);

		if (hour) {
			result.push({
				default: hour,
			});

			return result;
		}

		if (_.isUndefined(defaultHour)) {
			const row = _.first(result);

			hour = row.time;

			result.push({
				default: hour,
			});
		} else {
			if (Number(hour) === 0) {
				hour = defaultHour.hour;
			}
			result.push({
				default: defaultHour.hour,
			});
		}

		return result;
	}
	private async getDivisions(eventId: string) {
		return this.courtGridRepository.getDivisions(eventId);
	}
	private formatResults(results: CourtsResult[]) {
		results.forEach(function (res) {
			if (res.matches && res.matches.length) {
				res.matches.forEach(function (match) {
					if (match.results) {
						const results = match.results;

						if (results && results.winner) {
							match.res_team1_roster_id = results.team1.roster_team_id;
							match.res_team2_roster_id = results.team2.roster_team_id;
							match.res_winner = results.winner;

							if (match.res_winner == 1) {
								match.scores = results.team1.scores;
							} else if (match.res_winner == 2) {
								match.scores = results.team2.scores;
							}
						}
					}

					if (match.source && match.pb_seeds) {
						const seeds = [];
						try {
							const seedsObj = JSON.parse(match.pb_seeds);
							_.each(seedsObj, function (seed, key) {
								seeds[parseInt(key, 10)] = JSON.parse(seed);
							});
						} catch (e) {}

						const source = JSON.parse(match.source);

						let seed, newPool;
						seed = newPool = 0;
						if (source && source['team1'] && source['team1']['type'] && source['team1']['type'] == 5 && source['team1']['seed']) {
							seed = source['team1']['seed'];
							newPool = seeds[seed];
							if (newPool) {
								match.team1_pool_name = newPool.name;
								match.team_1_name = match.team_1_name || match.team1_pool_name || source.team1.name;
							}
						}

						if (!match.team_1_name) {
							match.team_1_name = source.team1.name;
						}

						if (source && source['team2'] && source['team2']['type'] && source['team2']['type'] == 5 && source['team2']['seed']) {
							seed = source['team2']['seed'];
							newPool = seeds[seed];
							if (newPool) {
								match.team2_pool_name = newPool.name;
								match.team_2_name = match.team_2_name || match.team2_pool_name || source.team2.name;
							}
						}

						if (!match.team_2_name) {
							match.team_2_name = source.team2.name;
						}

						if (source && source['ref'] && source['ref']['type'] && source['ref']['type'] == 5 && source['ref']['seed']) {
							seed = source['ref']['seed'];
							newPool = seeds[seed];
							if (newPool) {
								match.ref_pool_name = newPool.name;
								match.team_ref_name = match.team_ref_name || match.ref_pool_name || source.ref.name;
							}
						}

						if (!match.team_ref_name) {
							match.team_ref_name = source.ref.name;
						}

						delete match.team1_pool_name;
						delete match.team2_pool_name;
						delete match.ref_pool_name;
						delete match.source;
						delete match.pb_seeds;
					}
				});
			}
		});
	}
	private async getCourts({ hours, hour, hoursCount, filterDataByDivision, division_id, eventId, day }: IGetCourtsParams) {
		const DEFAULT_COUNT_OF_HOURS = 3;
		const setDefaultHour = (hour: number, hours: IHours[]) => {
			if (hour === 0) {
				return hours[0]?.time || 0;
			}
			return hour;
		};

		const setDefaultHoursCount = (hoursCount: number, defaultCount: number) => {
			return hoursCount || defaultCount;
		};

		const buildCourtsParams = (
			eventId: string,
			day: string,
			hour: number,
			hoursCount: number,
			filterDataByDivision: boolean,
			division_id?: number,
		): IGetCourtsRepository => {
			const params: IGetCourtsRepository = {
				eventId,
				day,
				hour,
				hoursCount,
				filterDataByDivision,
			};

			if (filterDataByDivision) {
				params.division_id = division_id;
			}

			return params;
		};

		hour = setDefaultHour(parseInt(hour), hours).toString();
		hoursCount = setDefaultHoursCount(hoursCount, DEFAULT_COUNT_OF_HOURS);

		const params = buildCourtsParams(eventId, day, parseInt(hour), hoursCount, filterDataByDivision, division_id);

		const result = (await this.courtGridRepository.getCourts(params)) as CourtsResult[];
		this.formatResults(result);
		return result;
	}

	async fetchCourtGrid({ eventId, day, hour, hours, division }: IFetchCourtGrid) {
		const cacheResponse = await this.cacheService.getCache('fetchCourtGrid', { eventId, day, hour, hours, division });
		if (cacheResponse) {
			return cacheResponse;
		}
		const hoursCount = parseInt(hours, 10);
		const division_id = Number(division) || null;
		const filterDataByDivision = !_.isNaN(division_id) && division_id > 0;

		if (!moment(day, 'YYYY-MM-DD', true).isValid()) {
			throw new BadRequestException('Invalid Event day passed');
		}

		if (!hoursCount) {
			throw new BadRequestException('Invalid Duration passed');
		}

		const defaultHour = await this.getDefaultHour({ eventId, day });

		let listHours: IHours[];
		let divisions: IDivision[];

		if (day) {
			listHours = await this.getHours({ defaultHour, eventId, day, hour });
			divisions = await this.getDivisions(eventId);
		}

		const courts = await this.getCourts({ hours: listHours, hour, hoursCount, filterDataByDivision, division_id, eventId, day });

		const response = {
			courts,
			hours: listHours,
			divisions,
		};

		this.cacheService.setCache(response, 'fetchCourtGrid', { eventId, day, hour, hours, division });

		return response;
	}
}
