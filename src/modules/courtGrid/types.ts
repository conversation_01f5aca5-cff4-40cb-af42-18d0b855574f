export interface IDefaultHour {
	hour: number | string;
}
export interface IHours {
	time?: number;
	key?: string;
	default?: string | number;
}
export interface IDivision {
	division_id: number;
	division_name: string;
	gender: string;
	sort_order: number;
	max_age: number;
	level_sort_order: number | null;
	level: string;
}
export interface IGetDefaultHour {
	eventId: string;
	day: string;
}
export interface IGetHours {
	defaultHour: IDefaultHour;
	eventId: string;
	day: string;
	hour: string | number;
}
export interface IFetchCourtGrid {
	eventId: string;
	day: string;
	hour: string;
	hours: string;
	division: string;
}
export interface IGetCourtsRepository {
	eventId: string;
	day: string;
	hour: number;
	hoursCount: number;
	filterDataByDivision: boolean;
	division_id?: number;
}
export interface IGetCourtsParams {
	hours: IHours[];
	hour: string;
	hoursCount: number;
	filterDataByDivision: boolean;
	division_id?: number;
	eventId: string;
	day: string;
}

interface Match {
	match_id: string;
	res_team1_roster_id?: number;
	res_team2_roster_id?: number;
	res_winner?: number;
	scores?: string;
	source?: string;
	pb_seeds?: string;
	team1_pool_name?: string;
	team2_pool_name?: string;
	team_1_name?: string;
	team_2_name?: string;
	team_ref_name?: string;
	division_id: number;
	division_name: string;
	division_short_name: string;
	date_start: number;
	date_end: number;
	secs_finished: null;
	team1_roster_id: number;
	team2_roster_id: number;
	color: null;
	results?: {
		team1: {
			roster_team_id: number;
			scores: string;
		};
		team2: {
			roster_team_id: number;
			scores: string;
		};
		winner: number;
	};
	ref_pool_name?: string;
}

export interface CourtsResult {
	matches: Match[];
}
