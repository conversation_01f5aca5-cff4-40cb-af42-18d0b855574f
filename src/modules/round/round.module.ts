import { Modu<PERSON> } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';
import { PoolBracketModule } from '@/modules/poolBracket/pool-bracket.module';

import { RoundsPoolBracketsLoader } from './loaders/rounds-pool-brackets.loader';
import { RoundRepository } from './round.repository';
import { RoundResolver } from './round.resolver';
import { RoundService } from './round.service';

@Module({
	imports: [PrismaModule, PoolBracketModule, CacheModule],
	providers: [RoundResolver, RoundService, RoundRepository, RoundsPoolBracketsLoader],
	exports: [RoundService],
})
export class RoundModule {}
