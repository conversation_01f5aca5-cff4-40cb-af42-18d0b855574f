import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { PoolOrBracket } from '@/modules/poolBracket/entities';
import { PoolBracketService } from '@/modules/poolBracket/pool-bracket.service';
import { Round } from '@/modules/round/entities';

@Injectable()
export class RoundsPoolBracketsLoader {
	constructor(private readonly poolBracketService: PoolBracketService) {}

	create(eventId: string): DataLoader<Round, (typeof PoolOrBracket)[]> {
		return new DataLoader<Round, (typeof PoolOrBracket)[]>(
			async (rounds: Round[]) => {
				const roundsIds = rounds.map((round) => round.uuid);
				const roundsPoolBracketsMap = await this.poolBracketService.fetchRoundsPoolBracketsMap({ eventId, roundsIds });
				return roundsIds.map((roundId) => roundsPoolBracketsMap[roundId]);
			},
			{ cache: false, name: 'RoundsPoolBracketsLoader' },
		);
	}
}
