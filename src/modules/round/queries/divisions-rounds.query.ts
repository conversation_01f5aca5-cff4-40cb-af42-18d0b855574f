import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type DivisionsRoundsQueryParams = {
	eventKey: EventKey;
	divisionsIds: string[];
};

export const getDivisionsRoundsQuery = ({ eventKey, divisionsIds }: DivisionsRoundsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const divisionsIdsValues = divisionsIds.map((id) => sqlVars.useValue(Number(id)));

	const query = `
		SELECT
			r.uuid,
			r.division_id,
			r.sort_priority,
			r.name,
			r.short_name,
			e.event_id,
			MIN(extract(epoch FROM m.secs_start)::BIGINT) * 1000 AS first_match_start,
			MAX(extract(epoch FROM m.secs_start)::BIGINT) * 1000 AS last_match_start
		FROM event e
		JOIN rounds r 
			ON r.event_id = e.event_id
		JOIN division d
			ON d.event_id = e.event_id
			AND d.division_id = r.division_id
			AND d.published IS TRUE
		JOIN poolbrackets pb 
			ON pb.round_id = r.uuid 
			AND pb.event_id = e.event_id
		JOIN matches m
			ON m.pool_bracket_id = pb.uuid
			AND m.division_id = r.division_id
			AND m.event_id = e.event_id
		LEFT JOIN rounds rr 
			ON rr.uuid = pb.group_id 
			AND rr.event_id = e.event_id
		WHERE 
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND r.division_id IN (${divisionsIdsValues.join(',')})
		GROUP BY 
			r.uuid, r.division_id, r.sort_priority, r.name, r.short_name, e.event_id
		ORDER BY
			r.division_id, r.sort_priority
	`;

	return [query, sqlVars.getValues()];
};
