import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToString, castFieldsToNumber, StringCastKeys, NumberCastKeys } from '@/shared/utils/format';

import { Round } from './entities';
import { getDivisionsRoundsQuery, DivisionsRoundsQueryParams } from './queries/divisions-rounds.query';

export type FetchDivisionsRoundsParams = DivisionsRoundsQueryParams;

const ROUND_STRING_CAST_FIELDS: StringCastKeys<Round>[] = ['event_id', 'division_id'];
const ROUND_NUMBER_CAST_FIELDS: NumberCastKeys<Round>[] = ['first_match_start', 'last_match_start'];

@Injectable()
export class RoundRepository {
	constructor(private prisma: PrismaService) {}

	async fetchDivisionsRounds(params: FetchDivisionsRoundsParams): Promise<Round[]> {
		const [query, values] = getDivisionsRoundsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Round[]>(query, ...values);
		castFieldsToString(items, ROUND_STRING_CAST_FIELDS);
		castFieldsToNumber(items, ROUND_NUMBER_CAST_FIELDS);
		return items;
	}
}
