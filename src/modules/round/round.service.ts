import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheScopes, CacheCategories } from '@/shared/constants/cache';
import { groupBy } from '@/shared/utils/collection';
import { completeObject } from '@/shared/utils/object';

import { Round } from './entities';
import { FetchDivisionsRoundsParams, RoundRepository } from './round.repository';

type FetchDivisionRoundsParams = Omit<FetchDivisionsRoundsParams, 'divisionsIds'> & {
	divisionId: string;
};

@Injectable()
export class RoundService {
	constructor(private roundRepository: RoundRepository, private cacheService: ExtendedCacheService) {}

	async fetchDivisionRounds(params: FetchDivisionRoundsParams): Promise<Round[]> {
		const { divisionId, ...rest } = params;
		const divisionRoundsMap = await this.fetchDivisionsRoundsMap({ ...rest, divisionsIds: [divisionId] });
		return divisionRoundsMap[divisionId];
	}

	fetchDivisionsRoundsMap(params: FetchDivisionsRoundsParams): Promise<Record<string, Round[]>> {
		const cacheKeyConfig = this._getDivisionsRoundsCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Round[]>(cacheKeyConfig, params.divisionsIds, (missingIds) =>
			this._getDivisionsRoundsData({
				...params,
				divisionsIds: missingIds,
			}),
		);
	}

	private async _getDivisionsRoundsData(params: FetchDivisionsRoundsParams): Promise<Record<string, Round[]>> {
		const divisionsRounds = await this.roundRepository.fetchDivisionsRounds(params);
		const divisionsRoundsMap = groupBy(divisionsRounds, 'division_id');
		return completeObject(params.divisionsIds, divisionsRoundsMap, []);
	}

	private _getDivisionsRoundsCacheKeyConfig(params: FetchDivisionsRoundsParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.DivisionRounds,
		};
	}
}
