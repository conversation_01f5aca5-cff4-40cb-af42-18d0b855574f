import { Field, ObjectType, ID } from '@nestjs/graphql';

@ObjectType()
export class Round {
	event_id: string;

	@Field(() => ID)
	uuid: string;

	@Field(() => ID)
	division_id: string;

	@Field()
	sort_priority: number;

	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	short_name: string;

	@Field({ nullable: true })
	first_match_start: number;

	@Field({ nullable: true })
	last_match_start: number;
}
