import { Args, Context, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';

import { PoolOrBracket } from '@/modules/poolBracket/entities';
import { EventKey } from '@/shared/utils/event';

import { DivisionRoundsInputDto } from './dto/division-rounds.dto';
import { Round } from './entities';
import { RoundsPoolBracketsLoader } from './loaders/rounds-pool-brackets.loader';
import { RoundService } from './round.service';

type RoundResolverContext = {
	roundsPoolBracketsLoader: ReturnType<RoundsPoolBracketsLoader['create']>;
};

@Resolver(() => Round)
export class RoundResolver {
	constructor(private readonly roundService: RoundService, private readonly roundsPoolBracketsLoader: RoundsPoolBracketsLoader) {}

	@Query(() => [Round])
	divisionRounds(@Args() args: DivisionRoundsInputDto): Promise<Round[]> {
		return this.roundService.fetchDivisionRounds({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@ResolveField(() => [PoolOrBracket], { name: 'pool_brackets' })
	poolBrackets(@Parent() round: Round, @Context() context: RoundResolverContext): Promise<(typeof PoolOrBracket)[]> {
		if (!context.roundsPoolBracketsLoader) {
			context.roundsPoolBracketsLoader = this.roundsPoolBracketsLoader.create(round.event_id);
		}
		return context.roundsPoolBracketsLoader.load(round);
	}
}
