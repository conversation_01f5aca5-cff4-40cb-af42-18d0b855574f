import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheScopes, CacheCategories, CacheCommonKeys } from '@/shared/constants/cache';

import { FetchDivisionsParams, DivisionRepository } from './division.repository';
import { Division } from './entities';

@Injectable()
export class DivisionService {
	constructor(private divisionRepository: DivisionRepository, private cacheService: ExtendedCacheService) {}

	fetchDivisions(params: FetchDivisionsParams): Promise<Division[]> {
		const cacheKeyConfig = this._getDivisionsCacheKeyConfig(params);
		return this.cacheService.getOrFetch<Division[]>(cacheKeyConfig, () => this.divisionRepository.fetchDivisions(params));
	}

	private _getDivisionsCacheKeyConfig(params: FetchDivisionsParams): ItemCacheKeyConfig {
		const { eventKey } = params;
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.Division,
			categoryKey: CacheCommonKeys.All,
		};
	}
}
