import { ArgsType, Field } from '@nestjs/graphql';
import { ArrayMaxSize, IsEnum, IsOptional } from 'class-validator';

import { EVENT_MEDIA_TYPES } from '@/shared/constants/media';

const MAX_FILE_TYPES = 5;

@ArgsType()
export class DivisionMediaOptionsInputDto {
	@Field(() => [String], { nullable: true })
	@IsOptional()
	@ArrayMaxSize(MAX_FILE_TYPES, { message: `"filterFileTypes" must contain at most ${MAX_FILE_TYPES} file types` })
	@IsEnum(EVENT_MEDIA_TYPES, {
		each: true,
		message: `"filterFileTypes" must contain one of the following values: ${Object.values(EVENT_MEDIA_TYPES).join(', ')}`,
	})
	filterFileTypes?: EVENT_MEDIA_TYPES[];
}
