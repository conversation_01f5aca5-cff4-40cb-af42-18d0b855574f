import { Module } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';
import { EventMediaModule } from '@/modules/eventMedia/event-media.module';
import { MatchModule } from '@/modules/match/match.module';
import { RoundModule } from '@/modules/round/round.module';
import { TeamModule } from '@/modules/team/team.module';

import { DivisionRepository } from './division.repository';
import { DivisionResolver } from './division.resolver';
import { DivisionService } from './division.service';
import { EventMediaLoader, MatchesTimeRangesLoader, QualifiedTeamsLoader, RoundsLoader } from './loaders';

@Module({
	imports: [PrismaModule, CacheModule, TeamModule, MatchModule, EventMediaModule, RoundModule],
	providers: [
		DivisionResolver,
		DivisionService,
		DivisionRepository,
		QualifiedTeamsLoader,
		MatchesTimeRangesLoader,
		EventMediaLoader,
		RoundsLoader,
	],
	exports: [DivisionService],
})
export class DivisionModule {}
