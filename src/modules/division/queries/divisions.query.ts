import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type DivisionsQueryParams = {
	eventKey: EventKey;
};

export const getDivisionsQuery = ({ eventKey }: DivisionsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	const query = `
		SELECT 
			e.event_id,
			d.division_id,
			d.name,
			d.short_name,
			d.has_flow_chart,
			COUNT(rt.roster_team_id) as teams_count
		FROM event e
		JOIN division d
			ON d.event_id = e.event_id
			AND d.published is TRUE
		JOIN roster_team rt
			ON rt.division_id = d.division_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted is NULL
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted is NULL
		GROUP BY
			e.event_id, d.division_id
		ORDER BY
			d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level
	`;

	return [query, sqlVars.getValues()];
};
