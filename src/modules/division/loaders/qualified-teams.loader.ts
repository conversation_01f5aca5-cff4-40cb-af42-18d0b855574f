import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Division } from '@/modules/division/entities';
import { Team } from '@/modules/team/entities';
import { TeamService } from '@/modules/team/team.service';
import { EventKey } from '@/shared/utils/event';

@Injectable()
export class QualifiedTeamsLoader {
	constructor(private teamService: TeamService) {}

	create(eventId: string): DataLoader<Division, Team[]> {
		return new DataLoader<Division, Team[]>(
			async (divisions: Division[]) => {
				const teams = await this.teamService.fetchQualifiedTeams({ eventKey: EventKey.fromKeyValue(eventId) });
				return divisions.map(({ division_id }) => teams.filter((team) => team.division_id === division_id));
			},
			{ cache: false, name: 'QualifiedTeamsLoader' },
		);
	}
}
