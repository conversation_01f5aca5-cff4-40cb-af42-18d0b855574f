import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Division } from '@/modules/division/entities';
import { MatchesTimeRange } from '@/modules/match/entities';
import { MatchService } from '@/modules/match/match.service';

@Injectable()
export class MatchesTimeRangesLoader {
	constructor(private matchService: MatchService) {}

	create(eventId: string): DataLoader<Division, MatchesTimeRange[]> {
		return new DataLoader<Division, MatchesTimeRange[]>(
			async (divisions: Division[]) => {
				const divisionsIds = divisions.map(({ division_id }) => division_id);
				const divisionsMatchesTimeRangesMap = await this.matchService.fetchDivisionsMatchesTimeRangesMap({ eventId, divisionsIds });
				return divisions.map(({ division_id }) => divisionsMatchesTimeRangesMap[division_id]);
			},
			{ cache: false, name: 'MatchesTimeRangesLoader' },
		);
	}
}
