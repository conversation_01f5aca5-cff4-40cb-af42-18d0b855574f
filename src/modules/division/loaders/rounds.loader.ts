import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Round } from '@/modules/round/entities';
import { RoundService } from '@/modules/round/round.service';
import { EventKey } from '@/shared/utils/event';

import { Division } from '../entities';

@Injectable()
export class RoundsLoader {
	constructor(private roundService: RoundService) {}

	create(eventKey: EventKey): DataLoader<Division, Round[]> {
		return new DataLoader<Division, Round[]>(
			async (divisions: Division[]) => {
				const divisionsIds = divisions.map((division) => division.division_id);
				const divisionsRoundsMap = await this.roundService.fetchDivisionsRoundsMap({ eventKey, divisionsIds });
				return divisionsIds.map((divisionId) => divisionsRoundsMap[divisionId]);
			},
			{ cache: false, name: 'RoundsLoader' },
		);
	}
}
