import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { EventMedia } from '@/modules/eventMedia/entities';
import { EventMediaService } from '@/modules/eventMedia/event-media.service';

import { Division } from '../entities';

@Injectable()
export class EventMediaLoader {
	constructor(private eventMediaService: EventMediaService) {}

	create(eventId: string): DataLoader<Division, EventMedia[]> {
		return new DataLoader<Division, EventMedia[]>(
			async (divisions: Division[]) => {
				const divisionsIds = divisions.map((division) => division.division_id);
				const divisionsEventMediaMap = await this.eventMediaService.fetchDivisionsEventMediaMap({ eventId, divisionsIds });
				return divisionsIds.map((divisionId) => divisionsEventMediaMap[divisionId]);
			},
			{ cache: false, name: 'EventMediaLoader' },
		);
	}
}
