import { Args, Context, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';

import { EventMedia } from '@/modules/eventMedia/entities';
import { MatchesTimeRange } from '@/modules/match/entities';
import { Round } from '@/modules/round/entities';
import { Team } from '@/modules/team/entities';
import { EventKey } from '@/shared/utils/event';

import { DivisionService } from './division.service';
import { DivisionMediaOptionsInputDto } from './dto/division-media-options.dto';
import { DivisionsInputDto } from './dto/divisions.dto';
import { Division } from './entities';
import { EventMediaLoader, MatchesTimeRangesLoader, QualifiedTeamsLoader, RoundsLoader } from './loaders';

type DivisionResolverContext = {
	qualifiedTeamsLoader: ReturnType<QualifiedTeamsLoader['create']>;
	matchesTimeRangesLoader: ReturnType<MatchesTimeRangesLoader['create']>;
	eventMediaLoader: ReturnType<EventMediaLoader['create']>;
	roundsLoader: ReturnType<RoundsLoader['create']>;
};

@Resolver(() => Division)
export class DivisionResolver {
	constructor(
		private divisionService: DivisionService,
		private qualifiedTeamsLoader: QualifiedTeamsLoader,
		private matchesTimeRangesLoader: MatchesTimeRangesLoader,
		private eventMediaLoader: EventMediaLoader,
		private roundsLoader: RoundsLoader,
	) {}

	@Query(() => [Division])
	divisions(@Args() args: DivisionsInputDto): Promise<Division[]> {
		return this.divisionService.fetchDivisions({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@ResolveField(() => [Team], { name: 'qualified_teams' })
	qualifiedTeams(@Parent() division: Division, @Context() context: DivisionResolverContext): Promise<Team[]> {
		if (!context.qualifiedTeamsLoader) {
			context.qualifiedTeamsLoader = this.qualifiedTeamsLoader.create(division.event_id);
		}
		return context.qualifiedTeamsLoader.load(division);
	}

	@ResolveField(() => [MatchesTimeRange], { name: 'matches_time_ranges' })
	matchesTimeRanges(@Parent() division: Division, @Context() context: DivisionResolverContext): Promise<MatchesTimeRange[]> {
		if (!context.matchesTimeRangesLoader) {
			context.matchesTimeRangesLoader = this.matchesTimeRangesLoader.create(division.event_id);
		}
		return context.matchesTimeRangesLoader.load(division);
	}

	@ResolveField(() => [EventMedia])
	async media(
		@Parent() division: Division,
		@Context() context: DivisionResolverContext,
		@Args() { filterFileTypes }: DivisionMediaOptionsInputDto,
	): Promise<EventMedia[]> {
		if (!context.eventMediaLoader) {
			context.eventMediaLoader = this.eventMediaLoader.create(division.event_id);
		}
		const eventMedia = await context.eventMediaLoader.load(division);
		if (filterFileTypes?.length) {
			return eventMedia.filter((media) => filterFileTypes.includes(media.file_type));
		}
		return eventMedia;
	}

	@ResolveField(() => [Round])
	async rounds(@Parent() division: Division, @Context() context: DivisionResolverContext): Promise<Round[]> {
		if (!context.roundsLoader) {
			context.roundsLoader = this.roundsLoader.create(EventKey.fromKeyValue(division.event_id));
		}
		return context.roundsLoader.load(division);
	}
}
