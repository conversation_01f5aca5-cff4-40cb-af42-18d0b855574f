import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToNumber, castFieldsToString, NumberCastKeys, StringCastKeys } from '@/shared/utils/format';

import { Division } from './entities';
import { getDivisionsQuery, DivisionsQueryParams } from './queries/divisions.query';

export type FetchDivisionsParams = DivisionsQueryParams;

const DIVISION_NUMBER_CAST_FIELDS: NumberCastKeys<Division>[] = ['teams_count'];
const DIVISION_STRING_CAST_FIELDS: StringCastKeys<Division>[] = ['event_id', 'division_id'];

@Injectable()
export class DivisionRepository {
	constructor(private prisma: PrismaService) {}

	async fetchDivisions(params: FetchDivisionsParams): Promise<Division[]> {
		const [query, values] = getDivisionsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Division[]>(query, ...values);
		castFieldsToNumber(items, DIVISION_NUMBER_CAST_FIELDS);
		castFieldsToString(items, DIVISION_STRING_CAST_FIELDS);
		return items;
	}
}
