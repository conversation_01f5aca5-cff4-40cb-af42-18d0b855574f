import { ENTRY_STATUSES } from '@/shared/constants/team';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type PoolBracketsTeamsQueryParams = {
	eventId: string;
	poolBracketsIds: string[];
};

export const getPoolBracketsTeamsQuery = ({ eventId, poolBracketsIds }: PoolBracketsTeamsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const poolBracketsIdsValues = poolBracketsIds.map((id) => sqlVars.useValue(id));

	const query = `
		SELECT DISTINCT
			rt.master_team_id,
			rt.roster_team_id AS team_id,
			rt.roster_club_id AS club_id,
			rt.organization_code AS team_code,
			rt.team_name,
			rt.extra,
			rt.event_id,
			rt.manual_club_name,
			d.division_id,
			d.short_name AS division_name,
			rc.club_name,
			rc.state,
			rc.code AS club_code,
			m.pool_bracket_id
		FROM matches m  
		JOIN roster_team rt
			ON rt.division_id = m.division_id
			AND rt.event_id = m.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted is NULL
			AND rt.roster_team_id IN (m.team1_roster_id, m.team2_roster_id)
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = m.event_id
			AND d.published is TRUE
		JOIN roster_club rc
			ON rc.roster_club_id = rt.roster_club_id
			AND rc.event_id = m.event_id
			AND rc.deleted IS NULL
		WHERE
			m.event_id = ${sqlVars.useValue(Number(eventId))}
			AND m.pool_bracket_id in (${poolBracketsIdsValues.map((id) => `${id}::uuid`).join(',')})
	`;

	return [query, sqlVars.getValues()];
};
