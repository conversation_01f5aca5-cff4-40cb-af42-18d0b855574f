import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

type TeamsSearchIndexEntriesQueryParams = {
	eventKey: EventKey;
};

export const getTeamsSearchIndexEntriesQuery = ({ eventKey }: TeamsSearchIndexEntriesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const query = `
		SELECT
			e.event_id,
			e.esw_id,
			COALESCE(d.division_id, 0) AS division_id,
			rt.roster_team_id AS team_id,
			COALESCE(rc.state, '') AS state,
			LOWER(
				rc.club_name || '|' || rt.team_name || '|' || COALESCE(rt.organization_code, '')
			) as search_content,
			ROW_NUMBER() OVER (PARTITION BY e.event_id ORDER BY 
				rt.team_name, 
				rt.roster_team_id
			) AS sort_order
		FROM
			event e
		JOIN roster_team rt
			ON rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted is NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published IS TRUE
		JOIN roster_club rc
			ON rc.roster_club_id = rt.roster_club_id
			AND rc.event_id = e.event_id
			AND rc.deleted IS NULL		
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
	`;

	return [query, sqlVars.getValues()];
};
