import { ENTRY_STATUSES } from '@/shared/constants/team';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type ClubsTeamsQueryParams = {
	eventId: string;
	clubsIds: string[];
};

export const getClubsTeamsQuery = ({ eventId, clubsIds }: ClubsTeamsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const clubsIdsValues = clubsIds.map((id) => sqlVars.useValue(Number(id)));

	const query = `
		SELECT DISTINCT
			rt.master_team_id,
			rt.roster_team_id AS team_id,
			rt.roster_club_id AS club_id,
			rt.organization_code AS team_code,
			rt.team_name,
			rt.extra,
			rt.event_id,
			rt.manual_club_name,
			d.division_id,
			d.short_name AS division_name,
			rc.club_name,
			rc.state,
			rc.code AS club_code
		FROM
			roster_club rc
		JOIN roster_team rt
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = rc.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = rt.event_id
			AND d.published IS TRUE
		WHERE
			rc.event_id = ${sqlVars.useValue(Number(eventId))}
			AND rc.roster_club_id IN (${clubsIdsValues.join(',')})
		ORDER BY
			d.short_name DESC, rt.team_name ASC
	`;

	return [query, sqlVars.getValues()];
};
