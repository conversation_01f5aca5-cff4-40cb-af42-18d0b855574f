import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type DivisionTeamsStandingQueryParams = {
	eventKey: EventKey;
	divisionId: string;
};

export const getDivisionTeamsStandingQuery = ({ eventKey, divisionId }: DivisionTeamsStandingQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	let query = `
		SELECT
			rt.master_team_id,
			rt.roster_team_id AS team_id,
			rt.roster_club_id AS club_id,
			rt.organization_code AS team_code,
			rt.team_name,
			rt.extra,
			rt.event_id,
			rt.manual_club_name,
			d.division_id,
			d.short_name AS division_name,
			rc.club_name,
			rc.state,
			rc.code AS club_code
		FROM event e
		JOIN division d
			ON d.event_id = e.event_id
		JOIN roster_team rt
			ON rt.division_id = d.division_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted is NULL
		JOIN roster_club rc
			ON rc.roster_club_id = rt.roster_club_id
			AND rc.event_id = e.event_id
			AND rc.deleted IS NULL
		JOIN division_standing ds 
			ON ds.event_id = e.event_id
			AND ds.division_id = d.division_id
			AND ds.team_id = rt.roster_team_id
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted IS NULL
			AND d.division_id = ${sqlVars.useValue(Number(divisionId))}
			AND d.published IS TRUE
	`;

	query += `ORDER BY
		COALESCE(CAST(ds.info->>'heading_sort_priority' AS INTEGER), 0),
		rt.team_name,
		rt.roster_team_id
	`;

	return [query, sqlVars.getValues()];
};
