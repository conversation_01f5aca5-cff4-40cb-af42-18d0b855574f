import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type PaginatedDivisionTeamsQueryParams = {
	eventKey: EventKey;
	divisionId: string;
	offset?: number;
	limit?: number;
	search?: string;
};

export const getPaginatedDivisionTeamsQuery = ({
	eventKey,
	divisionId,
	offset,
	limit,
	search,
}: PaginatedDivisionTeamsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	let query = `
		SELECT
			rt.master_team_id,
			rt.roster_team_id AS team_id,
			rt.roster_club_id AS club_id,
			rt.organization_code AS team_code,
			rt.team_name,
			rt.extra,
			rt.event_id,
			rt.manual_club_name,
			d.division_id,
			d.short_name AS division_name,
			rc.club_name,
			rc.state,
			rc.code AS club_code,
			COUNT(*) OVER() AS item_count
		FROM event e
		JOIN division d
			ON d.event_id = e.event_id
		JOIN roster_team rt
			ON rt.division_id = d.division_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted is NULL
		JOIN roster_club rc
			ON rc.roster_club_id = rt.roster_club_id
			AND rc.event_id = e.event_id
			AND rc.deleted IS NULL
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted IS NULL
			AND d.division_id = ${sqlVars.useValue(Number(divisionId))}
			AND d.published is TRUE
	`;

	search = search && search.trim();
	if (search) {
		const sValue = sqlVars.useValue(search);
		const wcVal = sqlVars.useValue(`%${search}%`);
		query += `AND (
			CASE
				WHEN LENGTH(${sValue}) = 2 THEN rc.state ILIKE ${wcVal}
				ELSE rt.team_name ILIKE ${wcVal}
			END
		) `;
	}

	query += 'ORDER BY rt.team_name, rt.roster_team_id ';

	if (offset) {
		query += `OFFSET ${sqlVars.useValue(offset)} `;
	}

	if (limit) {
		query += `LIMIT ${sqlVars.useValue(limit)} `;
	}

	return [query, sqlVars.getValues()];
};
