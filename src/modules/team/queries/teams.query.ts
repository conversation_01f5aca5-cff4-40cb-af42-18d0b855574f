import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type TeamsQueryParams = {
	eventKey: EventKey;
	teamsIds?: string[];
	qualified?: boolean;
	search?: string;
};

export const getTeamsQuery = ({ eventKey, teamsIds, qualified, search }: TeamsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	let query = `
		SELECT
			rt.master_team_id,
			rt.roster_team_id AS team_id,
			rt.roster_club_id AS club_id,
			rt.organization_code AS team_code,
			rt.team_name,
			rt.extra,
			rt.event_id,
			rt.manual_club_name,
			d.division_id,
			d.short_name AS division_name,
			rc.club_name,
			rc.state,
			rc.code AS club_code
		FROM
    	event e
		JOIN roster_team rt
			ON rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted is NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published IS TRUE
		JOIN roster_club rc
			ON rc.roster_club_id = rt.roster_club_id
			AND rc.event_id = e.event_id
			AND rc.deleted IS NULL
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted is NULL
	`;

	if (teamsIds?.length) {
		const teamsIdsValues = teamsIds.map((id) => sqlVars.useValue(Number(id)));
		query += `AND rt.roster_team_id IN (${teamsIdsValues.join(',')}) `;
	}

	if (qualified) {
		query += `AND (rt.extra->>'prev_qual')::BOOLEAN IS TRUE `;
	}

	search = search && search.trim();
	if (search) {
		const sValue = sqlVars.useValue(search);
		const wcVal = sqlVars.useValue(`%${search}%`);
		query += `AND (
			CASE
				WHEN LENGTH(${sValue}) = 2 THEN rc.state ILIKE ${wcVal}
				ELSE (
					rc.club_name ILIKE ${wcVal}
					OR rt.team_name ILIKE ${wcVal}
					OR rt.organization_code ILIKE ${wcVal}
				)
			END
		) `;
	}

	query += qualified ? 'ORDER BY rt.organization_code' : 'ORDER BY rt.team_name';

	return [query, sqlVars.getValues()];
};
