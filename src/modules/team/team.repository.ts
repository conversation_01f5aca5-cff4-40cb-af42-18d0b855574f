import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { groupBy } from '@/shared/utils/collection';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';
import { stripKeysInPlace } from '@/shared/utils/object';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { Team } from './entities';
import {
	getClubsTeamsQuery,
	ClubsTeamsQueryParams,
	getDivisionTeamsStandingQuery,
	DivisionTeamsStandingQueryParams,
	getPaginatedDivisionTeamsQuery,
	PaginatedDivisionTeamsQueryParams,
	getTeamsQuery,
	TeamsQueryParams,
	getPoolBracketsTeamsQuery,
	PoolBracketsTeamsQueryParams,
} from './queries';

export type FetchTeamsParams = TeamsQueryParams;
export type FetchClubsTeamsParams = ClubsTeamsQueryParams;
export type FetchDivisionTeamsStandingParams = DivisionTeamsStandingQueryParams;
export type FetchPaginatedDivisionTeamsParams = Omit<PaginatedDivisionTeamsQueryParams, 'offset' | 'limit'> & PageParams;
export type FetchPoolBracketsTeamsParams = PoolBracketsTeamsQueryParams;

const TEAM_STRING_CAST_FIELDS: StringCastKeys<Team>[] = ['event_id', 'club_id', 'team_id', 'division_id'];
const TEAM_INTERMEDIATE_FIELDS: string[] = ['club_name', 'club_code', 'state'];

@Injectable()
export class TeamRepository {
	constructor(private prisma: PrismaService) {}

	async fetchTeams(params: FetchTeamsParams): Promise<Team[]> {
		const [query, values] = getTeamsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Team[]>(query, ...values);
		return this._formatTeamsFields(items);
	}

	async fetchClubsTeams(params: FetchClubsTeamsParams): Promise<Team[]> {
		const [query, values] = getClubsTeamsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Team[]>(query, ...values);
		return this._formatTeamsFields(items);
	}

	async fetchDivisionTeamsStanding(params: FetchDivisionTeamsStandingParams): Promise<Team[]> {
		const [query, values] = getDivisionTeamsStandingQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Team[]>(query, ...values);
		return this._formatTeamsFields(items);
	}

	async fetchPaginatedDivisionTeams(params: FetchPaginatedDivisionTeamsParams): Promise<PageResults<Team>> {
		const [query, values] = getPaginatedDivisionTeamsQuery({ ...params, ...toOffsetLimit(params) });
		const items = await this.prisma.$queryRawUnsafe<Team[]>(query, ...values);
		if (!items.length) {
			return { items, itemCount: 0 };
		}
		// Get the item count from the first row as it is the same for all rows
		return {
			items: this._formatTeamsFields(items),
			itemCount: Number((items[0] as unknown as Record<string, string>).item_count),
		};
	}

	async fetchPoolBracketsTeamsMap(params: FetchPoolBracketsTeamsParams): Promise<Record<string, Team[]>> {
		const [query, values] = getPoolBracketsTeamsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<(Team & { pool_bracket_id: string })[]>(query, ...values);
		this._formatTeamsFields(items);
		return groupBy(items, 'pool_bracket_id');
	}

	private _formatTeamsFields(teams: Team[]): Team[] {
		castFieldsToString(teams, TEAM_STRING_CAST_FIELDS);
		teams.forEach((team) => {
			this._formatTeamExternal(team);
			this._cleanupTeamIntermediateFields(team);
		});
		return teams;
	}

	private _formatTeamExternal(team: Team): void {
		const { club_id, club_name, club_code, state } = team as unknown as Record<string, string>;
		team.external = {
			club_info: {
				roster_club_id: club_id,
				club_name,
				club_code,
				state,
			},
		};
	}

	private _cleanupTeamIntermediateFields(team: Team): void {
		stripKeysInPlace(team, TEAM_INTERMEDIATE_FIELDS);
	}
}
