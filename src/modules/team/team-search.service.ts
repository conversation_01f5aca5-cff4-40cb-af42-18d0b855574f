import { Injectable, OnModuleInit } from '@nestjs/common';

import configuration from '@/infra/configuration';
import { EventHelperService } from '@/infra/eventHelper/eventHelper.service';
import { EventKey } from '@/shared/utils/event';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { MIN_SEARCH_LENGTH } from './constants';
import { TeamSearchRepository } from './team-search.repository';
import { TeamSearchIndexEntry } from './types';
import { InjectSearchManager } from '../search/decorators';
import { SearchEntityName } from '../search/types';
import { RedisSearchManager, Search } from '../search/utils/redis-search-manager';

export type FindTeamsIdsParams = {
	eventKey: EventKey;
	search: string;
	divisionId?: string;
};
export type FindPaginatedTeamsIdsParams = FindTeamsIdsParams & PageParams;

const SEARCH_SERVICE_DISABLED = !configuration.USE_SEARCH_SERVICES;

@Injectable()
export class TeamSearchService implements OnModuleInit {
	constructor(
		@InjectSearchManager(SearchEntityName.Teams) private readonly searchManager: RedisSearchManager<TeamSearchIndexEntry>,
		private teamSearchRepository: TeamSearchRepository,
		private eventHelperService: EventHelperService,
	) {}

	async onModuleInit() {
		if (SEARCH_SERVICE_DISABLED) return;
		return this.searchManager.createOrUpdateIndex();
	}

	canSearchWith<T extends { search?: string }>(params: T): params is T & { search: string } {
		if (SEARCH_SERVICE_DISABLED) return false;
		return params.search?.trim().length >= MIN_SEARCH_LENGTH;
	}

	async findTeamsIds(params: FindTeamsIdsParams): Promise<string[]> {
		await this.actualizeIndexEntries(params.eventKey);

		const searchContent = this.searchManager.sanitizeSearchText(params.search);
		if (searchContent.length < MIN_SEARCH_LENGTH) {
			return [];
		}

		const entries = await this._prepareBuilder({ ...params, search: searchContent })
			.sortBy('sort_order')
			.return.all({ pageSize: 100 });
		return entries.map((item) => item.team_id);
	}

	async findPaginatedTeamsIds(params: FindPaginatedTeamsIdsParams): Promise<PageResults<string>> {
		await this.actualizeIndexEntries(params.eventKey);

		const searchContent = this.searchManager.sanitizeSearchText(params.search);
		if (searchContent.length < MIN_SEARCH_LENGTH) {
			return { items: [], itemCount: 0 };
		}

		const queryBuilder = this._prepareBuilder({ ...params, search: searchContent });

		const { offset, limit } = toOffsetLimit(params);
		const [entries, itemCount] = await Promise.all([
			queryBuilder.sortBy('sort_order').return.page(offset, limit),
			queryBuilder.count(), // TODO Get the count from the search result
		]);
		return { items: entries.map((item) => item.team_id), itemCount };
	}

	actualizeIndexEntries(eventKey: EventKey): Promise<void> {
		return this.searchManager.actualizeIndexEntries(eventKey.value, async () => {
			const [entries, { event_id, esw_id }] = await Promise.all([
				this.teamSearchRepository.fetchTeamsSearchIndexEntries(eventKey),
				this.eventHelperService.getEventIdentifiers(eventKey),
			]);
			return [entries, [event_id, esw_id]];
		});
	}

	clearIndexEntries(eventKey: EventKey): Promise<void> {
		return this.searchManager.clearIndexEntries(eventKey.value);
	}

	private _prepareBuilder({ eventKey, search, divisionId }: FindTeamsIdsParams) {
		let queryBuilder = this.searchManager.find();
		queryBuilder = this._withEventKey(queryBuilder, eventKey);
		queryBuilder = this._withDivision(queryBuilder, divisionId);
		return this._withTextSearch(queryBuilder, search);
	}

	private _withEventKey(queryBuilder: Search<TeamSearchIndexEntry>, eventKey: EventKey): Search<TeamSearchIndexEntry> {
		return queryBuilder.where(eventKey.isESWID ? 'esw_id' : 'event_id').eq(eventKey.value);
	}

	private _withDivision(queryBuilder: Search<TeamSearchIndexEntry>, divisionId: string): Search<TeamSearchIndexEntry> {
		return divisionId ? queryBuilder.and('division_id').eq(divisionId) : queryBuilder;
	}

	private _withTextSearch(queryBuilder: Search<TeamSearchIndexEntry>, searchContent: string): Search<TeamSearchIndexEntry> {
		return searchContent.length === 2
			? // If the search query is a state code, we should search by the state field
			  queryBuilder.and('state').eq(searchContent)
			: queryBuilder.and('search_content').matches(`*${searchContent}*`);
	}
}
