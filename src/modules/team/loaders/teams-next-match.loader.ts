import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Match } from '@/modules/match/entities';
import { MatchService } from '@/modules/match/match.service';
import { Team } from '@/modules/team/entities';

@Injectable()
export class TeamsNextMatchLoader {
	constructor(private matchService: MatchService) {}

	create(eventId: string): DataLoader<Team, Match> {
		return new DataLoader<Team, Match>(
			async (teams: Team[]) => {
				const teamsIds = teams.map((team) => team.team_id);
				const teamsNextMatchMap = await this.matchService.fetchTeamsNextMatchMap({ eventId, teamsIds });
				return teamsIds.map((teamId) => teamsNextMatchMap[teamId]);
			},
			{ cache: false, name: 'TeamsNextMatchLoader' },
		);
	}
}
