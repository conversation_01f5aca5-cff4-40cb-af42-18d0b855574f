import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { PoolOrBracket } from '@/modules/poolBracket/entities';
import { PoolBracketService } from '@/modules/poolBracket/pool-bracket.service';
import { Team } from '@/modules/team/entities';

@Injectable()
export class TeamsOngoingPoolBracketsLoader {
	constructor(private poolBracketService: PoolBracketService) {}

	create(eventId: string): DataLoader<Team, typeof PoolOrBracket | null> {
		return new DataLoader<Team, typeof PoolOrBracket | null>(
			async (teams: Team[]) => {
				const teamsIds = teams.map((team) => team.team_id);
				const teamsOngoingPoolBracketsMap = await this.poolBracketService.fetchTeamsOngoingPoolBracketsMap({ eventId, teamsIds });
				return teamsIds.map((teamId) => teamsOngoingPoolBracketsMap[teamId]);
			},
			{ cache: true, name: 'TeamsOngoingPoolBracketsLoader' },
		);
	}
}
