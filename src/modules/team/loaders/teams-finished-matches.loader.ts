import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Match } from '@/modules/match/entities';
import { MatchService } from '@/modules/match/match.service';
import { Team } from '@/modules/team/entities';

@Injectable()
export class TeamsFinishedMatchesLoader {
	constructor(private matchService: MatchService) {}

	create(eventId: string): DataLoader<Team, Match[]> {
		return new DataLoader<Team, Match[]>(
			async (teams: Team[]) => {
				const teamsIds = teams.map((team) => team.team_id);
				const teamsFinishedMatchesMap = await this.matchService.fetchTeamsFinishedMatchesMap({ eventId, teamsIds });
				return teamsIds.map((teamId) => teamsFinishedMatchesMap[teamId]);
			},
			{ cache: true, name: 'TeamsFinishedMatchesLoader' },
		);
	}
}
