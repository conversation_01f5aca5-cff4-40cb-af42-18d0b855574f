import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { DivisionStandingService } from '@/modules/divisionStanding/division-standing.service';
import { DivisionStanding } from '@/modules/divisionStanding/entities';
import { Team } from '@/modules/team/entities';

@Injectable()
export class TeamsDivisionStandingLoader {
	constructor(private divisionStandingService: DivisionStandingService) {}

	create(eventId: string): DataLoader<Team, DivisionStanding> {
		return new DataLoader<Team, DivisionStanding>(
			async (teams: Team[]) => {
				const teamsDivisionStanding = await this.divisionStandingService.fetchTeamsDivisionStanding({ eventId, divisionTeamPairs: teams });
				return teams.map((team) => teamsDivisionStanding.find((ds) => ds.team_id === team.team_id && ds.division_id === team.division_id));
			},
			{ cache: false, name: 'TeamsDivisionStandingLoader' },
		);
	}
}
