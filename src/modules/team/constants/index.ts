import { SchemaDefinition } from 'redis-om';

import { TeamSearchIndexEntry } from '../types';

export const MIN_SEARCH_LENGTH = 2;

export const TEAMS_INDEX_SCHEMA: SchemaDefinition<TeamSearchIndexEntry> = {
	esw_id: { type: 'string' },
	event_id: { type: 'string' },
	division_id: { type: 'string' },
	team_id: { type: 'string', indexed: false },
	state: { type: 'string' },
	search_content: { type: 'text' },
	sort_order: { type: 'number', sortable: true },
};
