import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { EventKey } from '@/shared/utils/event';
import { castFieldsToNumber, castFieldsToString } from '@/shared/utils/format';

import { getTeamsSearchIndexEntriesQuery } from './queries';
import { TeamSearchIndexEntry } from './types';

@Injectable()
export class TeamSearchRepository {
	constructor(private prisma: PrismaService) {}

	async fetchTeamsSearchIndexEntries(eventKey: EventKey): Promise<TeamSearchIndexEntry[]> {
		const [query, values] = getTeamsSearchIndexEntriesQuery({ eventKey });
		const items = await this.prisma.$queryRawUnsafe<TeamSearchIndexEntry[]>(query, ...values);
		castFieldsToNumber(items, ['sort_order']);
		castFieldsToString(items, ['event_id', 'team_id', 'division_id']);
		return items;
	}
}
