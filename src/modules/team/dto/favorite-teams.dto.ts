import { ArgsType, Field, ID } from '@nestjs/graphql';
import { ArrayMaxSize, ArrayMinSize, IsOptional } from 'class-validator';

import { IsEventKeyValue } from '@/shared/graphql/dto/validators/is-event-key-value.validator';
import { IsNumericId } from '@/shared/graphql/dto/validators/is-numeric-id.validator';
import { IsSearchField } from '@/shared/graphql/dto/validators/is-search-field.validator';

import { MIN_SEARCH_LENGTH } from '../constants';

@ArgsType()
export class FavoriteTeamsInputDto {
	@Field(() => ID)
	@IsEventKeyValue()
	eventKey: string;

	@Field(() => [ID])
	@ArrayMinSize(1, { message: '"teamsIds" must contain at least 1 team' })
	@ArrayMaxSize(200, { message: '"teamsIds" must contain at most 200 teams' })
	@IsNumericId({ each: true, message: '"teamsIds" must contain valid numeric IDs' })
	teamsIds: string[];

	@Field(() => String, { nullable: true })
	@IsOptional()
	@IsSearchField(MIN_SEARCH_LENGTH)
	search?: string;
}
