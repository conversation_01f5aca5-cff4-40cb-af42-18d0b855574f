import { ArgsType, Field, Int } from '@nestjs/graphql';
import { IsOptional, IsInt, Min, IsBoolean } from 'class-validator';

@ArgsType()
export class TeamMatchesOptionsInputDto {
	@Field(() => Boolean, { nullable: true })
	@IsOptional()
	@IsBoolean()
	pb_only?: boolean;

	@Field(() => Boolean, { nullable: true })
	@IsOptional()
	@IsBoolean()
	as_team?: boolean;

	@Field(() => Boolean, { nullable: true })
	@IsOptional()
	@IsBoolean()
	as_ref?: boolean;

	@Field(() => Int, { nullable: true })
	@IsOptional()
	@IsInt()
	@Min(1, { message: '"limit" must be at least 1' })
	limit?: number;
}
