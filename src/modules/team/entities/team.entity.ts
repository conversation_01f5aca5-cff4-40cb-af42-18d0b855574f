import { Field, ObjectType, ID } from '@nestjs/graphql';

import { ClubShortInfo } from '@/modules/club/entities';

import { TeamShortInfo } from './team-short-info.entity';

@ObjectType()
class TeamExtra {
	@Field({ nullable: true })
	prev_qual: boolean;

	@Field({ nullable: true })
	prev_qual_age?: string;

	@Field({ nullable: true })
	prev_qual_division?: string;

	@Field({ nullable: true })
	show_accepted_bid?: boolean;

	@Field({ nullable: true })
	show_previously_accepted_bid?: string;

	@Field({ nullable: true })
	earned_at?: string;
}

@ObjectType()
class TeamExternal {
	@Field(() => ClubShortInfo)
	club_info: ClubShortInfo;
}

@ObjectType()
export class Team extends TeamShortInfo {
	event_id: string;

	@Field(() => ID)
	club_id: string;

	@Field({ nullable: true })
	team_code: string;

	@Field(() => ID)
	division_id: string;

	@Field({ nullable: true })
	division_name: string;

	@Field(() => TeamExtra, { nullable: true })
	extra: TeamExtra;

	@Field(() => TeamExternal)
	external: TeamExternal;

	/**
	 * This field is used to display the club name when the team is not associated with a real club, and has a manual club name.
	 * TODO: Must be properly handled as a manual/virtual club in the future
	 */
	@Field({ nullable: true })
	manual_club_name?: string;
}
