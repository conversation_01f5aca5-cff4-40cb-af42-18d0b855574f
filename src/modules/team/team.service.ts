import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';

import { ExtendedCacheService, ItemCacheKeyConfig, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheCategories, CacheCommonKeys, CacheScopes } from '@/shared/constants/cache';
import { groupBy, keyBy } from '@/shared/utils/collection';
import { completeObject } from '@/shared/utils/object';
import { createPageInfo } from '@/shared/utils/pagination';

import { PaginatedTeams, Team } from './entities';
import { FindPaginatedTeamsIdsParams, FindTeamsIdsParams, TeamSearchService } from './team-search.service';
import {
	TeamRepository,
	FetchTeamsParams as TeamsParams,
	FetchClubsTeamsParams,
	FetchDivisionTeamsStandingParams,
	FetchPaginatedDivisionTeamsParams,
	FetchPoolBracketsTeamsParams,
} from './team.repository';

export type FetchQualifiedTeamsParams = Pick<TeamsParams, 'eventKey'>;
export type FetchTeamsParams = Pick<TeamsParams, 'eventKey' | 'teamsIds'> & {
	search?: string;
};

@Injectable()
export class TeamService {
	constructor(
		private teamRepository: TeamRepository,
		private teamSearchService: TeamSearchService,
		private cacheService: ExtendedCacheService,
	) {}

	async fetchFavoriteTeams(params: FetchTeamsParams): Promise<Team[]> {
		if (this.teamSearchService.canSearchWith(params)) {
			return this._findFavoriteTeams(params);
		} else {
			const favoriteTeams = await this.cacheService.getOrFetchMany<Team | null>(
				this._getTeamsCacheKeyConfig(params),
				params.teamsIds,
				(missingIds) => this._getTeamsData({ ...params, teamsIds: missingIds }),
			);
			// Ordering needs to be done here because the cache service doesn't guarantee the order of the items
			return Object.values(favoriteTeams).filter(Boolean);
		}
	}

	async fetchClubsTeamsMap(params: FetchClubsTeamsParams): Promise<Record<string, Team[]>> {
		const cacheKeyConfig = this._getClubsTeamsCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Team[]>(cacheKeyConfig, params.clubsIds, (missingIds) =>
			this._getClubsTeamsData({ ...params, clubsIds: missingIds }),
		);
	}

	fetchQualifiedTeams(params: FetchQualifiedTeamsParams): Promise<Team[]> {
		const cacheKeyConfig = this._getQualifiedTeamsCacheKeyConfig(params);
		return this.cacheService.getOrFetch<Team[]>(cacheKeyConfig, () => this.teamRepository.fetchTeams({ ...params, qualified: true }));
	}

	fetchDivisionTeamsStanding(params: FetchDivisionTeamsStandingParams): Promise<Team[]> {
		const cacheKeyConfig = this._getDivisionTeamsStandingCacheKeyConfig(params);
		return this.cacheService.getOrFetch<Team[]>(cacheKeyConfig, () => this.teamRepository.fetchDivisionTeamsStanding(params));
	}

	async fetchPaginatedDivisionTeams(params: FetchPaginatedDivisionTeamsParams): Promise<PaginatedTeams> {
		if (this.teamSearchService.canSearchWith(params)) {
			return this._findPaginatedDivisionTeams(params);
		} else {
			return this.cacheService.getOrFetch<PaginatedTeams>(this._getPaginatedDivisionTeamsCacheKeyConfig(params), () =>
				this._fetchPaginatedDivisionTeamsData(params),
			);
		}
	}

	fetchPoolBracketsTeamsMap(params: FetchPoolBracketsTeamsParams): Promise<Record<string, Team[]>> {
		const cacheKeyConfig = this._getPoolBracketsTeamsCacheKeyConfig(params);
		return this.cacheService.getOrFetchMany<Team[]>(cacheKeyConfig, params.poolBracketsIds, (missingIds) =>
			this._getPoolBracketsTeamsData({ ...params, poolBracketsIds: missingIds }),
		);
	}

	private async _fetchPaginatedDivisionTeamsData(params: FetchPaginatedDivisionTeamsParams): Promise<PaginatedTeams> {
		const { items, itemCount } = await this.teamRepository.fetchPaginatedDivisionTeams(params);
		return {
			items,
			page_info: createPageInfo(params, itemCount),
		};
	}

	private async _getTeamsData(params: TeamsParams): Promise<Record<string, Team | null>> {
		const teams = await this.teamRepository.fetchTeams(params);
		const teamsMap = keyBy(teams, 'team_id');
		return completeObject(params.teamsIds, teamsMap, null);
	}

	private async _findFavoriteTeams(params: FetchTeamsParams): Promise<Team[]> {
		const { eventKey } = params;
		const teamsIds = await this.teamSearchService.findTeamsIds(params as FindTeamsIdsParams);
		const intersectedIds = _.intersection(teamsIds, params.teamsIds);
		const teams = await this.cacheService.getOrFetchMany<Team | null>(
			this._getTeamsCacheKeyConfig({ ...params, search: '' }), // Setting search to empty string to involve the cache
			intersectedIds,
			(missingIds) => this._getTeamsData({ eventKey, teamsIds: missingIds }),
		);
		return teamsIds.map((id) => teams[id]).filter(Boolean);
	}

	private async _findPaginatedDivisionTeams(params: FindPaginatedTeamsIdsParams): Promise<PaginatedTeams> {
		const { eventKey } = params;
		const { items: teamsIds, itemCount } = await this.teamSearchService.findPaginatedTeamsIds(params);
		const teams = await this.cacheService.getOrFetchMany(this._getTeamsCacheKeyConfig(params), teamsIds, (missingIds) =>
			this._getTeamsData({ eventKey, teamsIds: missingIds }),
		);
		return {
			items: teamsIds.map((id) => teams[id]).filter(Boolean),
			page_info: createPageInfo(params, itemCount),
		};
	}

	private async _getClubsTeamsData(params: FetchClubsTeamsParams): Promise<Record<string, Team[]>> {
		const clubsTeams = await this.teamRepository.fetchClubsTeams(params);
		const clubsTeamMap = groupBy(clubsTeams, 'club_id');
		return completeObject(params.clubsIds, clubsTeamMap, []);
	}

	private async _getPoolBracketsTeamsData(params: FetchPoolBracketsTeamsParams): Promise<Record<string, Team[]>> {
		const poolBracketsTeamsMap = await this.teamRepository.fetchPoolBracketsTeamsMap(params);
		return completeObject(params.poolBracketsIds, poolBracketsTeamsMap, []);
	}

	private _getPaginatedDivisionTeamsCacheKeyConfig(params: FetchPaginatedDivisionTeamsParams): ItemCacheKeyConfig | null {
		if (params.search?.trim()) return null;

		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.DivisionTeams,
			categoryKey: `${params.divisionId}-${params.page}-${params.pageSize}`,
		};
	}

	private _getDivisionTeamsStandingCacheKeyConfig(params: FetchDivisionTeamsStandingParams): ItemCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.DivisionTeams,
			categoryKey: params.divisionId,
		};
	}

	private _getQualifiedTeamsCacheKeyConfig(params: FetchQualifiedTeamsParams): ItemCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.QualifiedTeams,
			categoryKey: CacheCommonKeys.All,
		};
	}

	private _getClubsTeamsCacheKeyConfig(params: FetchClubsTeamsParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.ClubTeams,
		};
	}

	private _getPoolBracketsTeamsCacheKeyConfig(params: FetchPoolBracketsTeamsParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventId,
			category: CacheCategories.PoolBracketTeams,
		};
	}

	private _getTeamsCacheKeyConfig(params: TeamsParams): ItemsCacheKeyConfig | null {
		if (params.search?.trim()) return null;

		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.Team,
		};
	}
}
