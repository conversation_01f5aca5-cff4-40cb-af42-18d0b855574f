import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { EventKey } from '@/shared/utils/event';
import { castFieldsToNumber, castFieldsToString, NumberCastKeys, StringCastKeys } from '@/shared/utils/format';

import { getClubsSearchIndexEntriesQuery } from './queries/clubs-search-index-entries.query';
import { ClubSearchIndexEntry } from './types';

const CLUB_SEARCH_NUMBER_CAST_FIELDS: NumberCastKeys<ClubSearchIndexEntry>[] = ['sort_order'];
const CLUB_SEARCH_STRING_CAST_FIELDS: StringCastKeys<ClubSearchIndexEntry>[] = ['event_id', 'club_id'];

@Injectable()
export class ClubSearchRepository {
	constructor(private prisma: PrismaService) {}

	async fetchClubsSearchIndexEntries(eventKey: EventKey): Promise<ClubSearchIndexEntry[]> {
		const [query, values] = getClubsSearchIndexEntriesQuery({ eventKey });
		const items = await this.prisma.$queryRawUnsafe<ClubSearchIndexEntry[]>(query, ...values);
		castFieldsToNumber(items, CLUB_SEARCH_NUMBER_CAST_FIELDS);
		castFieldsToString(items, CLUB_SEARCH_STRING_CAST_FIELDS);
		return items;
	}
}
