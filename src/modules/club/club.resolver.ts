import { Args, Context, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';

import { Team } from '@/modules/team/entities';
import { EventKey } from '@/shared/utils/event';

import { ClubService } from './club.service';
import { PaginatedClubsInputDto } from './dto/paginated-clubs.dto';
import { Club, PaginatedClubs } from './entities';
import { ClubsTeamsLoader } from './loaders/clubs-teams.loader';

type ClubResolverContext = {
	clubsTeamsLoader: ReturnType<ClubsTeamsLoader['create']>;
};

@Resolver(() => Club)
export class ClubResolver {
	constructor(private readonly clubService: ClubService, private readonly clubsTeamsLoader: ClubsTeamsLoader) {}

	@Query(() => PaginatedClubs)
	paginatedClubs(@Args() args: PaginatedClubsInputDto): Promise<PaginatedClubs> {
		return this.clubService.fetchPaginatedClubs({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@ResolveField(() => [Team])
	teams(@Parent() club: Club, @Context() context: ClubResolverContext): Promise<Team[]> {
		if (!context.clubsTeamsLoader) {
			context.clubsTeamsLoader = this.clubsTeamsLoader.create(club.event_id);
		}
		return context.clubsTeamsLoader.load(club);
	}
}
