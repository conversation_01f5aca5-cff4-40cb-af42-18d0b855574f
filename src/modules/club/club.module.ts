import { Modu<PERSON> } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { EventHelperModule } from '@/infra/eventHelper/eventHelper.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { ClubSearchRepository } from './club-search.repository';
import { ClubSearchService } from './club-search.service';
import { ClubRepository } from './club.repository';
import { ClubResolver } from './club.resolver';
import { ClubService } from './club.service';
import { CLUBS_INDEX_SCHEMA } from './constants';
import { SearchModule } from '../search/search.module';
import { ClubsTeamsLoader } from './loaders/clubs-teams.loader';
import { SearchEntityName } from '../search/types';
import { TeamModule } from '../team/team.module';

@Module({
	imports: [PrismaModule, CacheModule, TeamModule, EventHelperModule, SearchModule.forFeature(SearchEntityName.Clubs, CLUBS_INDEX_SCHEMA)],
	providers: [ClubResolver, ClubService, ClubRepository, ClubsTeamsLoader, ClubSearchService, ClubSearchRepository],
	exports: [ClubSearchService],
})
export class ClubModule {}
