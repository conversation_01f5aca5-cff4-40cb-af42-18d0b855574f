import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Club } from '@/modules/club/entities';
import { Team } from '@/modules/team/entities';
import { TeamService } from '@/modules/team/team.service';

@Injectable()
export class ClubsTeamsLoader {
	constructor(private teamService: TeamService) {}

	create(eventId: string): DataLoader<Club, Team[]> {
		return new DataLoader<Club, Team[]>(
			async (clubs: Club[]) => {
				const clubsIds = clubs.map((club) => club.roster_club_id);
				const clubsTeamsMap = await this.teamService.fetchClubsTeamsMap({ eventId, clubsIds });
				return clubsIds.map((clubId) => clubsTeamsMap[clubId]);
			},
			{ cache: false, name: 'ClubsTeamsLoader' },
		);
	}
}
