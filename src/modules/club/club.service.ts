import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemCacheKeyConfig, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheCategories, CacheScopes } from '@/shared/constants/cache';
import { keyBy } from '@/shared/utils/collection';
import { EventKey } from '@/shared/utils/event';
import { completeObject } from '@/shared/utils/object';
import { createPageInfo } from '@/shared/utils/pagination';

import { ClubSearchService, FindPaginatedClubsIdsParams } from './club-search.service';
import { ClubRepository, FetchPaginatedClubsParams as PaginatedClubsParams, FetchClubsParams } from './club.repository';
import { Club, PaginatedClubs } from './entities';

export type FetchPaginatedClubsParams = PaginatedClubsParams;

@Injectable()
export class ClubService {
	constructor(
		private clubRepository: ClubRepository,
		private clubSearchService: ClubSearchService,
		private cacheService: ExtendedCacheService,
	) {}

	async fetchPaginatedClubs(params: FetchPaginatedClubsParams): Promise<PaginatedClubs> {
		if (this.clubSearchService.canSearchWith(params)) {
			return this._findPaginatedClubs(params);
		} else {
			return this.cacheService.getOrFetch<PaginatedClubs>(this._getPaginatedClubsCacheKeyConfig(params), () =>
				this._getPaginatedClubsData(params),
			);
		}
	}

	private async _getPaginatedClubsData(params: FetchPaginatedClubsParams): Promise<PaginatedClubs> {
		const { items, itemCount } = await this.clubRepository.fetchPaginatedClubs(params);
		return {
			items,
			page_info: createPageInfo(params, itemCount),
		};
	}

	private async _getClubsData(params: FetchClubsParams): Promise<Record<string, Club | null>> {
		const clubs = await this.clubRepository.fetchClubs(params);
		const clubsMap = keyBy(clubs, 'roster_club_id');
		return completeObject(params.clubsIds, clubsMap, null);
	}

	private async _findPaginatedClubs(params: FindPaginatedClubsIdsParams): Promise<PaginatedClubs> {
		const { eventKey } = params;
		const { items: clubsIds, itemCount } = await this.clubSearchService.findPaginatedClubsIds(params);
		const clubs = await this.cacheService.getOrFetchMany(this._getClubsCacheKeyConfig(eventKey), clubsIds, (missingIds) =>
			this._getClubsData({ eventKey, clubsIds: missingIds }),
		);
		return {
			items: clubsIds.map((clubId) => clubs[clubId]).filter(Boolean),
			page_info: createPageInfo(params, itemCount),
		};
	}

	private _getPaginatedClubsCacheKeyConfig(params: FetchPaginatedClubsParams): ItemCacheKeyConfig | null {
		if (params.search?.trim()) return null; // Do not cache search results

		const { eventKey, page, pageSize } = params;
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.Club,
			categoryKey: `${page}-${pageSize}`,
		};
	}

	private _getClubsCacheKeyConfig(eventKey: EventKey): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: eventKey.value,
			category: CacheCategories.Club,
		};
	}
}
