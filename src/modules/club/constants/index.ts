import { SchemaDefinition } from 'redis-om';

import { ClubSearchIndexEntry } from '../types';

export const MIN_SEARCH_LENGTH = 2;

export const CLUBS_INDEX_SCHEMA: SchemaDefinition<ClubSearchIndexEntry> = {
	esw_id: { type: 'string' },
	event_id: { type: 'string' },
	club_id: { type: 'string', indexed: false },
	state: { type: 'string' },
	search_content: { type: 'text' },
	sort_order: { type: 'number', sortable: true },
};
