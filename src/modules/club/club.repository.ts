import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToNumber, castFieldsToString, NumberCastKeys, StringCastKeys } from '@/shared/utils/format';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { Club } from './entities';
import { getClubsQuery, ClubsQueryParams } from './queries/clubs.query';
import { getPaginatedClubsQuery, PaginatedClubsQueryParams } from './queries/paginated-clubs.query';

export type FetchClubsParams = ClubsQueryParams;
export type FetchPaginatedClubsParams = Omit<PaginatedClubsQueryParams, 'offset' | 'limit'> & PageParams;

const CLUB_NUMBER_CAST_FIELDS: NumberCastKeys<Club>[] = ['teams_count'];
const CLUB_STRING_CAST_FIELDS: StringCastKeys<Club>[] = ['event_id', 'roster_club_id'];

@Injectable()
export class ClubRepository {
	constructor(private prisma: PrismaService) {}

	async fetchPaginatedClubs(params: FetchPaginatedClubsParams): Promise<PageResults<Club>> {
		const [query, values] = getPaginatedClubsQuery({ ...params, ...toOffsetLimit(params) });

		const items = await this.prisma.$queryRawUnsafe<Club[]>(query, ...values);
		if (!items.length) {
			return { items, itemCount: 0 };
		}
		// Get the item count from the first row as it is the same for all rows
		return {
			items: this._formatClubsFields(items),
			itemCount: Number((items[0] as unknown as Record<string, string>).item_count),
		};
	}

	async fetchClubs(params: FetchClubsParams): Promise<Club[]> {
		const [query, values] = getClubsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Club[]>(query, ...values);
		return this._formatClubsFields(items);
	}

	private _formatClubsFields(clubs: Club[]): Club[] {
		castFieldsToNumber(clubs, CLUB_NUMBER_CAST_FIELDS);
		castFieldsToString(clubs, CLUB_STRING_CAST_FIELDS);
		return clubs;
	}
}
