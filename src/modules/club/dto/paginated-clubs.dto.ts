import { ArgsType, Field, ID } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';

import { PaginationInputDto } from '@/shared/graphql/dto/pagination.dto';
import { IsEventKeyValue } from '@/shared/graphql/dto/validators/is-event-key-value.validator';
import { IsSearchField } from '@/shared/graphql/dto/validators/is-search-field.validator';

import { MIN_SEARCH_LENGTH } from '../constants';

@ArgsType()
export class PaginatedClubsInputDto extends PaginationInputDto {
	@Field(() => ID)
	@IsEventKeyValue()
	eventKey: string;

	@Field(() => String, { nullable: true })
	@IsOptional()
	@IsSearchField(MIN_SEARCH_LENGTH)
	search?: string;
}
