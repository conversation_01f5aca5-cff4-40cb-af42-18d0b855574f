import { Injectable, OnModuleInit } from '@nestjs/common';

import configuration from '@/infra/configuration';
import { EventHelperService } from '@/infra/eventHelper/eventHelper.service';
import { EventKey } from '@/shared/utils/event';
import { PageParams, PageResults, toOffsetLimit } from '@/shared/utils/pagination';

import { ClubSearchRepository } from './club-search.repository';
import { MIN_SEARCH_LENGTH } from './constants';
import { ClubSearchIndexEntry } from './types';
import { InjectSearchManager } from '../search/decorators';
import { SearchEntityName } from '../search/types';
import { RedisSearchManager, Search } from '../search/utils/redis-search-manager';

export type FindPaginatedClubsIdsParams = {
	eventKey: EventKey;
	search: string;
} & PageParams;

const SEARCH_SERVICE_DISABLED = !configuration.USE_SEARCH_SERVICES;

@Injectable()
export class ClubSearchService implements OnModuleInit {
	constructor(
		@InjectSearchManager(SearchEntityName.Clubs) private readonly searchManager: RedisSearchManager<ClubSearchIndexEntry>,
		private clubIndicesRepository: ClubSearchRepository,
		private eventHelperService: EventHelperService,
	) {}

	async onModuleInit(): Promise<void> {
		if (SEARCH_SERVICE_DISABLED) return;
		return this.searchManager.createOrUpdateIndex();
	}

	canSearchWith<T extends { search?: string }>(params: T): params is T & { search: string } {
		if (SEARCH_SERVICE_DISABLED) return false;
		return params.search?.trim().length >= MIN_SEARCH_LENGTH;
	}

	async findPaginatedClubsIds(params: FindPaginatedClubsIdsParams): Promise<PageResults<string>> {
		const { eventKey } = params;
		await this.actualizeIndexEntries(eventKey);

		const searchContent = this.searchManager.sanitizeSearchText(params.search);
		if (searchContent.length < MIN_SEARCH_LENGTH) {
			return { items: [], itemCount: 0 };
		}

		let queryBuilder = this.searchManager.find();
		queryBuilder = this._withEventKey(queryBuilder, eventKey);
		queryBuilder = this._withTextSearch(queryBuilder, searchContent);

		const { offset, limit } = toOffsetLimit(params);
		const [items, itemCount] = await Promise.all([
			queryBuilder.sortBy('sort_order').return.page(offset, limit),
			queryBuilder.count(), // TODO Get the count from the search result
		]);
		return { items: items.map((item) => item.club_id), itemCount };
	}

	actualizeIndexEntries(eventKey: EventKey): Promise<void> {
		return this.searchManager.actualizeIndexEntries(eventKey.value, async () => {
			const [entries, { event_id, esw_id }] = await Promise.all([
				this.clubIndicesRepository.fetchClubsSearchIndexEntries(eventKey),
				this.eventHelperService.getEventIdentifiers(eventKey),
			]);
			return [entries, [event_id, esw_id]];
		});
	}

	clearIndexEntries(eventKey: EventKey): Promise<void> {
		return this.searchManager.clearIndexEntries(eventKey.value);
	}

	private _withEventKey(queryBuilder: Search<ClubSearchIndexEntry>, eventKey: EventKey): Search<ClubSearchIndexEntry> {
		return queryBuilder.where(eventKey.isESWID ? 'esw_id' : 'event_id').eq(eventKey.value);
	}

	private _withTextSearch(queryBuilder: Search<ClubSearchIndexEntry>, searchContent: string): Search<ClubSearchIndexEntry> {
		return searchContent.length === 2
			? // If the search query is a state code, we should search by the state field
			  queryBuilder.and('state').eq(searchContent)
			: queryBuilder.and('search_content').matches(`*${searchContent}*`);
	}
}
