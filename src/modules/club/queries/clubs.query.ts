import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type ClubsQueryParams = {
	eventKey: EventKey;
	clubsIds: string[];
};

export const getClubsQuery = ({ eventKey, clubsIds }: ClubsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const clubsIdsValues = clubsIds.map((id) => sqlVars.useValue(Number(id)));
	const query = `
		SELECT
			e.event_id,
			rc.roster_club_id,
			rc.club_name,
			rc.state,
			rc.code AS club_code,
			COUNT(DISTINCT rt.roster_team_id) AS teams_count
		FROM
			event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted IS NULL
		JOIN roster_team rt
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted IS NULL
			AND rt.roster_club_id IN (${clubsIdsValues.join(',')})
		GROUP BY e.event_id, rc.roster_club_id
	`;

	return [query, sqlVars.getValues()];
};
