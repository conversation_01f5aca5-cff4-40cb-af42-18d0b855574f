import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type PaginatedClubsQueryParams = {
	eventKey: EventKey;
	offset?: number;
	limit?: number;
	search?: string;
};

export const getPaginatedClubsQuery = ({ eventKey, offset, limit, search }: PaginatedClubsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	let query = `
		SELECT
			e.event_id,
			rc.roster_club_id,
			rc.club_name,
			rc.state,
			rc.code AS club_code,
			COUNT(DISTINCT rt.roster_team_id) AS teams_count,
			COUNT(*) OVER() AS item_count
		FROM
			event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted IS NULL
		JOIN roster_team rt
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND e.deleted IS NULL
	`;

	search = search && search.trim();
	if (search) {
		const sValue = sqlVars.useValue(search);
		const wcVal = sqlVars.useValue(`%${search}%`);
		query += `AND (
			CASE
				WHEN LENGTH(${sValue}) = 2 THEN rc.state ILIKE ${wcVal}
				ELSE (
					rc.club_name ILIKE ${wcVal} OR
					rc.code ILIKE ${wcVal} OR
					EXISTS (
						SELECT 1 FROM roster_team rt2
						WHERE rt2.roster_club_id = rc.roster_club_id
							AND rt2.deleted IS NULL
							AND (
								rt2.team_name ILIKE ${wcVal} OR
								rt2.organization_code ILIKE ${wcVal}
							)
					)
				)
			END
		) `;
	}

	query += `
		GROUP BY e.event_id, rc.roster_club_id
		ORDER BY club_name ASC
	`;

	if (offset) {
		query += `OFFSET ${sqlVars.useValue(offset)} `;
	}

	if (limit) {
		query += `LIMIT ${sqlVars.useValue(limit)} `;
	}

	return [query, sqlVars.getValues()];
};
