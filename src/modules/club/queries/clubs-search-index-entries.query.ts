import { ENTRY_STATUSES } from '@/shared/constants/team';
import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

type ClubsSearchIndexEntriesQueryParams = {
	eventKey: EventKey;
};

export const getClubsSearchIndexEntriesQuery = ({ eventKey }: ClubsSearchIndexEntriesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const query = `
		SELECT
			e.event_id,
			e.esw_id,
			rc.roster_club_id as club_id,
			COALESCE(rc.state, '') AS state,
			LOWER(
				rc.club_name || '|' || rc.code || '|' ||
				COALESCE(string_agg(rt.team_name, '|'), '') || '|' ||
				COALESCE(string_agg(COALESCE(rt.organization_code, ''), '|'), '')
			) AS search_content,
			row_number() OVER (PARTITION BY rc.event_id ORDER BY rc.club_name ASC) AS sort_order
		FROM
			event e
		JOIN roster_club rc
			ON rc.event_id = e.event_id
			AND rc.deleted IS NULL
		JOIN roster_team rt
			ON rt.roster_club_id = rc.roster_club_id
			AND rt.event_id = e.event_id
			AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
			AND rt.deleted IS NULL
		JOIN division d
			ON d.division_id = rt.division_id
			AND d.event_id = e.event_id
			AND d.published is TRUE
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
		GROUP BY e.event_id, rc.roster_club_id
	  ORDER BY club_name ASC
	`;

	return [query, sqlVars.getValues()];
};
