import { RedisClientType } from '@keyv/redis';
import { Module, DynamicModule } from '@nestjs/common';
import { EntityData, SchemaDefinition } from 'redis-om';

import { REDIS_CLIENT, REDIS_LOCK } from '@/infra/redis/constants';
import { RedisModule } from '@/infra/redis/redis.module';
import { RedisLockManager } from '@/infra/redis/utils/lockManager';

import { SearchEntityName } from './types';
import { RedisSearchManager } from './utils/redis-search-manager';
import { buildSearchManagerToken } from './utils/tokens';

@Module({})
export class SearchModule {
	static forFeature<Entry extends EntityData>(entityName: SearchEntityName, schemaDefinition: SchemaDefinition<Entry>): DynamicModule {
		const providerToken = buildSearchManagerToken(entityName);

		const provider = {
			provide: providerToken,
			useFactory: (redisClient: RedisClientType, redisLock: RedisLockManager) => {
				return new RedisSearchManager(entityName, schemaDefinition, redisClient, redisLock);
			},
			inject: [REDIS_CLIENT, REDIS_LOCK],
		};

		return {
			module: SearchModule,
			imports: [RedisModule],
			providers: [provider],
			exports: [provider],
		};
	}
}
