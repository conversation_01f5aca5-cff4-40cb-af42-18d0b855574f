import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Match } from '@/modules/match/entities';
import { MatchService } from '@/modules/match/match.service';

import { EventOfficialSchedule } from '../entities';

const MAX_BATCH_SIZE = 200; // Limit the amount of eventOfficialsSchedules that can be processed at once

@Injectable()
export class OfficialsSchedulesMatchesLoader {
	constructor(private matchService: MatchService) {}

	create(eventId: string): DataLoader<EventOfficialSchedule, Match | null> {
		return new DataLoader<EventOfficialSchedule, Match | null>(
			async (eventOfficialsSchedules) => {
				const matchesIds = eventOfficialsSchedules.map(({ match_id }) => match_id);
				const matchesMap = await this.matchService.fetchMatchesMap({ eventId, matchesIds });
				return matchesIds.map((matchId) => matchesMap[matchId]);
			},
			{ cache: false, name: 'OfficialsSchedulesMatchesLoader', maxBatchSize: MAX_BATCH_SIZE },
		);
	}
}
