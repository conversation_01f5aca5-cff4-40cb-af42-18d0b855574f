import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class EventOfficialSchedule {
	event_id: string;

	@Field(() => ID)
	schedule_id: string; // event_official_schedule_id

	@Field(() => ID)
	event_official_id: string;

	@Field(() => ID, { nullable: true })
	event_official_group_id?: string;

	@Field(() => ID)
	division_id: string;

	@Field(() => ID)
	match_id: string;

	@Field(() => String)
	match_name: string;

	@Field(() => Number, { nullable: true })
	match_start_time?: number;

	@Field(() => Boolean)
	published: boolean;

	@Field(() => ID, { nullable: true })
	court_id?: string;
}
