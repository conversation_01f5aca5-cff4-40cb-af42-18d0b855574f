import { Args, Context, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';

import { ProvidesData, DataProvidersParam } from '@/infra/dataSharing/decorators';
import { DataProviders, DataTypes } from '@/infra/dataSharing/types';
import { Match } from '@/modules/match/entities';
import { EventKey } from '@/shared/utils/event';

import { EventOfficialsSchedulesInputDto } from './dto/event-officials-schedules.dto';
import { EventOfficialSchedule } from './entities';
import { EventOfficialScheduleService } from './event-official-schedule.service';
import { OfficialsSchedulesMatchesLoader } from './loaders';

type EventOfficialScheduleResolverContext = {
	officialsSchedulesMatchesLoader: ReturnType<OfficialsSchedulesMatchesLoader['create']>;
};

@Resolver(() => EventOfficialSchedule)
export class EventOfficialScheduleResolver {
	constructor(
		private eventOfficialService: EventOfficialScheduleService,
		private officialsSchedulesMatchesLoader: OfficialsSchedulesMatchesLoader,
	) {}

	@Query(() => [EventOfficialSchedule])
	@ProvidesData(DataTypes.OfficialsSchedules)
	async eventOfficialsSchedules(
		@Args() args: EventOfficialsSchedulesInputDto,
		@DataProvidersParam() [schedulesProvider]: DataProviders<[DataTypes.OfficialsSchedules]>,
	): Promise<EventOfficialSchedule[]> {
		const schedules = await this.eventOfficialService.fetchEventOfficialsSchedules({
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
		schedulesProvider(schedules);
		return schedules;
	}

	@ResolveField(() => Match, { nullable: true })
	match(@Parent() eventOfficialSchedule: EventOfficialSchedule, @Context() context: EventOfficialScheduleResolverContext): Promise<Match> {
		if (!context.officialsSchedulesMatchesLoader) {
			context.officialsSchedulesMatchesLoader = this.officialsSchedulesMatchesLoader.create(eventOfficialSchedule.event_id);
		}
		return context.officialsSchedulesMatchesLoader.load(eventOfficialSchedule);
	}
}
