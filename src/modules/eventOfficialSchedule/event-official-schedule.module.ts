import { Module } from '@nestjs/common';

import { DataSharingModule } from '@/infra/dataSharing/dataSharing.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';
import { MatchModule } from '@/modules/match/match.module';

import { EventOfficialScheduleRepository } from './event-official-schedule.repository';
import { EventOfficialScheduleResolver } from './event-official-schedule.resolver';
import { EventOfficialScheduleService } from './event-official-schedule.service';
import { OfficialsSchedulesMatchesLoader } from './loaders';

@Module({
	imports: [PrismaModule, MatchModule, DataSharingModule],
	providers: [
		EventOfficialScheduleRepository,
		EventOfficialScheduleService,
		EventOfficialScheduleResolver,
		OfficialsSchedulesMatchesLoader,
	],
	exports: [EventOfficialScheduleService],
})
export class EventOfficialScheduleModule {}
