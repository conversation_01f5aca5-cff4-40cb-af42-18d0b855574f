import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToString, convertDateToTimeStamp, DateCastKeys, StringCastKeys } from '@/shared/utils/format';

import { EventOfficialSchedule } from './entities';
import { getEventOfficialsSchedulesQuery, EventOfficialsSchedulesQueryParams } from './queries/event-officials-schedules.query';

export type FetchEventOfficialsSchedulesParams = EventOfficialsSchedulesQueryParams;

const EVENT_OFFICIAL_SCHEDULE_STRING_CAST_FIELDS: StringCastKeys<EventOfficialSchedule>[] = [
	'event_id',
	'schedule_id',
	'event_official_id',
	'event_official_group_id',
	'division_id',
];
const EVENT_OFFICIAL_SCHEDULE_TIMESTAMP_CAST_FIELDS: DateCastKeys<EventOfficialSchedule>[] = ['match_start_time'];

@Injectable()
export class EventOfficialScheduleRepository {
	constructor(private prisma: PrismaService) {}

	async fetchEventOfficialsSchedules(params: FetchEventOfficialsSchedulesParams): Promise<EventOfficialSchedule[]> {
		const [query, values] = getEventOfficialsSchedulesQuery(params);
		const items = await this.prisma.$queryRawUnsafe<EventOfficialSchedule[]>(query, ...values);
		return this._formatEventOfficialScheduleFields(items);
	}

	private _formatEventOfficialScheduleFields(items: EventOfficialSchedule[]): EventOfficialSchedule[] {
		castFieldsToString(items, EVENT_OFFICIAL_SCHEDULE_STRING_CAST_FIELDS);
		convertDateToTimeStamp(items, EVENT_OFFICIAL_SCHEDULE_TIMESTAMP_CAST_FIELDS);
		return items;
	}
}
