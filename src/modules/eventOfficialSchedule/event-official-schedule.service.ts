import { Injectable } from '@nestjs/common';

import { EventOfficialSchedule } from './entities';
import { EventOfficialScheduleRepository, FetchEventOfficialsSchedulesParams } from './event-official-schedule.repository';

@Injectable()
export class EventOfficialScheduleService {
	constructor(private eventOfficialScheduleRepository: EventOfficialScheduleRepository) {}

	async fetchEventOfficialsSchedules(params: FetchEventOfficialsSchedulesParams): Promise<EventOfficialSchedule[]> {
		return this.eventOfficialScheduleRepository.fetchEventOfficialsSchedules(params);
	}
}
