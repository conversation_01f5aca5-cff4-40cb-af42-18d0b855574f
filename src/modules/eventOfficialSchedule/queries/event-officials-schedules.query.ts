import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type EventOfficialsSchedulesQueryParams = {
	eventKey: EventKey;
	eventOfficialsIds?: string[];
};

export const getEventOfficialsSchedulesQuery = ({
	eventKey,
	eventOfficialsIds,
}: EventOfficialsSchedulesQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	let query = `
		SELECT
			eos.event_id,
			eos.event_official_schedule_id schedule_id,
			eos.event_official_id,
			eos.event_official_group_id,
			eos.division_id,
			eos.match_name,
			eos.published,
			eos.court_id,
			eos.match_start_time,
			m.match_id
		FROM
			event e
		JOIN event_official_schedule eos
			ON eos.event_id = e.event_id
		JOIN matches m
			ON m.division_id = eos.division_id
			AND m.display_name = eos.match_name
			AND m.event_id = eos.event_id
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
	`;

	if (eventOfficialsIds?.length) {
		const eventOfficialsIdsValues = eventOfficialsIds.map((id) => sqlVars.useValue(Number(id)));
		query += `AND eos.event_official_id in (${eventOfficialsIdsValues.join(',')})`;
	}

	return [query, sqlVars.getValues()];
};
