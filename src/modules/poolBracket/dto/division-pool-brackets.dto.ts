import { ArgsType, Field, ID } from '@nestjs/graphql';

import { IsEventKeyValue } from '@/shared/graphql/dto/validators/is-event-key-value.validator';
import { IsNumericId } from '@/shared/graphql/dto/validators/is-numeric-id.validator';

@ArgsType()
export class DivisionPoolBracketsInputDto {
	@Field(() => ID)
	@IsEventKeyValue()
	eventKey: string;

	@Field(() => ID)
	@IsNumericId()
	divisionId: string;
}
