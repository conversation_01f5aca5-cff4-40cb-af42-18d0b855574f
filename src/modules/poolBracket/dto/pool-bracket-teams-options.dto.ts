import { ArgsType, Field, ID } from '@nestjs/graphql';
import { ArrayMaxSize, IsOptional } from 'class-validator';

import { IsNumericId } from '@/shared/graphql/dto/validators/is-numeric-id.validator';

@ArgsType()
export class PoolBracketTeamsOptionsInputDto {
	@Field(() => [ID], { nullable: true })
	@IsOptional()
	@ArrayMaxSize(10, { message: '"filterTeamsIds" must contain at most 10 teams' })
	@IsNumericId({ each: true, message: '"filterTeamsIds" must contain valid numeric IDs' })
	filterTeamsIds?: string[];
}
