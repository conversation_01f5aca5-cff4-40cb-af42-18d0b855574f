import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type MatchesPoolBracketsShortInfoQueryParams = {
	matchesIds: string[];
};

export const getMatchesPoolBracketsShortInfoQuery = ({ matchesIds }: MatchesPoolBracketsShortInfoQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const matchesIdsValues = matchesIds.map((id) => sqlVars.useValue(id));

	const query = `
		SELECT
			m.match_id,
			pb.uuid,
			pb.is_pool
		FROM 
			matches m
		JOIN poolbrackets pb
			ON pb.event_id = m.event_id
			AND pb.uuid = m.pool_bracket_id
		WHERE
			m.match_id in (${matchesIdsValues.map((id) => `${id}::uuid`).join(',')})
	`;

	return [query, sqlVars.getValues()];
};
