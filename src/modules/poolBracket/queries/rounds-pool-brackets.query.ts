import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type RoundsPoolBracketsQueryParams = {
	eventId: string;
	roundsIds: string[];
};

export const getRoundsPoolBracketsQuery = ({ eventId, roundsIds }: RoundsPoolBracketsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const roundsIdsValues = roundsIds.map((id) => sqlVars.useValue(id));

	const query = `
		SELECT 
			pb.uuid,
			pb.round_id,
			pb.group_id,
			pb.event_id,
			pb.division_id,
			pb.sort_priority,
			pb.is_pool,
			pb.name,
			pb.short_name,
			pb.display_name,
			pb.team_count,
			pb.match_count,
			pb.settings,
			pb.consolation,
			pb.flow_chart,
			pb.pb_stats,
			pb.pb_seeds,
			pb.pb_finishes,
			courts_matches.date_start AS date_start,
			courts_matches.courts
		FROM poolbrackets pb
		LEFT JOIN LATERAL (
			SELECT
			(
				SELECT EXTRACT(EPOCH FROM m.secs_start)::BIGINT * 1000
				FROM matches m
				WHERE 
					m.pool_bracket_id = pb.uuid
					AND m.event_id = pb.event_id
				ORDER BY m.secs_start, m.match_number
				LIMIT 1
			) AS date_start,
			(
				SELECT json_agg(jsonb_build_object('uuid', ordered_courts.uuid, 'short_name', ordered_courts.short_name))
				FROM 
				(
					SELECT DISTINCT c.uuid, c.short_name, c.sort_priority
					FROM matches m2
					JOIN courts c
						ON c.uuid = m2.court_id
						AND c.event_id = pb.event_id
					WHERE 
						m2.pool_bracket_id = pb.uuid
						AND m2.event_id = pb.event_id
					ORDER BY c.sort_priority
				) AS ordered_courts
			) AS courts
		) AS courts_matches ON true
		LEFT JOIN rounds r 
			ON r.uuid = pb.round_id 
			AND r.event_id = pb.event_id
		LEFT JOIN rounds rr 
			ON rr.uuid = pb.group_id 
			AND rr.event_id = pb.event_id
		WHERE
			pb.event_id = ${sqlVars.useValue(Number(eventId))}
			AND pb.round_id in (${roundsIdsValues.map((id) => `${id}::uuid`).join(',')})
		ORDER BY r.sort_priority, rr.sort_priority, pb.sort_priority
	`;

	return [query, sqlVars.getValues()];
};
