import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type TeamsOngoingPoolBracketsStatsQueryParams = {
	eventId: string;
	teamsIds: string[];
};

export const getTeamsOngoingPoolBracketsStatsQuery = ({
	eventId,
	teamsIds,
}: TeamsOngoingPoolBracketsStatsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();
	const teamsIdsValues = teamsIds.map((id) => sqlVars.useValue(Number(id)));

	const query = `
		SELECT DISTINCT ON (rt.roster_team_id)
			rt.roster_team_id,
			pb.uuid,
			pb.round_id,
			pb.group_id,
			pb.event_id,
			pb.division_id,
			pb.sort_priority,
			pb.is_pool,
			pb.name,
			pb.short_name,
			pb.display_name,
			pb.team_count,
			pb.match_count,
			pb.settings,
			pb.consolation,
			pb.flow_chart,
			pb.pb_stats,
			pb.pb_seeds,
			pb.pb_finishes,
			courts_matches.date_start AS date_start,
			courts_matches.courts
		FROM roster_team rt
		JOIN matches m
			ON m.event_id = rt.event_id
			AND m.division_id = rt.division_id
			AND (m.team1_roster_id = rt.roster_team_id OR m.team2_roster_id = rt.roster_team_id)
		JOIN poolbrackets pb
			ON pb.event_id = m.event_id
			AND pb.uuid = m.pool_bracket_id
		LEFT JOIN LATERAL (
			SELECT
			(
				SELECT EXTRACT(EPOCH FROM m.secs_start)::BIGINT * 1000
				FROM matches m
				WHERE
					m.pool_bracket_id = pb.uuid
					AND m.event_id = pb.event_id
				ORDER BY m.secs_start, m.match_number
				LIMIT 1
			) AS date_start,
			(
				SELECT json_agg(jsonb_build_object('uuid', ordered_courts.uuid, 'short_name', ordered_courts.short_name))
				FROM
				(
					SELECT DISTINCT c.uuid, c.short_name, c.sort_priority
					FROM matches m
					JOIN courts c
						ON c.uuid = m.court_id
						AND c.event_id = m.event_id
					WHERE
						m.pool_bracket_id = pb.uuid
						AND m.event_id = pb.event_id
					ORDER BY c.sort_priority
				) ordered_courts
			) AS courts
		) courts_matches ON true
		WHERE
			rt.event_id = ${sqlVars.useValue(Number(eventId))}
			AND rt.roster_team_id IN (${teamsIdsValues.join(',')})
			AND EXISTS (
				SELECT 1 FROM matches m2
				WHERE m2.pool_bracket_id = m.pool_bracket_id
				AND m2.secs_finished IS NULL
			)
		ORDER BY rt.roster_team_id, m.secs_start ASC
	`;

	return [query, sqlVars.getValues()];
};
