import { Field, ID, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PoolOrBracketStatItem {
	@Field(() => ID, { nullable: true })
	team_id: string;

	@Field({ nullable: true })
	name: string;

	@Field({ nullable: true })
	rank: number;

	@Field({ nullable: true })
	sets_pct: number;

	@Field({ nullable: true })
	sets_won: number;

	@Field({ nullable: true })
	sets_lost: number;

	@Field({ nullable: true })
	points_won: number;

	@Field({ nullable: true })
	matches_pct: number;

	@Field({ nullable: true })
	matches_won: number;

	@Field({ nullable: true })
	points_lost: number;

	@Field({ nullable: true })
	matches_lost: number;

	@Field({ nullable: true })
	points_ratio: number;
}
