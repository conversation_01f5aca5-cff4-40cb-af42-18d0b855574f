import { ObjectType, Field, ID, createUnionType, InterfaceType } from '@nestjs/graphql';

import { CourtShortInfo } from '@/modules/court/entities';
import { TeamAdvancement } from '@/shared/graphql/entities/team-advancement.entity';

import { PoolOrBracketSeedItem } from './pool-bracket-seed-item.entity';
import { PoolOrBracketSettings } from './pool-bracket-settings.entity';
import { PoolOrBracketShortInfoInterface } from './pool-bracket-short-info.entity';
import { PoolOrBracketStatItem } from './pool-bracket-stat-item.entity';

@ObjectType()
class PoolOrBracketExternal {
	@Field(() => [CourtShortInfo])
	courts_short_info: CourtShortInfo[];
}

@InterfaceType()
export abstract class PoolOrBracketBase extends PoolOrBracketShortInfoInterface {
	event_id: string;

	@Field(() => ID)
	round_id: string;

	@Field(() => ID, { nullable: true })
	group_id?: string;

	@Field(() => ID)
	division_id: string;

	@Field()
	sort_priority: number;

	@Field()
	team_count: number;

	@Field()
	match_count: number;

	@Field({ nullable: true })
	name?: string;

	@Field({ nullable: true })
	short_name?: string;

	@Field({ nullable: true })
	display_name?: string;

	@Field({ nullable: true })
	court_short_name?: string;

	@Field({ nullable: true })
	date_start?: number;

	@Field({ nullable: true })
	consolation?: number;

	@Field(() => [PoolOrBracketStatItem])
	pb_stats: PoolOrBracketStatItem[];

	@Field(() => [PoolOrBracketSeedItem])
	pb_seeds: PoolOrBracketSeedItem[];

	@Field(() => [TeamAdvancement])
	pb_finishes: TeamAdvancement[];

	@Field(() => PoolOrBracketSettings, { nullable: true })
	settings?: PoolOrBracketSettings;

	@Field(() => PoolOrBracketExternal)
	external: PoolOrBracketExternal;
}

@ObjectType({ implements: () => PoolOrBracketBase })
export class Pool extends PoolOrBracketBase {
	is_pool = 1;
}

@ObjectType({ implements: () => PoolOrBracketBase })
export class Bracket extends PoolOrBracketBase {
	is_pool = 0;
	@Field({ nullable: true })
	flow_chart?: string;
}

export const PoolOrBracket = createUnionType({
	name: 'PoolOrBracket',
	types: () => [Pool, Bracket] as const,
	resolveType(value) {
		if ('is_pool' in value) {
			return value.is_pool ? Pool : Bracket;
		}
		return null;
	},
});
