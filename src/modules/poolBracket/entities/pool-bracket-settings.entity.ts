import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PoolOrBracketSettings {
	@Field({ nullable: true })
	DoubleRR: boolean;

	@Field({ nullable: true })
	Duration: number;

	@Field({ nullable: true })
	NoWinner: boolean;

	@Field({ nullable: true })
	SetCount: number;

	@Field({ nullable: true })
	FinalPoints: number;

	@Field({ nullable: true })
	PlayAllSets: boolean;

	@Field({ nullable: true })
	WinningPoints: number;
}
