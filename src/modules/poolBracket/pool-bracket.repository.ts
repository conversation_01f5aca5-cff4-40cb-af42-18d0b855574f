import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { CourtShortInfo } from '@/modules/court/entities';
import { TeamAdvancement } from '@/shared/graphql/entities/team-advancement.entity';
import { keyBy } from '@/shared/utils/collection';
import { castFieldsToString, castFieldsToNumber, StringCastKeys, NumberCastKeys } from '@/shared/utils/format';
import { parseWithFallback } from '@/shared/utils/json';

import { PoolOrBracket, PoolOrBracketSeedItem, PoolOrBracketShortInfo, PoolOrBracketStatItem } from './entities';
import {
	getRoundsPoolBracketsQuery,
	getDivisionPoolBracketsQuery,
	getMatchesPoolBracketsShortInfoQuery,
	getTeamsOngoingPoolBracketsStatsQuery,
	RoundsPoolBracketsQueryParams,
	DivisionPoolBracketsQueryParams,
	MatchesPoolBracketsShortInfoQueryParams,
	TeamsOngoingPoolBracketsStatsQueryParams,
} from './queries';

export type FetchRoundsPoolBracketsParams = RoundsPoolBracketsQueryParams;
export type FetchDivisionPoolBracketsParams = DivisionPoolBracketsQueryParams;
export type FetchMatchesPoolBracketsShortInfoParams = MatchesPoolBracketsShortInfoQueryParams;
export type FetchTeamsOngoingPoolBracketsParams = TeamsOngoingPoolBracketsStatsQueryParams;

const POOL_BRACKET_STRING_CAST_FIELDS: StringCastKeys<typeof PoolOrBracket>[] = ['event_id', 'division_id'];
const POOL_BRACKET_NUMBER_CAST_FIELDS: NumberCastKeys<typeof PoolOrBracket>[] = ['date_start'];

@Injectable()
export class PoolBracketRepository {
	constructor(private prisma: PrismaService) {}

	async fetchRoundsPoolBrackets(params: FetchRoundsPoolBracketsParams): Promise<(typeof PoolOrBracket)[]> {
		const [query, values] = getRoundsPoolBracketsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<(typeof PoolOrBracket)[]>(query, ...values);
		return this._formatPoolBracketFields(items);
	}

	async fetchDivisionPoolBrackets(params: FetchDivisionPoolBracketsParams): Promise<(typeof PoolOrBracket)[]> {
		const [query, values] = getDivisionPoolBracketsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<(typeof PoolOrBracket)[]>(query, ...values);
		return this._formatPoolBracketFields(items);
	}

	async fetchMatchesPoolBracketsShortInfoMap(
		params: FetchMatchesPoolBracketsShortInfoParams,
	): Promise<Record<string, PoolOrBracketShortInfo>> {
		const [query, values] = getMatchesPoolBracketsShortInfoQuery(params);
		const items = await this.prisma.$queryRawUnsafe<(PoolOrBracketShortInfo & { match_id: string })[]>(query, ...values);
		this._formatPoolBracketShortInfoField(items);
		return keyBy(items, 'match_id');
	}

	async fetchTeamsOngoingPoolBracketsMap(params: FetchTeamsOngoingPoolBracketsParams): Promise<Record<string, typeof PoolOrBracket>> {
		const [query, values] = getTeamsOngoingPoolBracketsStatsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<(typeof PoolOrBracket & { roster_team_id: string })[]>(query, ...values);
		this._formatPoolBracketFields(items);
		return keyBy(items, 'roster_team_id');
	}

	private _formatPoolBracketShortInfoField<T extends PoolOrBracketShortInfo>(poolBracketsShortInfo: T[]): T[] {
		// Nothing's to format here for now
		return poolBracketsShortInfo;
	}

	private _formatPoolBracketFields(poolBrackets: (typeof PoolOrBracket)[]): (typeof PoolOrBracket)[] {
		castFieldsToString(poolBrackets, POOL_BRACKET_STRING_CAST_FIELDS);
		castFieldsToNumber(poolBrackets, POOL_BRACKET_NUMBER_CAST_FIELDS);
		poolBrackets.forEach((poolBracket) => {
			this._formatPoolBracketStats(poolBracket);
			this._formatPoolBracketSeeds(poolBracket);
			this._formatPoolBracketFinishes(poolBracket);
			this._formatPoolBracketExternal(poolBracket);
			this._cleanPoolBracketIntermediateFields(poolBracket);
		});
		return poolBrackets;
	}

	private _formatPoolBracketStats<T extends Pick<typeof PoolOrBracket, 'pb_stats'>>(poolBracket: T): void {
		const parsedStats = parseWithFallback<Record<string, PoolOrBracketStatItem>>(poolBracket.pb_stats as unknown as string, {});
		// Convert to array of PoolOrBracketStatItem objects sorted by key
		poolBracket.pb_stats = Object.entries(parsedStats)
			.sort(([a], [b]) => a.localeCompare(b))
			.map(([, value]) => value);
	}

	private _formatPoolBracketSeeds(poolBracket: typeof PoolOrBracket): void {
		const parsedSeeds = parseWithFallback<Record<string, string>>(poolBracket.pb_seeds as unknown as string, {});
		poolBracket.pb_seeds = Object.entries(parsedSeeds)
			.sort(([a], [b]) => a.localeCompare(b))
			.map(([, value]) => parseWithFallback<PoolOrBracketSeedItem>(value, {}));
	}

	private _formatPoolBracketFinishes(poolBracket: typeof PoolOrBracket): void {
		const parsedFinishes = parseWithFallback<Record<string, TeamAdvancement>>(poolBracket.pb_finishes as unknown as string, {});
		// Convert to array of TeamProgression objects sorted by key
		delete parsedFinishes.team_ids;
		poolBracket.pb_finishes = Object.entries(parsedFinishes)
			.sort(([a], [b]) => a.localeCompare(b))
			.map(([, value]) => this._formatTeamAdvancement(value));
	}

	private _formatTeamAdvancement(teamAdvancement: TeamAdvancement): TeamAdvancement {
		// TODO Make this logic model-related, it should be a part of the model during the future refactor
		const { next_match, next_ref } = teamAdvancement;
		teamAdvancement.next_match = next_match && Object.keys(next_match).length ? next_match : undefined;
		teamAdvancement.next_ref = next_ref && Object.keys(next_ref).length ? next_ref : undefined;
		return teamAdvancement;
	}

	private _formatPoolBracketExternal(poolBracket: typeof PoolOrBracket): void {
		const { courts } = poolBracket as unknown as Record<string, CourtShortInfo[]>;
		poolBracket.external = { courts_short_info: courts || [] };
	}

	private _cleanPoolBracketIntermediateFields(poolBracket: typeof PoolOrBracket): void {
		const castedPoolBracket = poolBracket as unknown as Record<string, unknown>;
		delete castedPoolBracket.courts;
	}
}
