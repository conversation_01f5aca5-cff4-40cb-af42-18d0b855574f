import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Match } from '@/modules/match/entities';
import { MatchService } from '@/modules/match/match.service';

import { PoolOrBracket } from '../entities';

@Injectable()
export class PoolBracketsMatchesLoader {
	constructor(private matchService: MatchService) {}

	create(eventId: string): DataLoader<typeof PoolOrBracket, Match[]> {
		return new DataLoader<typeof PoolOrBracket, Match[]>(
			async (poolBrackets: (typeof PoolOrBracket)[]) => {
				const poolBracketsIds = poolBrackets.map((poolBracket) => poolBracket.uuid);
				const poolBracketsMatchesMap = await this.matchService.fetchPoolBracketsMatchesMap({ eventId, poolBracketsIds });
				return poolBrackets.map(({ uuid }) => poolBracketsMatchesMap[uuid]);
			},
			{ cache: false, name: 'PoolBracketsMatchesLoader' },
		);
	}
}
