import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { Team } from '@/modules/team/entities';
import { TeamService } from '@/modules/team/team.service';

import { PoolOrBracket } from '../entities';

@Injectable()
export class PoolBracketsTeamsLoader {
	constructor(private teamService: TeamService) {}

	create(eventId: string): DataLoader<typeof PoolOrBracket, Team[]> {
		return new DataLoader<typeof PoolOrBracket, Team[]>(
			async (poolBrackets: (typeof PoolOrBracket)[]) => {
				const poolBracketsIds = poolBrackets.map((poolBracket) => poolBracket.uuid);
				const poolBracketsTeamsMap = await this.teamService.fetchPoolBracketsTeamsMap({ eventId, poolBracketsIds });
				return poolBrackets.map(({ uuid }) => poolBracketsTeamsMap[uuid]);
			},
			{ cache: false, name: 'PoolBracketsTeamsLoader' },
		);
	}
}
