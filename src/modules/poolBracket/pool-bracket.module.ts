import { Module, forwardRef } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';
import { MatchModule } from '@/modules/match/match.module';
import { TeamModule } from '@/modules/team/team.module';

import { PoolBracketsMatchesLoader, PoolBracketsTeamsLoader } from './loaders';
import { PoolBracketRepository } from './pool-bracket.repository';
import { PoolBracketResolver } from './pool-bracket.resolver';
import { PoolBracketService } from './pool-bracket.service';

@Module({
	imports: [PrismaModule, forwardRef(() => TeamModule), MatchModule, CacheModule],
	providers: [PoolBracketResolver, PoolBracketService, PoolBracketRepository, PoolBracketsMatchesLoader, PoolBracketsTeamsLoader],
	exports: [PoolBracketService],
})
export class PoolBracketModule {}
