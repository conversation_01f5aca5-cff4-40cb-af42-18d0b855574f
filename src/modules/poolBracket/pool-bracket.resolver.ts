import { Args, Context, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { TeamsPoolBrackets } from 'shared/graphql/context';

import { Match } from '@/modules/match/entities';
import { Team } from '@/modules/team/entities';
import { EventKey } from '@/shared/utils/event';

import { DivisionPoolBracketsInputDto, PoolBracketTeamsOptionsInputDto } from './dto';
import { PoolOrBracket, PoolOrBracketBase } from './entities';
import { PoolBracketsMatchesLoader, PoolBracketsTeamsLoader } from './loaders';
import { PoolBracketService } from './pool-bracket.service';

type ClubResolverContext = {
	pollBracketsMatchesLoader: ReturnType<PoolBracketsMatchesLoader['create']>;
	pollBracketsTeamsLoader: ReturnType<PoolBracketsTeamsLoader['create']>;
} & TeamsPoolBrackets;

@Resolver(() => PoolOrBracketBase)
export class PoolBracketResolver {
	constructor(
		private poolBracketService: PoolBracketService,
		private pollBracketsMatchesLoader: PoolBracketsMatchesLoader,
		private pollBracketsTeamsLoader: PoolBracketsTeamsLoader,
	) {}

	@Query(() => [PoolOrBracket])
	divisionPoolBrackets(@Args() args: DivisionPoolBracketsInputDto): Promise<(typeof PoolOrBracket)[]> {
		return this.poolBracketService.fetchDivisionPoolBrackets({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@ResolveField(() => [Team])
	async teams(
		@Parent() pollOrBracket: PoolOrBracketBase,
		@Context() context: ClubResolverContext,
		@Args() { filterTeamsIds }: PoolBracketTeamsOptionsInputDto,
	): Promise<Team[]> {
		if (!context.pollBracketsTeamsLoader) {
			context.pollBracketsTeamsLoader = this.pollBracketsTeamsLoader.create(pollOrBracket.event_id);
		}
		if (!context.teamsPoolBrackets) {
			context.teamsPoolBrackets = new Map();
		}
		const teams = await context.pollBracketsTeamsLoader.load(pollOrBracket);

		if (filterTeamsIds?.length) {
			return teams.reduce((acc, team) => {
				if (filterTeamsIds.includes(team.team_id)) {
					context.teamsPoolBrackets.set(team, pollOrBracket);
					acc.push(team);
				}
				return acc;
			}, []);
		} else {
			teams.forEach((team) => context.teamsPoolBrackets.set(team, pollOrBracket));
			return teams;
		}
	}

	@ResolveField(() => [Match])
	matches(@Parent() pollOrBracket: PoolOrBracketBase, @Context() context: ClubResolverContext): Promise<Match[]> {
		if (!context.pollBracketsMatchesLoader) {
			context.pollBracketsMatchesLoader = this.pollBracketsMatchesLoader.create(pollOrBracket.event_id);
		}
		return context.pollBracketsMatchesLoader.load(pollOrBracket);
	}
}
