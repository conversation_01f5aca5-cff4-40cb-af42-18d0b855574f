import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

export type CourtsQueryParams = {
	eventKey: EventKey;
	courtsIds?: string[];
};

export const getCourtsQuery = ({ eventKey, courtsIds }: CourtsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	let query = `
		SELECT 
			c.event_id,
			c.uuid,
			c.sort_priority,
			c.name,
			c.short_name
		FROM
			event e
		JOIN courts c
			ON c.event_id = e.event_id
		WHERE
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
	`;

	if (courtsIds?.length) {
		const courtsIdsValues = courtsIds.map((id) => sqlVars.useValue(id));
		query += `AND uuid IN (${courtsIdsValues.map((id) => `${id}::uuid`).join(',')})`;
	}

	query += ' ORDER BY c.sort_priority';

	return [query, sqlVars.getValues()];
};
