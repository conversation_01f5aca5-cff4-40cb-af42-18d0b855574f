import { Modu<PERSON> } from '@nestjs/common';

import { CacheModule } from '@/infra/cache/cache.module';
import { DataSharingModule } from '@/infra/dataSharing/dataSharing.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { CourtRepository } from './court.repository';
import { CourtResolver } from './court.resolver';
import { CourtService } from './court.service';

@Module({
	imports: [PrismaModule, CacheModule, DataSharingModule],
	providers: [CourtResolver, CourtService, CourtRepository],
})
export class CourtModule {}
