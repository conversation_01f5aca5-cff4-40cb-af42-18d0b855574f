import { Args, Query, Resolver } from '@nestjs/graphql';

import { ConsumesData, ProvidedDataParam } from '@/infra/dataSharing/decorators';
import { ProvidedData, DataTypes } from '@/infra/dataSharing/types';
import { EventKey } from '@/shared/utils/event';

import { CourtService } from './court.service';
import { CourtsInputDto } from './dto/courts.dto';
import { Court } from './entities';

@Resolver(() => Court)
export class CourtResolver {
	constructor(private readonly courtService: CourtService) {}

	@Query(() => [Court])
	courts(@Args() args: CourtsInputDto): Promise<Court[]> {
		return this.courtService.fetchCourts({
			...args,
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@Query(() => [Court])
	@ConsumesData(DataTypes.Matches, DataTypes.OfficialsSchedules)
	async relatedCourts(
		/**
		 * The `uniqKey` argument is used to generate a unique query signature at the GraphQL level,
		 * ensuring Apollo Client properly caches `relatedCourts` without requiring explicit parameters.
		 * Note: `uniqKey` is ignored by the resolver but serves as a crucial identifier for caching.
		 */
		@Args({ name: 'uniqKey', type: () => String, nullable: true }) _: string | undefined,
		@ProvidedDataParam(DataTypes.Matches) providedMatches: ProvidedData<DataTypes.Matches>,
		@ProvidedDataParam(DataTypes.OfficialsSchedules) providedSchedules: ProvidedData<DataTypes.OfficialsSchedules>,
	): Promise<Court[]> {
		const [matchesEventId, matchesCourtsIdsSet] = this._collectCourtsIds(providedMatches);
		const [schedulesEventId, schedulesCourtsIdsSet] = this._collectCourtsIds(providedSchedules);

		// The event ID should be the same for both matches and schedules, take the first one
		const eventId = matchesEventId || schedulesEventId;
		const courtsIdsSet = new Set([...matchesCourtsIdsSet, ...schedulesCourtsIdsSet]);
		if (!eventId || !courtsIdsSet.size) return [];

		return this.courtService.fetchCourtsByIds({ courtsIds: Array.from(courtsIdsSet), eventKey: EventKey.fromKeyValue(eventId) });
	}

	private _collectCourtsIds<T extends { event_id?: string; court_id?: string }>(
		providedData: (T[] | undefined)[],
	): [eventId: string, courtsIds: Set<string>] {
		let eventId = '';
		const courtsIds = new Set<string>();

		providedData.forEach((list) => {
			if (!list?.length) return;
			if (!eventId) eventId = list[0].event_id;
			list.forEach((item) => item.court_id && courtsIds.add(item.court_id));
		});

		return [eventId, courtsIds];
	}
}
