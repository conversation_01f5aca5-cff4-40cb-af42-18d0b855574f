import { Injectable } from '@nestjs/common';

import { ExtendedCacheService, ItemCacheKeyConfig, ItemsCacheKeyConfig } from '@/infra/cache/extended-cache.service';
import { CacheCategories, CacheCommonKeys, CacheScopes } from '@/shared/constants/cache';
import { keyBy } from '@/shared/utils/collection';
import { completeObject } from '@/shared/utils/object';

import { CourtRepository, FetchCourtsParams } from './court.repository';
import { Court } from './entities';

export type FetchCourtsServiceParams = Pick<FetchCourtsParams, 'eventKey'>;
export type FetchCourtsByIdsServiceParams = Required<FetchCourtsParams>;

@Injectable()
export class CourtService {
	constructor(private courtRepository: CourtRepository, private cacheService: ExtendedCacheService) {}

	async fetchCourts(params: FetchCourtsServiceParams): Promise<Court[]> {
		return this.cacheService.getOrFetch<Court[]>(this._getCourtsCacheKeyConfig(params), () => this.courtRepository.fetchCourts(params));
	}

	async fetchCourtsByIds(params: FetchCourtsByIdsServiceParams): Promise<Court[]> {
		const courts = await this.cacheService.getOrFetchMany<Court | null>(
			this._getCourtsByIdsCacheKeyConfig(params),
			params.courtsIds,
			(missingIds) => {
				return this._getCourtsByIdsData({ ...params, courtsIds: missingIds });
			},
		);
		return Object.values(courts)
			.filter(Boolean)
			.sort((a, b) => a.sort_priority - b.sort_priority);
	}

	private async _getCourtsByIdsData(params: FetchCourtsParams): Promise<Record<string, Court | null>> {
		const courts = await this.courtRepository.fetchCourts(params);
		const courtsMap = keyBy(courts, 'uuid');
		return completeObject(params.courtsIds, courtsMap, null);
	}

	private _getCourtsCacheKeyConfig(params: FetchCourtsServiceParams): ItemCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.Court,
			categoryKey: CacheCommonKeys.All,
		};
	}

	private _getCourtsByIdsCacheKeyConfig(params: FetchCourtsByIdsServiceParams): ItemsCacheKeyConfig {
		return {
			scope: CacheScopes.Event,
			scopeKey: params.eventKey.value,
			category: CacheCategories.Court,
		};
	}
}
