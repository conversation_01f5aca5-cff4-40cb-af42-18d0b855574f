import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';

import { Court } from './entities';
import { getCourtsQuery, CourtsQueryParams } from './queries/courts.query';

export type FetchCourtsParams = CourtsQueryParams;

const COURT_STRING_CAST_FIELDS: StringCastKeys<Court>[] = ['event_id'];

@Injectable()
export class CourtRepository {
	constructor(private prisma: PrismaService) {}

	async fetchCourts(params: FetchCourtsParams): Promise<Court[]> {
		const [query, values] = getCourtsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<Court[]>(query, ...values);
		return this._formatCourtsFields(items);
	}

	private _formatCourtsFields(courts: Court[]): Court[] {
		castFieldsToString(courts, COURT_STRING_CAST_FIELDS);
		return courts;
	}
}
