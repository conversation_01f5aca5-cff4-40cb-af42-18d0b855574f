import { EventKey } from '@/shared/utils/event';
import { RawSqlValue, RawSqlVarHelper } from '@/shared/utils/sql';

import { EVENT_OFFICIAL_WORK_STATUS } from '../constants';

export type EventOfficialsQueryParams = {
	eventKey: EventKey;
};

export const getEventOfficialsQuery = ({ eventKey }: EventOfficialsQueryParams): [string, RawSqlValue[]] => {
	const sqlVars = new RawSqlVarHelper();

	const query = `
		SELECT 
			e.event_id,
			eof.event_official_id,
			eof.official_id,
			eof.schedule_name,
			eof.additional_restrictions, 
			eof.departure_time, 
			eof.schedule_availability, 
			oar.name official_additional_role,
			o.user_id,
			o.rank,
			u.first,
			u.last
		FROM 
			event e
		JOIN event_official eof
			ON eof.event_id = e.event_id
		JOIN official o
			ON o.official_id = eof.official_id
		JOIN "user" u
			ON u.user_id = o.user_id
		LEFT JOIN event_official_additional_role eoar
			ON eoar.event_official_id = eof.event_official_id
		LEFT JOIN official_additional_role AS oar
			ON oar.official_additional_role_id = eoar.official_additional_role_id
		WHERE 
			${eventKey.isESWID ? `e.esw_id = ${sqlVars.useValue(eventKey.value)}` : `e.event_id = ${sqlVars.useValue(Number(eventKey.value))}`}
			AND eof.work_status = '${EVENT_OFFICIAL_WORK_STATUS.APPROVED}'
			AND eof.is_official = TRUE 
			AND eof.deleted IS NULL
			AND o.rank IS NOT NULL
			AND u.deleted_at IS NULL
	`;

	return [query, sqlVars.getValues()];
};
