import { Injectable } from '@nestjs/common';
import * as DataLoader from 'dataloader';

import { EventOfficialSchedule } from '@/modules/eventOfficialSchedule/entities';
import { EventOfficialScheduleService } from '@/modules/eventOfficialSchedule/event-official-schedule.service';
import { EventKey } from '@/shared/utils/event';

import { EventOfficial } from '../entities';

const MAX_BATCH_SIZE = 200; // Limit the amount of eventOfficials that can be processed at once

@Injectable()
export class EventOfficialsSchedulesLoader {
	constructor(private eventOfficialScheduleService: EventOfficialScheduleService) {}

	create(eventKey: EventKey): DataLoader<EventOfficial, EventOfficialSchedule[]> {
		return new DataLoader<EventOfficial, EventOfficialSchedule[]>(
			async (eventOfficials) => {
				const eventOfficialsIds = eventOfficials.map((eventOfficial) => eventOfficial.event_official_id);
				const eventOfficialsSchedules = await this.eventOfficialScheduleService.fetchEventOfficialsSchedules({
					eventKey,
					eventOfficialsIds,
				});
				return eventOfficialsIds.map((id) =>
					eventOfficialsSchedules.filter((eventOfficialSchedule) => eventOfficialSchedule.event_official_id === id),
				);
			},
			{ cache: false, name: 'EventOfficialsSchedulesLoader', maxBatchSize: MAX_BATCH_SIZE },
		);
	}
}
