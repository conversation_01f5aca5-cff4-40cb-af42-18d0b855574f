import { Args, Context, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';

import { EventOfficialSchedule } from '@/modules/eventOfficialSchedule/entities';
import { EventKey } from '@/shared/utils/event';

import { EventOfficialsInputDto } from './dto/event-officials.dto';
import { EventOfficial } from './entities';
import { EventOfficialService } from './event-official.service';
import { EventOfficialsSchedulesLoader } from './loaders';

type EventOfficialResolverContext = {
	eventOfficialsSchedulesLoader: ReturnType<EventOfficialsSchedulesLoader['create']>;
};

@Resolver(() => EventOfficial)
export class EventOfficialResolver {
	constructor(private eventOfficialService: EventOfficialService, private eventOfficialsSchedulesLoader: EventOfficialsSchedulesLoader) {}

	@Query(() => [EventOfficial])
	eventOfficials(@Args() args: EventOfficialsInputDto): Promise<EventOfficial[]> {
		return this.eventOfficialService.fetchEventOfficials({
			eventKey: EventKey.fromKeyValue(args.eventKey),
		});
	}

	@ResolveField(() => [EventOfficialSchedule])
	schedules(@Parent() eventOfficial: EventOfficial, @Context() context: EventOfficialResolverContext): Promise<EventOfficialSchedule[]> {
		if (!context.eventOfficialsSchedulesLoader) {
			context.eventOfficialsSchedulesLoader = this.eventOfficialsSchedulesLoader.create(EventKey.fromKeyValue(eventOfficial.event_id));
		}
		return context.eventOfficialsSchedulesLoader.load(eventOfficial);
	}
}
