import { Injectable } from '@nestjs/common';

import { EventOfficial } from './entities';
import { EventOfficialRepository, FetchEventOfficialsParams } from './event-official.repository';

@Injectable()
export class EventOfficialService {
	constructor(private eventOfficialRepository: EventOfficialRepository) {}

	async fetchEventOfficials(params: FetchEventOfficialsParams): Promise<EventOfficial[]> {
		return this.eventOfficialRepository.fetchEventOfficials(params);
	}
}
