import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/infra/prisma/prisma.service';
import { UserShortInfo } from '@/shared/graphql/entities/user-short-info.entity';
import { castFieldsToString, StringCastKeys } from '@/shared/utils/format';
import { stripKeysInPlace } from '@/shared/utils/object';

import { EventOfficial, OfficialShortInfo } from './entities';
import { getEventOfficialsQuery, EventOfficialsQueryParams } from './queries/event-officials.query';

export type FetchEventOfficialsParams = EventOfficialsQueryParams;

const EVENT_OFFICIAL_STRING_CAST_FIELDS: StringCastKeys<EventOfficial>[] = ['event_id', 'official_id', 'event_official_id'];
const EVENT_OFFICIAL_INTERMEDIATE_FIELDS: string[] = ['user_id', 'rank', 'first', 'last'];

@Injectable()
export class EventOfficialRepository {
	constructor(private prisma: PrismaService) {}

	async fetchEventOfficials(params: FetchEventOfficialsParams): Promise<EventOfficial[]> {
		const [query, values] = getEventOfficialsQuery(params);
		const items = await this.prisma.$queryRawUnsafe<EventOfficial[]>(query, ...values);
		return this._formatEventOfficialFields(items);
	}

	private _formatEventOfficialFields(items: EventOfficial[]): EventOfficial[] {
		castFieldsToString(items, EVENT_OFFICIAL_STRING_CAST_FIELDS);
		items.forEach((item) => {
			this._formatEventOfficialExternalFields(item);
			this._cleanupEventOfficialIntermediateFields(item);
		});
		return items;
	}

	private _formatEventOfficialExternalFields(eventOfficial: EventOfficial): EventOfficial {
		const { official_id, rank } = eventOfficial as unknown as OfficialShortInfo;
		const { user_id: userId, first, last } = eventOfficial as unknown as UserShortInfo;
		const user_id = String(userId);
		eventOfficial.external = {
			official_info: {
				official_id,
				user_id,
				rank,
			},
			user_info: {
				user_id,
				first,
				last,
			},
		};
		return eventOfficial;
	}

	private _cleanupEventOfficialIntermediateFields(eventOfficial: object): void {
		stripKeysInPlace(eventOfficial, EVENT_OFFICIAL_INTERMEDIATE_FIELDS);
	}
}
