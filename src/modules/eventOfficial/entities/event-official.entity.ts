import { ObjectType, Field, ID } from '@nestjs/graphql';

import { EventOfficialExternal } from './event-official-external.entity';

@ObjectType()
export class EventOfficial {
	event_id: string;

	@Field(() => ID)
	event_official_id: string;

	@Field(() => ID)
	official_id: string;

	@Field(() => String, { nullable: true })
	schedule_name?: string;

	@Field(() => String, { nullable: true })
	additional_restrictions?: string;

	@Field(() => String, { nullable: true })
	departure_time?: string;

	@Field(() => String, { nullable: true })
	official_additional_role?: string;

	// TODO Return this field as soon as Object type will be implemented
	// @Field(() => String, { nullable: true })
	// schedule_availability?: string;

	@Field(() => EventOfficialExternal)
	external: EventOfficialExternal;
}
