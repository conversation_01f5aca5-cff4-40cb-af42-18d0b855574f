import { Module } from '@nestjs/common';

import { PrismaModule } from '@/infra/prisma/prisma.module';
import { EventOfficialScheduleModule } from '@/modules/eventOfficialSchedule/event-official-schedule.module';

import { EventOfficialRepository } from './event-official.repository';
import { EventOfficialResolver } from './event-official.resolver';
import { EventOfficialService } from './event-official.service';
import { EventOfficialsSchedulesLoader } from './loaders';

@Module({
	imports: [PrismaModule, EventOfficialScheduleModule],
	providers: [EventOfficialResolver, EventOfficialRepository, EventOfficialService, EventOfficialsSchedulesLoader],
	exports: [EventOfficialService],
})
export class EventOfficialModule {}
