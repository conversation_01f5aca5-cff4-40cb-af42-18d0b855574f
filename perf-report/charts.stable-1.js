const chartDom = document.getElementById('main');
const myChart = echarts.init(chartDom);
let option;

// Example data structure:
const data = {
	GQL_GetDivisionQualifiedTeamsQuery,
	GQL_GetDivisionRoundsPoolBracketsQuery,
	GQL_GetDivisionTeamsStandingQuery,
	GQL_GetEventCountsQuery,
	GQL_GetEventDetailsQuery,
	GQL_GetPaginatedAthletesQuery,
	GQL_GetPaginatedAthletesQuery_Search,
	GQL_GetPaginatedClubsTeamsQuery,
	GQL_GetPaginatedClubsTeamsQuery_Search,
	GQL_GetPaginatedDivisionTeamsQuery,
	GQL_GetPaginatedDivisionTeamsQuery_Search,
	GQL_GetPaginatedStaffQuery,
	GQL_GetPaginatedStaffQuery_Search,
};

const categories = Object.keys(data);
const legendData = GQL_GetDivisionQualifiedTeamsQuery.map((item) => item.scenario);
const xAxisData = categories;

const series = [];
legendData.forEach((scenario, index) => {
	const items = [];
	const minItems = [];
	Object.values(data).forEach((categoryData) => {
		const record = categoryData.find((item) => item.scenario === scenario);
		const { min, max, med, p90 } = record;
		// minItems.push(record.min);
		items.push(record.p90);
		// items.push([med, p90, min, max]);
	});
	series.push({
		type: 'bar',
		animation: false,
		name: scenario,
		// stack: 'min' + index,
		data: items,
	});
});
console.log('series:', series);

option = {
	grid: {
		left: 50, // Add space on the left
		right: 50, // Add space on the right
		bottom: 340, // Add space at the bottom
		top: 150,
	},
	tooltip: {
		trigger: 'axis',
	},
	legend: {
		data: legendData,
	},
	// dataZoom: [
	// 	{
	// 		type: 'slider',
	// 		start: 0,
	// 		end: 100,
	// 	},
	// 	{
	// 		type: 'inside',
	// 		start: 50,
	// 		end: 70,
	// 	},
	// ],
	xAxis: {
		type: 'category',
		data: xAxisData,
		boundaryGap: true,
		axisLabel: {
			rotate: 90,
			margin: 10,
			verticalAlign: 'center',
			textStyle: {
				fontWeight: 'bold',
				fontSize: 14,
			},
		},
		offset: 0,
		splitArea: {
			show: true,
		},
		splitLine: {
			show: false,
		},
		// name: 'X Axis',
		// axisLine: { onZero: true },
		// splitLine: { show: false },
		// splitArea: { show: false },
	},
	yAxis: {},
	series,
};
