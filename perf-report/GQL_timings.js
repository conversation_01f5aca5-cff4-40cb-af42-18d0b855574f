/********************************************************************************************
 * GQL_GetDivisionQualifiedTeamsQuery
 ********************************************************************************************/
const GQL_GetDivisionQualifiedTeamsQuery = [
	// 1) No Sentry, NodeJS 20, no PM2, vus=80
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 30.39,
		min: 4.98,
		med: 10.66,
		max: 312.2,
		p90: 56.05,
		p95: 147.75,
	},
	// 2) Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 923.34,
		min: 8.06,
		med: 424.42,
		max: 4270, // 4.27s => 4270ms
		p90: 3030, // 3.03s => 3030ms
		p95: 3790, // 3.79s => 3790ms
	},
	// 3) Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 1290, // 1.29s
		min: 10.63,
		med: 808,
		max: 5870, // 5.87s
		p90: 3510, // 3.51s
		p95: 5060, // 5.06s
	},
	// 4) Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1630, // 1.63s
		min: 16.03,
		med: 1420, // 1.42s
		max: 4570, // 4.57s
		p90: 3850, // 3.85s
		p95: 4230, // 4.23s
	},
	// 5) Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1810, // 1.81s
		min: 124.54,
		med: 1670, // 1.67s
		max: 3750, // 3.75s
		p90: 3340, // 3.34s
		p95: 3600, // 3.6s
	},
	// 6) Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 4240, // 4.24s
		min: 1330, // 1.33s
		med: 4160, // 4.16s
		max: 8480, // 8.48s
		p90: 5680, // 5.68s
		p95: 6150, // 6.15s
	},
	// 7) Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2400, // 2.4s
		min: 57.72,
		med: 2310, // 2.31s
		max: 4820, // 4.82s
		p90: 3900, // 3.9s
		p95: 4420, // 4.42s
	},
	// 8) Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1260, // 1.26s
		min: 23.68,
		med: 1040, // 1.04s
		max: 4810, // 4.81s
		p90: 2550, // 2.55s
		p95: 3360, // 3.36s
	},
	// 9) Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 846.14,
		min: 8.69,
		med: 317,
		max: 5240, // 5.24s
		p90: 3290, // 3.29s
		p95: 3550, // 3.55s
	},
	// 10) No Sentry, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 57.32,
		min: 5.75,
		med: 12.03,
		max: 978.64,
		p90: 75.87,
		p95: 331.51,
	},
];

/********************************************************************************************
 * GQL_GetDivisionRoundsPoolBracketsQuery
 ********************************************************************************************/
const GQL_GetDivisionRoundsPoolBracketsQuery = [
	// 1) No Sentry, NodeJS 20, no PM2
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 106.07,
		min: 7.45,
		med: 56.87,
		max: 789.88,
		p90: 238.84,
		p95: 368.94,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 692.67,
		min: 23.33,
		med: 475.49,
		max: 3170, // 3.17s
		p90: 1660, // 1.66s
		p95: 1990, // 1.99s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 1600, // 1.6s => 1600ms
		min: 13.73,
		med: 936.15,
		max: 7740, // 7.74s
		p90: 4790, // 4.79s
		p95: 5940, // 5.94s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1570, // 1.57s
		min: 24.45,
		med: 1270, // 1.27s
		max: 5560, // 5.56s
		p90: 3250, // 3.25s
		p95: 3930, // 3.93s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1760, // 1.76s
		min: 38.61,
		med: 1670, // 1.67s
		max: 6380, // 6.38s
		p90: 3280, // 3.28s
		p95: 3930, // 3.93s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 4700, // 4.7s
		min: 304.73,
		med: 4650, // 4.65s
		max: 10430, // 10.43s
		p90: 6590, // 6.59s
		p95: 7150, // 7.15s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2650, // 2.65s
		min: 18.23,
		med: 2650, // 2.65s
		max: 6630, // 6.63s
		p90: 4500, // 4.5s
		p95: 4940, // 4.94s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1500, // 1.5s
		min: 53.07,
		med: 1340, // 1.34s
		max: 5370, // 5.37s
		p90: 2740, // 2.74s
		p95: 3220, // 3.22s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1320, // 1.32s
		min: 19.89,
		med: 807.39,
		max: 6900, // 6.9s
		p90: 3230, // 3.23s
		p95: 4010, // 4.01s
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 164.22,
		min: 7.11,
		med: 65.18,
		max: 2850, // 2.85s
		p90: 367.31,
		p95: 644.04,
	},
];

/********************************************************************************************
 * GQL_GetDivisionTeamsStandingQuery
 ********************************************************************************************/
const GQL_GetDivisionTeamsStandingQuery = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 87.03,
		min: 5.62,
		med: 28.71,
		max: 658.61,
		p90: 282.62,
		p95: 380.81,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 888.12,
		min: 11.71,
		med: 430.49,
		max: 4530, // 4.53s
		p90: 2130, // 2.13s
		p95: 4160, // 4.16s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 1000, // 1s
		min: 12.97,
		med: 758.1,
		max: 5530, // 5.53s
		p90: 2270, // 2.27s
		p95: 2920, // 2.92s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1170, // 1.17s
		min: 10.57,
		med: 760.26,
		max: 4380, // 4.38s
		p90: 2240, // 2.24s
		p95: 3080, // 3.08s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1620, // 1.62s
		min: 34.55,
		med: 1460, // 1.46s
		max: 4530, // 4.53s
		p90: 2950, // 2.95s
		p95: 3710, // 3.71s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 4110, // 4.11s
		min: 51.27,
		med: 4000, // 4s
		max: 10050, // 10.05s
		p90: 6080, // 6.08s
		p95: 6460, // 6.46s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2410, // 2.41s
		min: 45.6,
		med: 2400, // 2.4s
		max: 5700, // 5.7s
		p90: 3940, // 3.94s
		p95: 4500, // 4.5s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1320, // 1.32s
		min: 13.34,
		med: 1160, // 1.16s
		max: 4700, // 4.7s
		p90: 2600, // 2.6s
		p95: 3690, // 3.69s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 743.97,
		min: 9.89,
		med: 557.63,
		max: 5300, // 5.3s
		p90: 1490,
		p95: 2220,
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 791.59,
		min: 5.48,
		med: 30.3,
		max: 8750, // 8.75s
		p90: 3050, // 3.05s
		p95: 5890, // 5.89s
	},
];

/********************************************************************************************
 * GQL_GetEventCountsQuery
 ********************************************************************************************/
const GQL_GetEventCountsQuery = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 92.8,
		min: 3.75,
		med: 11.43,
		max: 757.55,
		p90: 439.57,
		p95: 656.05,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 442.65,
		min: 7.25,
		med: 230.21,
		max: 3080, // 3.08s
		p90: 1140, // 1.14s
		p95: 1270, // 1.27s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 950.16,
		min: 6.94,
		med: 595.55,
		max: 5670, // 5.67s
		p90: 2250, // 2.25s
		p95: 2660, // 2.66s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1180, // 1.18s
		min: 7.58,
		med: 889.6,
		max: 4290, // 4.29s
		p90: 2750, // 2.75s
		p95: 3050, // 3.05s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1470, // 1.47s
		min: 8.5,
		med: 1390, // 1.39s
		max: 4440, // 4.44s
		p90: 2570, // 2.57s
		p95: 3200, // 3.2s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3880, // 3.88s
		min: 57.98,
		med: 3910, // 3.91s
		max: 7660, // 7.66s
		p90: 5500, // 5.5s
		p95: 5970, // 5.97s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2290, // 2.29s
		min: 22.49,
		med: 2230, // 2.23s
		max: 6330, // 6.33s
		p90: 3640, // 3.64s
		p95: 4500, // 4.5s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1230, // 1.23s
		min: 11.11,
		med: 962.1,
		max: 4900, // 4.9s
		p90: 2540, // 2.54s
		p95: 3010, // 3.01s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 630.66,
		min: 7.43,
		med: 329.5,
		max: 5470, // 5.47s
		p90: 1690,
		p95: 1900,
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 162.64,
		min: 4.43,
		med: 12,
		max: 8200, // 8.2s
		p90: 548.05,
		p95: 925.81,
	},
];

/********************************************************************************************
 * GQL_GetEventDetailsQuery
 ********************************************************************************************/
const GQL_GetEventDetailsQuery = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 156.5,
		min: 3.83,
		med: 15.63,
		max: 2060, // 2.06s
		p90: 433.42,
		p95: 638.82,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 541.27,
		min: 7.8,
		med: 360.93,
		max: 4270, // 4.27s
		p90: 1150, // 1.15s
		p95: 1590, // 1.59s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 1140, // 1.14s
		min: 9.45,
		med: 848.71,
		max: 5300, // 5.3s
		p90: 2310, // 2.31s
		p95: 3750, // 3.75s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1380, // 1.38s
		min: 13.77,
		med: 1190, // 1.19s
		max: 4380, // 4.38s
		p90: 3130, // 3.13s
		p95: 3310, // 3.31s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1560, // 1.56s
		min: 13.6,
		med: 1440, // 1.44s
		max: 4470, // 4.47s
		p90: 2760, // 2.76s
		p95: 3390, // 3.39s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3450, // 3.45s
		min: 46.94,
		med: 3560, // 3.56s
		max: 6770, // 6.77s
		p90: 5270, // 5.27s
		p95: 5600, // 5.6s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2320, // 2.32s
		min: 12.12,
		med: 2170, // 2.17s
		max: 6030, // 6.03s
		p90: 3830, // 3.83s
		p95: 4380, // 4.38s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1550, // 1.55s
		min: 9.92,
		med: 1200, // 1.2s
		max: 4700, // 4.7s
		p90: 3480, // 3.48s
		p95: 3880, // 3.88s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 812.03,
		min: 8.98,
		med: 566.99,
		max: 5840, // 5.84s
		p90: 1660,
		p95: 2310,
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 614.87,
		min: 4.67,
		med: 18.58,
		max: 12610, // 12.61s
		p90: 607.95,
		p95: 3960, // 3.96s
	},
];

/********************************************************************************************
 * GQL_GetPaginatedAthletesQuery
 ********************************************************************************************/
const GQL_GetPaginatedAthletesQuery = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 39.86,
		min: 4.03,
		med: 11.56,
		max: 420.19,
		p90: 124.3,
		p95: 232.74,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 810.47,
		min: 8.48,
		med: 294.09,
		max: 4150, // 4.15s
		p90: 2450, // 2.45s
		p95: 3800, // 3.8s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 999, // 998.78 ~ 999
		min: 8.38,
		med: 588.22,
		max: 5350, // 5.35s
		p90: 2540, // 2.54s
		p95: 3190, // 3.19s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1250, // 1.25s
		min: 23.95,
		med: 1080, // ~1.08s
		max: 4180, // 4.18s
		p90: 2470, // 2.47s
		p95: 3270, // 3.27s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1520, // 1.52s
		min: 9.36,
		med: 1490, // 1.49s
		max: 3790, // 3.79s
		p90: 2810, // 2.81s
		p95: 3190, // 3.19s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 4020, // 4.02s
		min: 36.12,
		med: 3890, // 3.89s
		max: 7210, // 7.21s
		p90: 5920, // 5.92s
		p95: 6430, // 6.43s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2240, // 2.24s
		min: 16.47,
		med: 2090, // 2.09s
		max: 6200, // 6.2s
		p90: 3900, // 3.9s
		p95: 4620, // 4.62s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1150, // 1.15s
		min: 14.55,
		med: 919.81,
		max: 4030, // 4.03s
		p90: 2180, // 2.18s
		p95: 3400, // 3.4s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 607.24,
		min: 7.38,
		med: 322.68,
		max: 5840, // 5.84s
		p90: 1510,
		p95: 2000,
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 206.2,
		min: 4.47,
		med: 9.65,
		max: 5010, // 5.01s
		p90: 129.4,
		p95: 1200, // 1.2s
	},
];

/********************************************************************************************
 * GQL_GetPaginatedAthletesQuery_Search
 ********************************************************************************************/
const GQL_GetPaginatedAthletesQuery_Search = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 36.04,
		min: 6.03,
		med: 21.98,
		max: 342.44,
		p90: 66.94,
		p95: 98.85,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 327.83,
		min: 7.13,
		med: 139.92,
		max: 2620, // 2.62s
		p90: 932.81,
		p95: 1250, // 1.25s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 675.2,
		min: 9.96,
		med: 348.84,
		max: 5380, // 5.38s
		p90: 1860, // 1.86s
		p95: 2510, // 2.51s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 789.14,
		min: 8.75,
		med: 608.08,
		max: 4480, // 4.48s
		p90: 1760, // 1.76s
		p95: 2330, // 2.33s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1170, // 1.17s
		min: 10.58,
		med: 992.8,
		max: 4610, // 4.61s
		p90: 2540, // 2.54s
		p95: 2980, // 2.98s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3680, // 3.68s
		min: 17.86,
		med: 3840, // 3.84s
		max: 9950, // 9.95s
		p90: 5710, // 5.71s
		p95: 6360, // 6.36s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 1830, // 1.83s
		min: 8.57,
		med: 1820, // 1.82s
		max: 6350, // 6.35s
		p90: 3560, // 3.56s
		p95: 3950, // 3.95s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 793.07,
		min: 11.92,
		med: 635.15,
		max: 4750, // 4.75s
		p90: 1710, // 1.71s
		p95: 2180, // 2.18s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 460.54,
		min: 10.35,
		med: 101.71,
		max: 5810, // 5.81s
		p90: 1180,
		p95: 2060,
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 68.22,
		min: 6.27,
		med: 23.54,
		max: 3810, // 3.81s
		p90: 90.72,
		p95: 167.18,
	},
];

/********************************************************************************************
 * GQL_GetPaginatedClubsTeamsQuery
 ********************************************************************************************/
const GQL_GetPaginatedClubsTeamsQuery = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 73.02,
		min: 7.57,
		med: 38.34,
		max: 728.94,
		p90: 157.97,
		p95: 285.82,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 891,
		min: 25.7,
		med: 442.29,
		max: 4800, // 4.8s
		p90: 1870, // 1.87s
		p95: 4070, // 4.07s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 1370, // 1.37s
		min: 25.2,
		med: 1020, // 1.02s
		max: 7540, // 7.54s
		p90: 2810, // 2.81s
		p95: 4800, // 4.8s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1480, // 1.48s
		min: 23.49,
		med: 1390, // 1.39s
		max: 4600, // 4.6s
		p90: 3090, // 3.09s
		p95: 4120, // 4.12s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1790, // 1.79s
		min: 58.71,
		med: 1740, // 1.74s
		max: 5600, // 5.6s
		p90: 3300, // 3.3s
		p95: 3650, // 3.65s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 4430, // 4.43s
		min: 108.8,
		med: 4300, // 4.3s
		max: 9880, // 9.88s
		p90: 5930, // 5.93s
		p95: 6680, // 6.68s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2550, // 2.55s
		min: 50.98,
		med: 2560, // 2.56s
		max: 6410, // 6.41s
		p90: 4170, // 4.17s
		p95: 4570, // 4.57s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1590, // 1.59s
		min: 27.69,
		med: 1400, // 1.4s
		max: 5230, // 5.23s
		p90: 2930, // 2.93s
		p95: 4190, // 4.19s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1180, // 1.18s
		min: 32.62,
		med: 713.52,
		max: 6400, // 6.4s
		p90: 2690, // 2.69s
		p95: 4080, // 4.08s
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 262.16,
		min: 9.32,
		med: 36.67,
		max: 5630, // 5.63s
		p90: 229.59,
		p95: 1690, // 1.69s
	},
];

/********************************************************************************************
 * GQL_GetPaginatedClubsTeamsQuery_Search
 ********************************************************************************************/
const GQL_GetPaginatedClubsTeamsQuery_Search = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 37.05,
		min: 7.17,
		med: 22.52,
		max: 423.53,
		p90: 70.13,
		p95: 109.81,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 394.31,
		min: 9.18,
		med: 191.6,
		max: 4200, // 4.2s
		p90: 1090, // 1.09s
		p95: 1500, // 1.5s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 851.07,
		min: 11.15,
		med: 391.89,
		max: 5790, // 5.79s
		p90: 2170, // 2.17s
		p95: 3390, // 3.39s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 955.83,
		min: 10.52,
		med: 725.96,
		max: 4880, // 4.88s
		p90: 2000, // 2s
		p95: 2830, // 2.83s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1380, // 1.38s
		min: 11.47,
		med: 1230, // 1.23s
		max: 5360, // 5.36s
		p90: 2820, // 2.82s
		p95: 3330, // 3.33s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3720, // 3.72s
		min: 19.69,
		med: 3910, // 3.91s
		max: 10370, // 10.37s
		p90: 5670, // 5.67s
		p95: 6420, // 6.42s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 1920, // 1.92s
		min: 12.52,
		med: 1910, // 1.91s
		max: 6220, // 6.22s
		p90: 3510, // 3.51s
		p95: 4210, // 4.21s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 881.46,
		min: 14.43,
		med: 676.38,
		max: 4610, // 4.61s
		p90: 1980, // 1.98s
		p95: 2370, // 2.37s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 534.06,
		min: 13.57,
		med: 151.55,
		max: 6330, // 6.33s
		p90: 1570, // 1.57s
		p95: 2450, // 2.45s
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 94.27,
		min: 8.43,
		med: 25.64,
		max: 5810, // 5.81s
		p90: 95.26,
		p95: 201.77,
	},
];

/********************************************************************************************
 * GQL_GetPaginatedDivisionTeamsQuery
 ********************************************************************************************/
const GQL_GetPaginatedDivisionTeamsQuery = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 48.97,
		min: 5.73,
		med: 19.44,
		max: 439.75,
		p90: 128.58,
		p95: 249.07,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 689.35,
		min: 9.75,
		med: 340.83,
		max: 4460, // 4.46s
		p90: 1740, // 1.74s
		p95: 2580, // 2.58s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 1260, // 1.26s
		min: 7.92,
		med: 620.89,
		max: 6060, // 6.06s
		p90: 4380, // 4.38s
		p95: 5340, // 5.34s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1260, // 1.26s
		min: 18.45,
		med: 937.41,
		max: 4420, // 4.42s
		p90: 2980, // 2.98s
		p95: 3400, // 3.4s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1580, // 1.58s
		min: 15.67,
		med: 1340, // 1.34s
		max: 5240, // 5.24s
		p90: 3300, // 3.3s
		p95: 3630, // 3.63s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 4210, // 4.21s
		min: 37.48,
		med: 4140, // 4.14s
		max: 9740, // 9.74s
		p90: 6060, // 6.06s
		p95: 6410, // 6.41s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2420, // 2.42s
		min: 29.21,
		med: 2190, // 2.19s
		max: 6540, // 6.54s
		p90: 4250, // 4.25s
		p95: 4630, // 4.63s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1210, // 1.21s
		min: 12.43,
		med: 977.27,
		max: 5170, // 5.17s
		p90: 2250, // 2.25s
		p95: 3830, // 3.83s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 817.2,
		min: 10.88,
		med: 457.48,
		max: 6190, // 6.19s
		p90: 2190, // 2.19s
		p95: 2990, // 2.99s
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 157.79,
		min: 4.98,
		med: 18.95,
		max: 4970, // 4.97s
		p90: 201.47,
		p95: 668, // 667.99 ~ 668
	},
];

/********************************************************************************************
 * GQL_GetPaginatedDivisionTeamsQuery_Search
 ********************************************************************************************/
const GQL_GetPaginatedDivisionTeamsQuery_Search = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 30.65,
		min: 7.49,
		med: 17.38,
		max: 202.73,
		p90: 57.28,
		p95: 116.2,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 375.7,
		min: 10.89,
		med: 193.05,
		max: 1710, // 1.71s
		p90: 1030, // 1.03s
		p95: 1370, // 1.37s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 618,
		min: 8.65,
		med: 356.33,
		max: 3700, // 3.7s
		p90: 1500, // 1.5s
		p95: 2070, // 2.07s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 718, // 717.63 ~ 718
		min: 11,
		med: 573.13,
		max: 2670, // 2.67s
		p90: 1560, // 1.56s
		p95: 1830, // 1.83s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1330, // 1.33s
		min: 12.22,
		med: 1290, // 1.29s
		max: 4600, // 4.6s
		p90: 2300, // 2.3s
		p95: 3010, // 3.01s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3860, // 3.86s
		min: 451.79,
		med: 3930, // 3.93s
		max: 7070, // 7.07s
		p90: 5490, // 5.49s
		p95: 5860, // 5.86s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2110, // 2.11s
		min: 16.24,
		med: 2020, // 2.02s
		max: 5280, // 5.28s
		p90: 4010, // 4.01s
		p95: 4260, // 4.26s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 998.04,
		min: 14.18,
		med: 1040,
		max: 2590, // 2.59s
		p90: 1910, // 1.91s
		p95: 2210, // 2.21s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 656.43,
		min: 13.96,
		med: 291.15,
		max: 5020, // 5.02s
		p90: 2020, // 2.02s
		p95: 2230, // 2.23s
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 35.98,
		min: 6.95,
		med: 18.64,
		max: 419.68,
		p90: 48.88,
		p95: 107.73,
	},
];

/********************************************************************************************
 * GQL_GetPaginatedStaffQuery
 ********************************************************************************************/
const GQL_GetPaginatedStaffQuery = [
	// 1) No Sentry, NodeJS 20
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 33.12,
		min: 3.51,
		med: 10.5,
		max: 342.64,
		p90: 69.79,
		p95: 205.46,
	},
	// 2) Sentry=0, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 657.66,
		min: 7.53,
		med: 251.36,
		max: 3960, // 3.96s
		p90: 1790, // 1.79s
		p95: 2890, // 2.89s
	},
	// 3) Sentry=0.05, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 1010, // ~1.01s
		min: 7.17,
		med: 534.68,
		max: 5320, // 5.32s
		p90: 2470, // 2.47s
		p95: 4590, // 4.59s
	},
	// 4) Sentry=0.2, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1250, // 1.25s
		min: 9.85,
		med: 837.19,
		max: 4180, // 4.18s
		p90: 3220, // 3.22s
		p95: 3520, // 3.52s
	},
	// 5) Sentry=0.5, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1570, // 1.57s
		min: 12.01,
		med: 1360, // 1.36s
		max: 4890, // 4.89s
		p90: 3150, // 3.15s
		p95: 3640, // 3.64s
	},
	// 6) Sentry=1, NodeJS 20
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3960, // 3.96s
		min: 261.67,
		med: 3950, // 3.95s
		max: 9780, // 9.78s
		p90: 5590, // 5.59s
		p95: 5960, // 5.96s
	},
	// 7) Sentry=1, NodeJS 22
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2250, // 2.25s
		min: 73.43,
		med: 1980, // 1.98s
		max: 6020, // 6.02s
		p90: 4320, // 4.32s
		p95: 4770, // 4.77s
	},
	// 8) Sentry=1, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1210, // 1.21s
		min: 18.48,
		med: 902.6,
		max: 4870, // 4.87s
		p90: 3280, // 3.28s
		p95: 3830, // 3.83s
	},
	// 9) Sentry=0.05, NodeJS 22, PM2
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 488.07,
		min: 8.57,
		med: 221.87,
		max: 2530, // 2.53s
		p90: 1510,
		p95: 1960,
	},
	// 10) No Sentry, NodeJS 22, PM2
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 54.83,
		min: 4.35,
		med: 10.76,
		max: 1860, // 1.86s
		p90: 44.53,
		p95: 95.41,
	},
];

// ----------------------------------------------
// GQL_GetPaginatedStaffQuery_Search
// ----------------------------------------------
const GQL_GetPaginatedStaffQuery_Search = [
	// 1) No Sentry, NodeJS 20, no PM2, vus=80
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 25.75,
		min: 6.17,
		med: 19.03,
		max: 287.8,
		p90: 48.83,
		p95: 74.01,
	},
	// 2) Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 273.74,
		min: 7.3,
		med: 99.76,
		max: 2360, // 2.36s -> 2360ms
		p90: 816.45,
		p95: 1120, // 1.12s -> 1120ms
	},
	// 3) Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 509.52,
		min: 8.98,
		med: 203.91,
		max: 5090, // 5.09s -> 5090ms
		p90: 1280, // 1.28s -> 1280ms
		p95: 1970, // 1.97s -> 1970ms
	},
	// 4) Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 723.79,
		min: 8.29,
		med: 539.33,
		max: 4290, // 4.29s -> 4290ms
		p90: 1720, // 1.72s -> 1720ms
		p95: 2260, // 2.26s -> 2260ms
	},
	// 5) Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1130, // 1.13s
		min: 11.18,
		med: 986.76,
		max: 3900, // 3.9s -> 3900ms
		p90: 2340, // 2.34s -> 2340ms
		p95: 2740, // 2.74s -> 2740ms
	},
	// 6) Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3430, // 3.43s
		min: 14.63,
		med: 3510, // 3.51s
		max: 9810, // 9.81s
		p90: 5780, // 5.78s
		p95: 6690, // 6.69s
	},
	// 7) Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 1630, // 1.63s
		min: 8.88,
		med: 1680, // 1.68s
		max: 5790, // 5.79s
		p90: 3090, // 3.09s
		p95: 3650, // 3.65s
	},
	// 8) Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 744.32,
		min: 11.12,
		med: 613.78,
		max: 3520, // 3.52s
		p90: 1660, // 1.66s
		p95: 2020, // 2.02s
	},
	// 9) Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 466.09,
		min: 11.27,
		med: 97.31,
		max: 5780, // 5.78s
		p90: 1050, // 1.05s
		p95: 2010, // 2.01s
	},
	// 10) No Sentry, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 61.33,
		min: 6.32,
		med: 21.6,
		max: 3810, // 3.81s
		p90: 56.92,
		p95: 121.83,
	},
];

// ----------------------------------------------
// http_req_duration
// ----------------------------------------------
const http_req_duration = [
	// 1) No Sentry, NodeJS 20, no PM2, vus=80
	{
		scenario: 'No Sentry, NodeJS 20, no PM2, vus=80',
		avg: 63.26,
		min: 3.66,
		med: 19.88,
		max: 2060, // 2.06s -> 2060ms
		p90: 137.18,
		p95: 332.06,
	},
	// 2) Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0, NodeJS 20, no PM2, vus=80',
		avg: 506.98,
		min: 7.28,
		med: 234.31,
		max: 4800, // 4.8s -> 4800ms
		p90: 1260, // 1.26s -> 1260ms
		p95: 1700, // 1.7s -> 1700ms
	},
	// 3) Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 20, no PM2, vus=80',
		avg: 948.37,
		min: 7.08,
		med: 546.72,
		max: 7740, // 7.74s
		p90: 2320, // 2.32s
		p95: 3580, // 3.58s
	},
	// 4) Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.2, NodeJS 20, no PM2, vus=80',
		avg: 1100,
		min: 7.73,
		med: 812.38,
		max: 5560, // 5.56s
		p90: 2490, // 2.49s
		p95: 3210, // 3.21s
	},
	// 5) Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.5, NodeJS 20, no PM2, vus=80',
		avg: 1430, // 1.43s
		min: 8.62,
		med: 1320, // 1.32s
		max: 6380, // 6.38s
		p90: 2780, // 2.78s
		p95: 3310, // 3.31s
	},
	// 6) Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 20, no PM2, vus=80',
		avg: 3850, // 3.85s
		min: 14.81,
		med: 3930, // 3.93s
		max: 10430, // 10.43s
		p90: 5730, // 5.73s
		p95: 6360, // 6.36s
	},
	// 7) Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, no PM2, vus=80',
		avg: 2140, // 2.14s
		min: 8.72,
		med: 2030, // 2.03s
		max: 6630, // 6.63s
		p90: 3830, // 3.83s
		p95: 4400, // 4.4s
	},
	// 8) Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'Sentry tracesSampleRate=1, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 1110,
		min: 10.06,
		med: 912.17,
		max: 5370, // 5.37s
		p90: 2330, // 2.33s
		p95: 3120, // 3.12s
	},
	// 9) Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'Sentry tracesSampleRate=0.05, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 655.35,
		min: 7.53,
		med: 268.35,
		max: 6900, // 6.9s
		p90: 1830, // 1.83s
		p95: 2470, // 2.47s
	},
	// 10) No Sentry, NodeJS 22, PM2 2 nodes, vus=80
	{
		scenario: 'No Sentry, NodeJS 22, PM2 2 nodes, vus=80',
		avg: 181.17,
		min: 4.47,
		med: 21.86,
		max: 12630, // 12.63s
		p90: 220.58,
		p95: 633.27,
	},
];
